[build]
builder = "NIXPACKS"

[deploy]
startCommand = "cargo run --bin voidbot-bot --release"
healthcheckPath = "/health"
healthcheckTimeout = 300

[environments.production]
DATABASE_URL = "${{Postgres.DATABASE_URL}}"
DISCORD_BOT_TOKEN = "${{DISCORD_BOT_TOKEN}}"
RUST_LOG = "info"
PORT = "${{PORT}}"

[environments.development]
DATABASE_URL = "${{Postgres.DATABASE_URL}}"
DISCORD_BOT_TOKEN = "${{DISCORD_BOT_TOKEN}}" 
RUST_LOG = "debug"
PORT = "3000"