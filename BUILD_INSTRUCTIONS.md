# VoidBot - Instructions de Build Multi-Architecture

Ce guide explique comment compiler VoidBot pour toutes les plateformes supportées.

## 📋 Prérequis

### Système requis
- **OS** : Linux, macOS ou Windows
- **RAM** : 4GB minimum (8GB recommandé)
- **Espace disque** : 5GB pour tous les builds

### Outils requis
- **Rust** 1.70+ avec <PERSON>go
- **Node.js** 18+ avec npm
- **Git**

### Installation automatique des dépendances
```bash
# Clone le repository
git clone https://github.com/VoidBot/VoidBot.git
cd VoidBot

# Installe les dépendances
./scripts/build-all.sh --install-deps
```

## 🚀 Builds Rapides

### Build pour votre plateforme actuelle
```bash
cd voidbot-desktop
npm ci
npm run tauri:build
```

### Build optimisé pour production
```bash
cd voidbot-desktop
npm run tauri:build:prod
```

## 🌍 Builds Multi-Architecture

### Script automatique (recommandé)
```bash
# Build toutes les architectures supportées sur votre OS
./scripts/build-all.sh

# Nettoyer avant de builder
./scripts/build-all.sh --clean
```

### Builds manuels par plateforme

#### Windows (64-bit et 32-bit)
```bash
# 64-bit (MSI + NSIS)
npm run build:windows

# 32-bit (MSI + NSIS)  
npm run build:windows:x86
```

#### macOS (Universal Binary)
```bash
# Support Intel + Apple Silicon
npm run build:macos
```

#### Linux (x64 et ARM64)
```bash
# 64-bit (DEB + AppImage)
npm run build:linux

# ARM64 (DEB + AppImage)
npm run build:linux:arm64
```

## 📦 Formats de sortie

### Windows
- **`.msi`** - Installeur Microsoft Installer (recommandé)
- **`.exe`** - Installeur NSIS avec interface graphique

### macOS
- **`.dmg`** - Image disque macOS avec installeur glisser-déposer

### Linux
- **`.deb`** - Package Debian/Ubuntu
- **`.AppImage`** - Portable Linux (toutes distributions)

## 🔧 Configuration Avancée

### Variables d'environnement

```bash
# Mode de build
export TAURI_BUILD_MODE="production"

# Target spécifique
export TAURI_BUILD_TARGET="x86_64-pc-windows-msvc"

# Architecture spécifique
export TAURI_BUILD_ARCH="x64"

# Optimisations
export CARGO_BUILD_JOBS="4"
export RUSTFLAGS="-C target-cpu=native"
```

### Cross-compilation

#### Depuis Linux vers Windows
```bash
# Installation toolchain Windows
rustup target add x86_64-pc-windows-msvc
sudo apt install mingw-w64

# Build Windows depuis Linux
cargo tauri build --target x86_64-pc-windows-msvc
```

#### Depuis macOS vers Linux
```bash
# Installation toolchain Linux
rustup target add x86_64-unknown-linux-gnu

# Installation cross-compiler
brew install filosottile/musl-cross/musl-cross

# Build Linux depuis macOS
cargo tauri build --target x86_64-unknown-linux-gnu
```

## 🎯 Optimisations de Performance

### Profile de build optimisé
Le fichier `Cargo.toml` inclut des optimisations :
- **LTO** : Link Time Optimization activé
- **Codegen Units** : 1 pour optimisation maximale
- **Strip** : Suppression symboles debug
- **Panic** : Mode abort pour binaires plus petits

### Optimisations Vite (Frontend)
```bash
# Build avec optimisations maximales
npm run build:prod
```

### Tailles de build typiques
- **Windows x64** : ~45MB (MSI), ~35MB (exe)
- **macOS Universal** : ~42MB (DMG)
- **Linux x64** : ~38MB (DEB), ~40MB (AppImage)

## 🔍 Débogage

### Build avec symboles de debug
```bash
# Profile release-with-debug
cargo tauri build --profile release-with-debug
```

### Logs de build détaillés
```bash
# Activer logs Cargo
export CARGO_LOG=cargo::core::compiler::fingerprint=info

# Activer logs Tauri
export TAURI_DEBUG=true

# Build avec logs
npm run tauri:build
```

### Problèmes courants

#### Erreur "Rust target not found"
```bash
# Installer le target manquant
rustup target add [TARGET_NAME]
```

#### Erreur de linking Windows
```bash
# Installer Visual Studio Build Tools
# Ou utiliser rustup-init avec option MSVC
```

#### Erreur WebKit Linux
```bash
# Ubuntu/Debian
sudo apt install libwebkit2gtk-4.0-dev

# Fedora/CentOS
sudo dnf install webkit2gtk3-devel
```

## 📊 GitHub Actions

### Workflow automatique
Le fichier `.github/workflows/build.yml` configure :
- **Build automatique** sur push/tag
- **Matrices de build** pour toutes les plateformes
- **Release automatique** avec artifacts
- **Checksums SHA256** pour vérification

### Déclencher un build
```bash
# Tag pour release
git tag v1.0.0
git push origin v1.0.0

# Build manuel via GitHub UI
# Actions > Build VoidBot Multi-Architecture > Run workflow
```

## 📋 Checklist Release

### Avant le build
- [ ] Version mise à jour dans `package.json`
- [ ] Version mise à jour dans `Cargo.toml`
- [ ] Changelog mis à jour
- [ ] Tests passent
- [ ] Linting sans erreurs

### Après le build
- [ ] Tous les artifacts générés
- [ ] Checksums vérifiés
- [ ] Installation testée sur chaque OS
- [ ] Fonctionnalités principales testées
- [ ] Release notes rédigées

### Distribution
- [ ] Upload sur GitHub Releases
- [ ] Mise à jour site web de téléchargement
- [ ] Notification Discord/Twitter
- [ ] Documentation mise à jour

## 🔐 Signature et Vérification

### Windows (Code Signing)
```bash
# Configuration certificat (optionnel)
export WINDOWS_CERTIFICATE_THUMBPRINT="[YOUR_THUMBPRINT]"
export WINDOWS_TIMESTAMP_URL="http://timestamp.digicert.com"
```

### macOS (App Notarization)
```bash
# Configuration Apple Developer (optionnel)
export APPLE_CERTIFICATE="[CERTIFICATE_NAME]"
export APPLE_ID="[YOUR_APPLE_ID]"
export APPLE_PASSWORD="[APP_SPECIFIC_PASSWORD]"
```

### Checksums de vérification
```bash
# Générer checksums
sha256sum builds/* > checksums.sha256

# Vérifier intégrité
sha256sum -c checksums.sha256
```

## 📞 Support

- **Documentation** : [docs.voidbot.app](https://docs.voidbot.app)
- **Discord** : [discord.gg/voidbot](https://discord.gg/voidbot)
- **Issues** : [GitHub Issues](https://github.com/VoidBot/VoidBot/issues)

---

**Note** : Les builds cross-platform peuvent nécessiter des configurations supplémentaires selon votre environnement. Consultez la documentation Tauri pour plus de détails.