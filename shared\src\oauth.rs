// OAuth Discord avec PKCE (Proof Key for Code Exchange)
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use base64::{Engine as _, engine::general_purpose::URL_SAFE_NO_PAD};
use sha2::{Sha256, Digest};
use rand::{thread_rng, Rng};
use std::collections::HashMap;

/// Configuration OAuth Discord pour VoidBot
#[derive(Debug, Clone)]
pub struct DiscordOAuthConfig {
    pub client_id: String,
    pub client_secret: String, // Optionnel avec PKCE
    pub redirect_uri: String,
    pub scopes: Vec<String>,
}

impl Default for DiscordOAuthConfig {
    fn default() -> Self {
        Self {
            // Utiliser un client ID Discord valide ou la variable d'environnement
            client_id: std::env::var("DISCORD_CLIENT_ID")
                .unwrap_or_else(|_| "1312345678901234567".to_string()),
            client_secret: std::env::var("DISCORD_CLIENT_SECRET")
                .unwrap_or_else(|_| String::new()), // PKCE n'a pas besoin de secret
            redirect_uri: "http://localhost:3000/oauth/callback".to_string(),
            scopes: vec![
                "identify".to_string(),
                "guilds".to_string(),
                "guilds.members.read".to_string(),
                "messages.read".to_string(),
                "applications.commands".to_string(),
            ],
        }
    }
}

/// État OAuth avec PKCE pour sécurité renforcée
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthState {
    pub state: String,
    pub code_verifier: String,
    pub code_challenge: String,
    pub nonce: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl OAuthState {
    /// Génère un nouvel état OAuth avec PKCE
    pub fn new() -> Result<Self> {
        let mut rng = thread_rng();
        
        // Générer le state (32 bytes random)
        let state: String = (0..32)
            .map(|_| rng.gen::<u8>())
            .map(|b| format!("{:02x}", b))
            .collect();
        
        // Générer le code_verifier (43-128 chars, URL-safe)
        let code_verifier: String = (0..64)
            .map(|_| {
                let chars = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
                chars[rng.gen_range(0..chars.len())] as char
            })
            .collect();
        
        // Générer le code_challenge (SHA256 du verifier, base64 URL-safe)
        let mut hasher = Sha256::new();
        hasher.update(code_verifier.as_bytes());
        let challenge_bytes = hasher.finalize();
        let code_challenge = URL_SAFE_NO_PAD.encode(&challenge_bytes);
        
        // Générer un nonce pour prévenir les attaques replay
        let nonce: String = (0..16)
            .map(|_| rng.gen::<u8>())
            .map(|b| format!("{:02x}", b))
            .collect();
        
        Ok(Self {
            state,
            code_verifier,
            code_challenge,
            nonce,
            created_at: chrono::Utc::now(),
        })
    }
    
    /// Vérifie si l'état OAuth est encore valide (10 minutes max)
    pub fn is_valid(&self) -> bool {
        let now = chrono::Utc::now();
        let duration = now.signed_duration_since(self.created_at);
        duration.num_minutes() < 10
    }
}

/// Réponse de l'API Discord pour l'échange de code
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DiscordTokenResponse {
    pub access_token: String,
    pub token_type: String,
    pub expires_in: u64,
    pub refresh_token: String,
    pub scope: String,
}

/// Informations utilisateur Discord via OAuth
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct DiscordOAuthUser {
    pub id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub verified: Option<bool>,
    pub mfa_enabled: Option<bool>,
    pub locale: Option<String>,
    pub flags: Option<u32>,
    pub premium_type: Option<u8>,
}

/// Gestionnaire OAuth Discord
pub struct DiscordOAuth {
    config: DiscordOAuthConfig,
    http_client: reqwest::Client,
}

impl DiscordOAuth {
    /// Crée un nouveau gestionnaire OAuth
    pub fn new(config: DiscordOAuthConfig) -> Result<Self> {
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .user_agent("VoidBot OAuth/1.0")
            .build()?;
        
        Ok(Self {
            config,
            http_client,
        })
    }
    
    /// Crée un nouvel état OAuth avec PKCE
    pub fn create_oauth_state(&self) -> Result<OAuthState> {
        OAuthState::new()
    }
    
    /// Génère l'URL d'autorisation Discord avec PKCE
    pub fn get_authorization_url(&self, oauth_state: &OAuthState) -> String {
        let mut params: HashMap<&str, &str> = HashMap::new();
        params.insert("client_id", &self.config.client_id);
        params.insert("redirect_uri", &self.config.redirect_uri);
        params.insert("response_type", "code");
        let scopes = self.config.scopes.join(" ");
        params.insert("scope", &scopes);
        params.insert("state", &oauth_state.state);
        params.insert("code_challenge", &oauth_state.code_challenge);
        params.insert("code_challenge_method", "S256");
        params.insert("prompt", "consent"); // Force le consentement
        
        let query_string: String = params
            .iter()
            .map(|(k, v)| format!("{}={}", urlencoding::encode(k), urlencoding::encode(v)))
            .collect::<Vec<_>>()
            .join("&");
        
        format!("https://discord.com/api/oauth2/authorize?{}", query_string)
    }
    
    /// Échange le code d'autorisation contre un access token
    pub async fn exchange_code(
        &self,
        code: &str,
        oauth_state: &OAuthState,
    ) -> Result<DiscordTokenResponse> {
        let mut params: HashMap<&str, &str> = HashMap::new();
        params.insert("client_id", &self.config.client_id);
        params.insert("client_secret", &self.config.client_secret);
        params.insert("grant_type", "authorization_code");
        params.insert("code", code);
        params.insert("redirect_uri", &self.config.redirect_uri);
        params.insert("code_verifier", &oauth_state.code_verifier);
        
        let response = self.http_client
            .post("https://discord.com/api/oauth2/token")
            .header("Content-Type", "application/x-www-form-urlencoded")
            .form(&params)
            .send()
            .await?;
        
        if response.status().is_success() {
            let token_response: DiscordTokenResponse = response.json().await?;
            Ok(token_response)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Erreur échange OAuth: {}", error_text))
        }
    }
    
    /// Récupère les informations utilisateur avec l'access token
    pub async fn get_user_info(&self, access_token: &str) -> Result<DiscordOAuthUser> {
        let response = self.http_client
            .get("https://discord.com/api/v10/users/@me")
            .header("Authorization", format!("Bearer {}", access_token))
            .send()
            .await?;
        
        if response.status().is_success() {
            let user_info: DiscordOAuthUser = response.json().await?;
            Ok(user_info)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Erreur récupération utilisateur: {}", error_text))
        }
    }
    
    /// Échange le code d'autorisation contre une session OAuth complète
    pub async fn exchange_code_for_token(
        &self,
        code: &str,
        oauth_state: &OAuthState,
    ) -> Result<OAuthSession> {
        // Échanger le code contre un token
        let token_response = self.exchange_code(code, oauth_state).await?;
        
        // Récupérer les informations utilisateur
        let user_info = self.get_user_info(&token_response.access_token).await?;
        
        // Créer la session OAuth
        let session = OAuthSession::new(user_info, token_response);
        
        Ok(session)
    }
    
    /// Actualise l'access token avec le refresh token
    pub async fn refresh_token(&self, refresh_token: &str) -> Result<DiscordTokenResponse> {
        let mut params: HashMap<&str, &str> = HashMap::new();
        params.insert("client_id", &self.config.client_id);
        params.insert("client_secret", &self.config.client_secret);
        params.insert("grant_type", "refresh_token");
        params.insert("refresh_token", refresh_token);
        
        let response = self.http_client
            .post("https://discord.com/api/oauth2/token")
            .header("Content-Type", "application/x-www-form-urlencoded")
            .form(&params)
            .send()
            .await?;
        
        if response.status().is_success() {
            let token_response: DiscordTokenResponse = response.json().await?;
            Ok(token_response)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Erreur refresh token: {}", error_text))
        }
    }
    
    /// Révoque l'access token (logout sécurisé)
    pub async fn revoke_token(&self, access_token: &str) -> Result<()> {
        let mut params: HashMap<&str, &str> = HashMap::new();
        params.insert("client_id", &self.config.client_id);
        params.insert("client_secret", &self.config.client_secret);
        params.insert("token", access_token);
        
        let response = self.http_client
            .post("https://discord.com/api/oauth2/token/revoke")
            .header("Content-Type", "application/x-www-form-urlencoded")
            .form(&params)
            .send()
            .await?;
        
        if response.status().is_success() {
            Ok(())
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Erreur révocation token: {}", error_text))
        }
    }
}

/// Gestionnaire de sessions OAuth avec stockage sécurisé
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthSession {
    pub user_info: DiscordOAuthUser,
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl OAuthSession {
    /// Crée une nouvelle session OAuth
    pub fn new(
        user_info: DiscordOAuthUser,
        token_response: DiscordTokenResponse,
    ) -> Self {
        let now = chrono::Utc::now();
        let expires_at = now + chrono::Duration::seconds(token_response.expires_in as i64);
        
        Self {
            user_info,
            access_token: token_response.access_token,
            refresh_token: Some(token_response.refresh_token),
            expires_at: Some(expires_at),
            created_at: now,
        }
    }
    
    /// Vérifie si la session est encore valide
    pub fn is_valid(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            chrono::Utc::now() < expires_at
        } else {
            false
        }
    }
    
    /// Vérifie si la session expire bientôt (dans les 5 prochaines minutes)
    pub fn needs_refresh(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            let now = chrono::Utc::now();
            let time_until_expiry = expires_at.signed_duration_since(now);
            time_until_expiry.num_minutes() < 5
        } else {
            true
        }
    }
    
    /// Met à jour la session avec un nouveau token
    pub fn update_token(&mut self, token_response: DiscordTokenResponse) {
        self.access_token = token_response.access_token;
        self.refresh_token = Some(token_response.refresh_token);
        self.expires_at = Some(chrono::Utc::now() + chrono::Duration::seconds(token_response.expires_in as i64));
    }
}