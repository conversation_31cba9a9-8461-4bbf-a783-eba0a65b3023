import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';

// Configuration optimisée pour production Railway
export default defineConfig({
  integrations: [tailwind()],
  
  // Configuration de build optimisée
  build: {
    inlineStylesheets: 'auto',
    assets: '_assets'
  },
  
  // Configuration serveur pour Railway
  server: {
    host: true,
    port: 4321
  },
  
  // Optimisations SEO et performance
  compressHTML: true,
  
  // Configuration de sortie pour site statique
  output: 'static',
  
  // Support multi-site si nécessaire
  site: 'https://voidbot.app',
  
  // Optimisations Vite pour production
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['astro']
          }
        }
      },
      // Optimisations assets
      assetsInlineLimit: 4096,
      cssCodeSplit: true,
      sourcemap: false,
      minify: 'esbuild'
    },
    optimizeDeps: {
      include: ['@astrojs/tailwind']
    }
  }
});