use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Gestionnaire de surveillance d'activité utilisateurs
#[derive(Debug, Clone)]
pub struct ActivityViewer {
    pub config: ActivityViewerConfig,
    pub tracked_users: HashMap<String, TrackedUser>,
    pub activity_history: Vec<ActivityEvent>,
    pub stats: ActivityStats,
}

/// Configuration de l'Activity Viewer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityViewerConfig {
    pub enabled: bool,
    pub max_tracked_users: usize,
    pub max_history_entries: usize,
    pub track_status_changes: bool,
    pub track_voice_activity: bool,
    pub track_game_activity: bool,
    pub track_typing: bool,
    pub track_presence: bool,
    pub auto_remove_inactive_days: Option<u32>,
    pub notification_on_activity: bool,
    pub stealth_mode: bool, // Masquer notre surveillance
}

impl Default for ActivityViewerConfig {
    fn default() -> Self {
        Self {
            enabled: false, // Désactivé par défaut pour la sécurité
            max_tracked_users: 50,
            max_history_entries: 1000,
            track_status_changes: true,
            track_voice_activity: true,
            track_game_activity: true,
            track_typing: true,
            track_presence: true,
            auto_remove_inactive_days: Some(30),
            notification_on_activity: false,
            stealth_mode: true,
        }
    }
}

/// Utilisateur surveillé
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrackedUser {
    pub user_id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub added_at: DateTime<Utc>,
    pub last_seen: DateTime<Utc>,
    pub current_status: UserStatus,
    pub current_activity: Option<UserActivity>,
    pub voice_state: Option<VoiceState>,
    pub guilds_shared: Vec<String>,
    pub notes: Option<String>,
    pub alerts_enabled: bool,
}

/// Statut utilisateur
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum UserStatus {
    Online,
    Idle,
    DoNotDisturb,
    Invisible,
    Offline,
}

/// Activité utilisateur
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct UserActivity {
    pub activity_type: ActivityType,
    pub name: String,
    pub details: Option<String>,
    pub state: Option<String>,
    pub timestamps: Option<ActivityTimestamps>,
    pub assets: Option<ActivityAssets>,
    pub party: Option<ActivityParty>,
    pub buttons: Vec<String>,
}

/// Type d'activité
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ActivityType {
    Playing,
    Streaming,
    Listening,
    Watching,
    Custom,
    Competing,
}

/// Timestamps d'activité
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ActivityTimestamps {
    pub start: Option<DateTime<Utc>>,
    pub end: Option<DateTime<Utc>>,
}

/// Assets d'activité (images, etc.)
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ActivityAssets {
    pub large_image: Option<String>,
    pub large_text: Option<String>,
    pub small_image: Option<String>,
    pub small_text: Option<String>,
}

/// Informations de party
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ActivityParty {
    pub id: Option<String>,
    pub size: Option<(u32, u32)>, // (current, max)
}

/// État vocal
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VoiceState {
    pub guild_id: Option<String>,
    pub channel_id: Option<String>,
    pub self_mute: bool,
    pub self_deaf: bool,
    pub server_mute: bool,
    pub server_deaf: bool,
    pub streaming: bool,
    pub video: bool,
}

/// Événement d'activité
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityEvent {
    pub user_id: String,
    pub event_type: ActivityEventType,
    pub timestamp: DateTime<Utc>,
    pub guild_id: Option<String>,
    pub channel_id: Option<String>,
    pub data: ActivityEventData,
}

/// Type d'événement d'activité
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ActivityEventType {
    StatusChange,
    ActivityStart,
    ActivityUpdate,
    ActivityEnd,
    VoiceJoin,
    VoiceMove,
    VoiceLeave,
    TypingStart,
    TypingStop,
    PresenceUpdate,
    UserUpdate,
}

/// Données d'événement
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ActivityEventData {
    StatusChange {
        old_status: UserStatus,
        new_status: UserStatus,
    },
    ActivityChange {
        old_activity: Option<UserActivity>,
        new_activity: Option<UserActivity>,
    },
    VoiceChange {
        old_state: Option<VoiceState>,
        new_state: Option<VoiceState>,
    },
    TypingEvent {
        channel_id: String,
        guild_id: Option<String>,
    },
    PresenceUpdate {
        username: Option<String>,
        discriminator: Option<String>,
        avatar: Option<String>,
    },
    UserUpdate {
        old_username: Option<String>,
        new_username: Option<String>,
        old_discriminator: Option<String>,
        new_discriminator: Option<String>,
        old_avatar: Option<String>,
        new_avatar: Option<String>,
    },
}

/// Statistiques d'activité
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityStats {
    pub total_tracked_users: usize,
    pub total_events: usize,
    pub events_today: usize,
    pub most_active_user: Option<String>,
    pub most_common_activity: Option<String>,
    pub average_online_time: f64, // En heures
    pub last_updated: DateTime<Utc>,
}

impl Default for ActivityStats {
    fn default() -> Self {
        Self {
            total_tracked_users: 0,
            total_events: 0,
            events_today: 0,
            most_active_user: None,
            most_common_activity: None,
            average_online_time: 0.0,
            last_updated: Utc::now(),
        }
    }
}

impl ActivityViewer {
    pub fn new() -> Self {
        Self {
            config: ActivityViewerConfig::default(),
            tracked_users: HashMap::new(),
            activity_history: Vec::new(),
            stats: ActivityStats::default(),
        }
    }
    
    /// Ajouter un utilisateur à surveiller
    pub fn add_tracked_user(&mut self, user_id: String, username: String, discriminator: String, avatar: Option<String>) -> Result<(), String> {
        if self.tracked_users.len() >= self.config.max_tracked_users {
            return Err(format!("Limite d'utilisateurs surveillés atteinte ({})", self.config.max_tracked_users));
        }
        
        if self.tracked_users.contains_key(&user_id) {
            return Err("Utilisateur déjà surveillé".to_string());
        }
        
        let tracked_user = TrackedUser {
            user_id: user_id.clone(),
            username,
            discriminator,
            avatar,
            added_at: Utc::now(),
            last_seen: Utc::now(),
            current_status: UserStatus::Offline,
            current_activity: None,
            voice_state: None,
            guilds_shared: Vec::new(),
            notes: None,
            alerts_enabled: false,
        };
        
        self.tracked_users.insert(user_id, tracked_user);
        self.update_stats();
        
        Ok(())
    }
    
    /// Supprimer un utilisateur surveillé
    pub fn remove_tracked_user(&mut self, user_id: &str) -> Result<(), String> {
        if self.tracked_users.remove(user_id).is_none() {
            return Err("Utilisateur non trouvé".to_string());
        }
        
        // Supprimer l'historique de cet utilisateur
        self.activity_history.retain(|event| event.user_id != user_id);
        self.update_stats();
        
        Ok(())
    }
    
    /// Enregistrer un événement d'activité
    pub fn record_activity_event(&mut self, event: ActivityEvent) {
        if !self.config.enabled {
            return;
        }
        
        // Mettre à jour last_seen si utilisateur surveillé
        if let Some(user) = self.tracked_users.get_mut(&event.user_id) {
            user.last_seen = event.timestamp;
            
            // Mettre à jour l'état selon le type d'événement
            match &event.event_type {
                ActivityEventType::StatusChange => {
                    if let ActivityEventData::StatusChange { new_status, .. } = &event.data {
                        user.current_status = new_status.clone();
                    }
                },
                ActivityEventType::ActivityStart | ActivityEventType::ActivityUpdate => {
                    if let ActivityEventData::ActivityChange { new_activity, .. } = &event.data {
                        user.current_activity = new_activity.clone();
                    }
                },
                ActivityEventType::ActivityEnd => {
                    user.current_activity = None;
                },
                ActivityEventType::VoiceJoin | ActivityEventType::VoiceMove => {
                    if let ActivityEventData::VoiceChange { new_state, .. } = &event.data {
                        user.voice_state = new_state.clone();
                    }
                },
                ActivityEventType::VoiceLeave => {
                    user.voice_state = None;
                },
                _ => {}
            }
        }
        
        // Ajouter à l'historique
        self.activity_history.push(event);
        
        // Limiter la taille de l'historique
        if self.activity_history.len() > self.config.max_history_entries {
            self.activity_history.remove(0);
        }
        
        self.update_stats();
    }
    
    /// Obtenir l'historique d'activité d'un utilisateur
    pub fn get_user_activity_history(&self, user_id: &str, limit: Option<usize>) -> Vec<&ActivityEvent> {
        let mut events: Vec<&ActivityEvent> = self.activity_history
            .iter()
            .filter(|event| event.user_id == user_id)
            .collect();
        
        // Trier par timestamp décroissant (plus récent en premier)
        events.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        
        if let Some(limit) = limit {
            events.truncate(limit);
        }
        
        events
    }
    
    /// Obtenir les utilisateurs actuellement en ligne
    pub fn get_online_users(&self) -> Vec<&TrackedUser> {
        self.tracked_users
            .values()
            .filter(|user| matches!(user.current_status, UserStatus::Online | UserStatus::Idle | UserStatus::DoNotDisturb))
            .collect()
    }
    
    /// Obtenir les utilisateurs avec une activité
    pub fn get_users_with_activity(&self) -> Vec<&TrackedUser> {
        self.tracked_users
            .values()
            .filter(|user| user.current_activity.is_some())
            .collect()
    }
    
    /// Nettoyer les utilisateurs inactifs
    pub fn cleanup_inactive_users(&mut self) {
        if let Some(days) = self.config.auto_remove_inactive_days {
            let cutoff = Utc::now() - chrono::Duration::days(days as i64);
            
            let inactive_users: Vec<String> = self.tracked_users
                .iter()
                .filter(|(_, user)| user.last_seen < cutoff)
                .map(|(id, _)| id.clone())
                .collect();
            
            for user_id in inactive_users {
                let _ = self.remove_tracked_user(&user_id);
            }
        }
    }
    
    /// Mettre à jour les statistiques
    fn update_stats(&mut self) {
        self.stats.total_tracked_users = self.tracked_users.len();
        self.stats.total_events = self.activity_history.len();
        
        // Compter les événements d'aujourd'hui
        let today = Utc::now().date_naive();
        self.stats.events_today = self.activity_history
            .iter()
            .filter(|event| event.timestamp.date_naive() == today)
            .count();
        
        // Trouver l'utilisateur le plus actif
        let mut user_event_count: HashMap<String, usize> = HashMap::new();
        for event in &self.activity_history {
            *user_event_count.entry(event.user_id.clone()).or_insert(0) += 1;
        }
        
        self.stats.most_active_user = user_event_count
            .iter()
            .max_by_key(|(_, count)| *count)
            .map(|(user_id, _)| user_id.clone());
        
        self.stats.last_updated = Utc::now();
    }
    
    /// Exporter les données d'activité
    pub fn export_activity_data(&self, user_id: Option<&str>) -> Result<String, String> {
        let events = if let Some(user_id) = user_id {
            self.get_user_activity_history(user_id, None)
        } else {
            self.activity_history.iter().collect()
        };
        
        serde_json::to_string_pretty(&events)
            .map_err(|e| format!("Erreur de sérialisation: {}", e))
    }
}

/// Helper pour créer un événement de changement de statut
pub fn create_status_change_event(
    user_id: String,
    old_status: UserStatus,
    new_status: UserStatus,
    guild_id: Option<String>,
) -> ActivityEvent {
    ActivityEvent {
        user_id,
        event_type: ActivityEventType::StatusChange,
        timestamp: Utc::now(),
        guild_id,
        channel_id: None,
        data: ActivityEventData::StatusChange { old_status, new_status },
    }
}

/// Helper pour créer un événement de changement d'activité
pub fn create_activity_change_event(
    user_id: String,
    old_activity: Option<UserActivity>,
    new_activity: Option<UserActivity>,
    guild_id: Option<String>,
) -> ActivityEvent {
    let event_type = match (&old_activity, &new_activity) {
        (None, Some(_)) => ActivityEventType::ActivityStart,
        (Some(_), None) => ActivityEventType::ActivityEnd,
        (Some(_), Some(_)) => ActivityEventType::ActivityUpdate,
        (None, None) => ActivityEventType::ActivityUpdate, // Ne devrait pas arriver
    };
    
    ActivityEvent {
        user_id,
        event_type,
        timestamp: Utc::now(),
        guild_id,
        channel_id: None,
        data: ActivityEventData::ActivityChange { old_activity, new_activity },
    }
}