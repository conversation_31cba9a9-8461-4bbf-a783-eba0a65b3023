use anyhow::Result;
use serenity::{
    model::application::CommandInteraction,
    prelude::*,
};
use crate::{BotData, stealth::StealthManager};

pub async fn handle_rpc(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let preset = command
        .data
        .options
        .iter()
        .find(|opt| opt.name == "preset")
        .and_then(|opt| opt.value.as_str());

    let content = match preset {
        Some(preset_name) => {
            format!("🎮 **Rich Presence Updated**\n\nPreset '{}' has been applied to your Rich Presence.\n\n*This feature is currently in development.*", preset_name)
        }
        None => {
            "🎮 **Rich Presence Configuration**\n\nUse the desktop app to configure your Rich Presence settings.\n\nAvailable presets:\n• Gaming\n• Coding\n• Music\n• Custom\n\n*This feature is currently in development.*".to_string()
        }
    };

    stealth.send_response(ctx, command, content, false).await?;
    Ok(())
}

pub async fn handle_rpc_presets(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let content = r#"
🎮 **Available Rich Presence Presets**

**🎯 Gaming**
• Playing various games
• Competitive status
• Game-specific assets

**💻 Coding**
• Visual Studio Code
• Various programming languages
• GitHub integration

**🎵 Music**
• Spotify integration
• SoundCloud support
• Custom music status

**🎨 Custom**
• Create your own preset
• Custom images and text
• Unlimited possibilities

Use `/rpc <preset_name>` to apply a preset.
*This feature is currently in development.*
    "#;

    stealth.send_response(ctx, command, content, false).await?;
    Ok(())
}