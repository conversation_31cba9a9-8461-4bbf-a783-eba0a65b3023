# 🚀 Guide de Déploiement VoidBot sur Railway

## 📋 Vue d'ensemble

Ce guide détaille le déploiement du site vitrine VoidBot sur Railway avec configuration optimisée pour la production.

---

## ⚡ Déploiement rapide

### 1. **Connexion Repository**
```bash
# Sur Railway (https://railway.app)
1. Create New Project
2. Deploy from GitHub repo
3. Sélectionner : VoidBot/VoidBot
4. Branch : main
5. Root Directory : /website
```

### 2. **Configuration automatique**
Railway détectera automatiquement :
- ✅ `package.json` - Projet Node.js/Astro
- ✅ `railway.toml` - Configuration de build
- ✅ Build command : `npm ci && npm run build`
- ✅ Start command : `npm run start`

### 3. **Variables d'environnement**
```bash
NODE_ENV=production
ASTRO_TELEMETRY_DISABLED=1
PORT=4321
```

---

## 🔧 Configuration détaillée

### **Railway Configuration (`railway.toml`)**
```toml
[build]
builder = "NIXPACKS"
buildCommand = "npm ci && npm run build"

[deploy]
startCommand = "npm run start"
healthcheckPath = "/"
healthcheckTimeout = 300
```

### **Package.json optimisé**
```json
{
  "scripts": {
    "build": "astro check && astro build",
    "start": "astro preview --host 0.0.0.0 --port 4321",
    "preview": "astro preview --host 0.0.0.0 --port 4321"
  }
}
```

### **Astro config production**
```javascript
export default defineConfig({
  output: 'static',
  site: 'https://voidbot.app',
  server: { host: true, port: 4321 },
  compressHTML: true
});
```

---

## 🌐 Configuration domaine

### **Domaine personnalisé**
1. **Railway Dashboard** → Settings → Domains
2. **Add Custom Domain** : `voidbot.app`
3. **DNS Configuration** :
   ```
   Type: CNAME
   Name: @
   Value: [railway-generated-url]
   ```

### **SSL automatique**
Railway configure automatiquement :
- ✅ Certificat Let's Encrypt
- ✅ HTTPS forcé
- ✅ Redirection HTTP → HTTPS

---

## ⚡ Optimisations performance

### **Headers de sécurité**
```nginx
X-Frame-Options: SAMEORIGIN
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff
Content-Security-Policy: default-src 'self'
```

### **Cache optimisé**
```nginx
# Assets statiques : 1 an
/.jpg|.png|.css|.js/ → Cache-Control: max-age=31536000

# Pages HTML : no-cache  
/.html/ → Cache-Control: no-cache, must-revalidate
```

### **Compression**
- ✅ Gzip activé automatiquement
- ✅ Minification CSS/JS
- ✅ Images optimisées

---

## 📊 Monitoring

### **Railway built-in**
- ✅ **Uptime monitoring** - 99.9% disponibilité
- ✅ **Deploy logs** - Historique des déploiements
- ✅ **Metrics** - CPU, mémoire, trafic
- ✅ **Alertes** - Notifications en cas de problème

### **Analytics externes (optionnel)**
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>

<!-- Plausible Analytics -->
<script defer data-domain="voidbot.app" src="https://plausible.io/js/script.js"></script>
```

---

## 🔄 Déploiement automatique

### **Git Push Deploy**
```bash
# Déploiement automatique sur push
git add .
git commit -m "Update site vitrine"
git push origin main
# → Railway rebuild automatiquement
```

### **Preview deployments**
```bash
# Branch feature/new-page
git checkout -b feature/new-page
git push origin feature/new-page
# → Railway crée un preview URL
```

---

## 🛠️ Commandes utiles

### **Développement local**
```bash
cd website/
npm install
npm run dev          # Dev server local
npm run build        # Test build
npm run preview      # Test production locale
```

### **Debug Railway**
```bash
# Railway CLI
npm install -g @railway/cli
railway login
railway status
railway logs
railway shell
```

### **Test production**
```bash
# Test build local
npm run build
npm run start
# → http://localhost:4321

# Test tous les liens
curl -I https://voidbot.app/
curl -I https://voidbot.app/download
curl -I https://voidbot.app/docs
```

---

## 🔍 Résolution de problèmes

### **Build fails**
```bash
# Vérifier les dépendances
npm ci
npm run check

# Vérifier la config Astro
npm run build 2>&1 | grep -i error
```

### **Site inaccessible**
```bash
# Vérifier le déploiement
railway logs --follow

# Vérifier la santé
curl https://voidbot.app/health
```

### **Assets manquants**
```bash
# Vérifier les chemins
ls -la public/
ls -la dist/

# Vérifier la config Nginx
docker exec -it container nginx -t
```

---

## 📈 Performance attendue

### **Métriques cibles**
- **Time to First Byte** : < 200ms
- **First Contentful Paint** : < 1.5s
- **Largest Contentful Paint** : < 2.5s
- **Cumulative Layout Shift** : < 0.1

### **Lighthouse Score**
- ✅ **Performance** : 95+ /100
- ✅ **Accessibility** : 100/100  
- ✅ **Best Practices** : 100/100
- ✅ **SEO** : 100/100

---

## 🎯 Checklist déploiement

### **Pré-déploiement**
- [ ] Build local réussi (`npm run build`)
- [ ] Tous les liens testés
- [ ] Assets complets (favicon, OpenGraph)
- [ ] Configuration Railway validée

### **Déploiement**
- [ ] Repository connecté à Railway
- [ ] Variables d'environnement configurées
- [ ] Premier deploy réussi
- [ ] URL publique accessible

### **Post-déploiement**
- [ ] Domaine personnalisé configuré
- [ ] SSL/HTTPS actif
- [ ] Tests de navigation complets
- [ ] Monitoring activé

### **Optimisations**
- [ ] Performance Lighthouse > 95
- [ ] SEO optimisé (sitemap, meta tags)
- [ ] Analytics configuré (optionnel)
- [ ] CDN configuré (optionnel)

---

## 📞 Support

### **Resources Railway**
- **Documentation** : https://docs.railway.app
- **Discord** : https://discord.gg/railway
- **Status** : https://status.railway.app

### **VoidBot**
- **Repository** : https://github.com/VoidBot/VoidBot
- **Issues** : GitHub Issues pour les bugs
- **Documentation** : /docs sur le site déployé

---

**Status** : ✅ **Configuration prête pour déploiement**  
**URL finale** : https://voidbot.app  
**Temps de déploiement** : 3-5 minutes