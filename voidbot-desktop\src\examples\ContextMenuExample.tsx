import React from 'react';
import { ContextMenu } from '../components/ContextMenu';
import { useContextMenu } from '../hooks/useContextMenu';
import { useNotifications, NotificationToast } from '../components/NotificationToast';

interface MessageProps {
  id: string;
  content: string;
  author: string;
  authorId: string;
  timestamp: string;
  channelId: string;
  guildId?: string;
}

const MessageComponent: React.FC<MessageProps> = ({ 
  id, 
  content, 
  author, 
  authorId, 
  timestamp, 
  channelId, 
  guildId 
}) => {
  const { contextMenu, showContextMenu, hideContextMenu, contextMenuRef } = useContextMenu();
  const { notifications, removeNotification } = useNotifications();

  const handleRightClick = (e: React.MouseEvent) => {
    const messageData = {
      id,
      content,
      authorId,
      timestamp,
      channelId,
      guildId
    };
    
    showContextMenu(e, messageData);
  };

  return (
    <div className="relative">
      {/* Message Discord simulé */}
      <div 
        className="p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors cursor-pointer"
        onContextMenu={handleRightClick}
      >
        <div className="flex items-center gap-2 mb-1">
          <span className="font-semibold text-white">{author}</span>
          <span className="text-xs text-gray-400">{timestamp}</span>
        </div>
        <div className="text-gray-200">{content}</div>
      </div>

      {/* Menu contextuel */}
      {contextMenu.isVisible && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          onClose={hideContextMenu}
          messageData={contextMenu.data}
        />
      )}

      {/* Notifications */}
      {notifications.map((notification) => (
        <NotificationToast
          key={notification.id}
          message={notification.message}
          type={notification.type}
          duration={notification.duration}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
};

// Exemple d'utilisation dans votre application
export const ContextMenuExample: React.FC = () => {
  const sampleMessages = [
    {
      id: '1234567890',
      content: 'Salut ! Comment ça va ? 😊 J\'ai trouvé un super emoji <:voidbot:123456789>',
      author: 'John Doe',
      authorId: '987654321',
      timestamp: '14:30',
      channelId: '555666777',
      guildId: '111222333'
    },
    {
      id: '1234567891',
      content: 'Hello everyone! This is a test message with some emojis 🎉🔥',
      author: 'Jane Smith',
      authorId: '987654322',
      timestamp: '14:32',
      channelId: '555666777',
      guildId: '111222333'
    }
  ];

  return (
    <div className="p-4 space-y-4 bg-gray-900 min-h-screen">
      <h1 className="text-2xl font-bold text-white mb-4">
        VoidBot - Menu Contextuel Intégré
      </h1>
      
      <div className="bg-yellow-800 p-4 rounded-lg mb-4">
        <p className="text-yellow-200">
          <strong>Instructions:</strong> Faites un clic droit sur les messages ci-dessous pour voir le menu contextuel.
          Vous pouvez copier les emojis, traduire le texte, ou accéder à d'autres fonctions utiles.
        </p>
      </div>

      <div className="space-y-3">
        {sampleMessages.map((message) => (
          <MessageComponent
            key={message.id}
            {...message}
          />
        ))}
      </div>

      <div className="bg-green-800 p-4 rounded-lg mt-8">
        <h2 className="text-green-200 font-semibold mb-2">Fonctionnalités disponibles :</h2>
        <ul className="text-green-200 space-y-1">
          <li>✅ Copier les emojis du message</li>
          <li>✅ Traduire le texte</li>
          <li>✅ Copier le contenu du message</li>
          <li>✅ Copier l'ID du message</li>
          <li>✅ Copier le lien du message</li>
          <li>✅ Copier l'ID de l'utilisateur</li>
          <li>✅ Voir la date de création du compte</li>
        </ul>
      </div>
    </div>
  );
};
