import { invoke } from '@tauri-apps/api/core';
import { Minus, Square, X } from 'lucide-react';

export function Titlebar() {
  const handleMinimize = () => invoke('minimize_window');
  const handleMaximize = () => invoke('maximize_window');
  const handleClose = () => invoke('close_window');

  return (
    <div className="titlebar">
      <div className="titlebar-title">
        VoidBot
      </div>
      
      <div className="titlebar-buttons">
        <button 
          className="titlebar-button"
          onClick={handleMinimize}
          title="Minimize"
        >
          <Minus size={14} />
        </button>
        
        <button 
          className="titlebar-button"
          onClick={handleMaximize}
          title="Maximize"
        >
          <Square size={12} />
        </button>
        
        <button 
          className="titlebar-button close"
          onClick={handleClose}
          title="Close"
        >
          <X size={14} />
        </button>
      </div>
    </div>
  );
}