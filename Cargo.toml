[workspace]
members = [
    "voidbot-desktop/src-tauri",
    "discord-bot",
    "shared",
    "backend-api"
]
resolver = "2"

[workspace.package]
version = "1.0.0"
edition = "2021"
authors = ["VoidBot Team"]
license = "MIT"

[workspace.dependencies]
# Async runtime - VERSION ÉPINGLÉE
tokio = { version = "=1.38.1", features = ["full"] }
# Serialization - VERSIONS ÉPINGLÉES
serde = { version = "=1.0.210", features = ["derive"] }
serde_json = "=1.0.128"
# Error handling - VERSIONS ÉPINGLÉES
anyhow = "=1.0.89"
thiserror = "=1.0.63"
# Logging - VERSIONS ÉPINGLÉES
tracing = "=0.1.40"
tracing-subscriber = { version = "=0.3.18", features = ["env-filter"] }
# Database - VERSION ÉPINGLÉE
sqlx = { version = "=0.7.4", features = ["runtime-tokio", "sqlite", "postgres", "json", "chrono"] }
# HTTP - VERSIONS ÉPINGLÉES
reqwest = { version = "=0.12.8", features = ["json"] }
axum = "=0.7.7"
# Utils - VERSIONS ÉPINGLÉES
chrono = { version = "=0.4.38", features = ["serde"] }
uuid = { version = "=1.9.1", features = ["v4", "serde"] }