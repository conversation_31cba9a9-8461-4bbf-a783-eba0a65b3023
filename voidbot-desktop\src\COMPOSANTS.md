# 📚 Documentation des Composants VoidBot

Cette documentation détaille l'architecture et l'utilisation des composants React de VoidBot.

## 🏗️ Architecture Générale

### Structure des Dossiers
```
src/
├── components/           # Composants principaux
│   ├── pages/           # Pages de l'application
│   ├── ThemeBuilder.tsx # Constructeur de thèmes
│   ├── AutoCommands.tsx # Gestion auto-commandes
│   └── ...
├── hooks/               # Hooks personnalisés
├── stores/              # Gestion d'état Zustand
├── types/               # Définitions TypeScript
└── styles/              # Styles globaux
```

### Technologies Utilisées
- **React 18** avec hooks et TypeScript
- **Zustand** pour la gestion d'état globale
- **Framer Motion** pour les animations
- **Tailwind CSS** pour le styling
- **Tauri v2** pour l'API backend

## 🎨 Système de Thèmes

### ThemeBuilder Component

**Fichier**: `src/components/ThemeBuilder.tsx`

**Description**: Interface complète pour créer et personnaliser les thèmes VoidBot.

#### Fonctionnalités Principales
- ✅ Sélection de thèmes prédéfinis (Cyberpunk, Discord, Nighty, etc.)
- ✅ Éditeur de couleurs avec validation temps réel
- ✅ Configuration des gradients et ombres CSS
- ✅ Aperçu instantané des modifications
- ✅ Export/Import de thèmes personnalisés
- ✅ Sauvegarde et application des thèmes

#### Interfaces TypeScript
```typescript
interface ThemeConfig {
  name: string;
  colors: { /* 11 couleurs principales */ };
  gradients: { /* 4 dégradés CSS */ };
  shadows: { /* 4 types d'ombres */ };
  borderRadius: { /* 3 tailles */ };
  animations: { /* Paramètres de transition */ };
  isDefault: boolean;
  createdAt: string;
  lastModified: string;
}
```

#### Utilisation
```tsx
import { ThemeBuilder } from './components/ThemeBuilder';

function App() {
  return <ThemeBuilder />;
}
```

#### API Tauri Utilisée
- `get_custom_themes()` - Récupération thèmes personnalisés
- `save_theme(theme)` - Sauvegarde d'un thème
- `apply_theme(theme)` - Application d'un thème
- `export_theme(theme)` - Export au format JSON
- `import_theme(data)` - Import depuis JSON

## 🤖 Auto-Commandes

### AutoCommands Component

**Fichier**: `src/components/AutoCommands.tsx`

**Description**: Gestion des commandes automatiques Discord (traduction, slash, réponses).

#### Fonctionnalités
- **Auto-Translate**: Traduction automatique des messages
- **Auto-Slash**: Exécution automatique de commandes slash
- **Auto-Reply**: Réponses automatiques aux messages

#### Interfaces TypeScript
```typescript
interface AutoTranslateConfig {
  enabled: boolean;
  service: 'google' | 'deepl' | 'bing';
  target_language: string;
  ignore_channels: string[];
  ignore_users: string[];
  min_length: number;
  similarity_threshold: number;
}

interface SlashTrigger {
  trigger: string;
  command: string;
  delay_ms: number;
  cooldown_seconds: number;
  enabled: boolean;
  channels: string[];
  exact_match: boolean;
  case_sensitive: boolean;
}
```

#### API Tauri Utilisée
- `get_auto_translate_config()` - Configuration traduction
- `update_auto_translate_config(config)` - Mise à jour traduction
- `get_auto_slash_config()` - Configuration auto-slash
- `update_auto_slash_config(config)` - Mise à jour auto-slash
- `clear_auto_commands_cooldowns()` - Reset des cooldowns

## 📊 Tableau de Bord

### Dashboard Component

**Fichier**: `src/components/pages/Dashboard.tsx`

**Description**: Page principale avec statut du bot et statistiques.

#### Fonctionnalités
- ✅ Statut de connexion du bot Discord
- ✅ Interface de connexion avec token
- ✅ Indicateurs des fonctionnalités (Stealth Mode, animations, snipers)
- ✅ Statistiques temps réel
- ✅ Actions rapides

#### État Géré
Utilise `useAppStore` pour:
- Statut de connexion (`isConnected`, `botStatus`)
- Informations utilisateur (`discordUser`)
- Mode furtif (`stealthMode`)
- Statistiques (`stats`)

## 🔧 Hooks Personnalisés

### useTauriCommand Hook

**Fichier**: `src/hooks/useTauri.ts`

**Description**: Hook pour exécuter des commandes Tauri avec gestion d'état.

#### Utilisation
```tsx
const { data, loading, error, execute } = useTauriCommand<UserInfo>('get_user_info');

// Avec arguments
const { data, loading } = useTauriCommand<ServerInfo>('get_server_info', { serverId: '123' });

// Re-exécution manuelle
const handleRefresh = () => execute({ serverId: '456' });
```

#### Retourne
- `data`: Résultat de la commande (T | null)
- `loading`: Indicateur de chargement
- `error`: Message d'erreur (string | null)
- `execute`: Fonction de re-exécution
- `refetch`: Alias pour execute()

### useTauriEvent Hook

**Description**: Hook pour écouter les événements Tauri temps réel.

#### Utilisation
```tsx
// Écoute des notifications
useTauriEvent<Notification>('notification-received', (notification) => {
  console.log('Nouvelle notification:', notification.title);
  showToast(notification.message);
});

// Écoute des changements de statut
useTauriEvent<BotStatus>('bot-status-changed', (status) => {
  setBotStatus(status.isOnline ? 'online' : 'offline');
});
```

### useTauriState Hook

**Description**: Hook pour synchroniser un état React avec Tauri/Rust.

#### Utilisation
```tsx
const { value: theme, setValue: setTheme, loading } = useTauriState<string>(
  'get_user_theme',
  'set_user_theme',
  'dark' // valeur par défaut
);

const handleThemeChange = async (newTheme: string) => {
  try {
    await setTheme(newTheme); // Met à jour côté Rust et React
  } catch (error) {
    console.error('Erreur changement thème:', error);
  }
};
```

## 🗄️ Gestion d'État

### App Store (Zustand)

**Fichier**: `src/stores/appStore.ts`

**Description**: État global de l'application avec synchronisation Tauri.

#### État Principal
```typescript
interface AppState {
  // Connexion bot
  isConnected: boolean;
  botStatus: 'offline' | 'connecting' | 'online' | 'error';
  discordUser: DiscordUser | null;
  
  // Configuration
  stealthMode: 'normal' | 'ghost';
  currentPage: string;
  
  // Fonctionnalités
  animationsEnabled: boolean;
  sniperEnabled: boolean;
  
  // Statistiques
  stats: {
    commandsUsed: number;
    serversBackedUp: number;
    animationsActive: number;
    uptime: number;
  };
}
```

#### Actions Principales
- `connectBot(token, saveSession?)` - Connexion du bot
- `disconnectBot()` - Déconnexion
- `setStealthMode(mode)` - Changement mode furtif
- `autoConnectFromSession()` - Reconnexion automatique

## 🎯 Bonnes Pratiques

### 1. Gestion d'Erreurs
```tsx
// ✅ Bon
try {
  await invoke('risky_operation');
  showSuccess('Opération réussie');
} catch (error) {
  console.error('Erreur opération:', error);
  showError('Échec de l\'opération');
}

// ❌ Éviter
invoke('risky_operation'); // Pas de gestion d'erreur
```

### 2. TypeScript Strict
```tsx
// ✅ Bon - Typage explicite
const config: AutoTranslateConfig = {
  enabled: true,
  service: 'google',
  // ...
};

// ❌ Éviter - Type any
const config: any = { /* ... */ };
```

### 3. Composants Documentés
```tsx
/**
 * Composant de sélection de couleur avec validation
 * @param props.label - Libellé affiché
 * @param props.value - Valeur actuelle
 * @param props.onChange - Callback de changement
 */
const ColorPicker = ({ label, value, onChange }: ColorPickerProps) => {
  // ...
};
```

### 4. Hooks Personnalisés
```tsx
// ✅ Bon - Logique réutilisable
const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  
  useTauriEvent('notification-received', (notif) => {
    setNotifications(prev => [notif, ...prev]);
  });
  
  return { notifications, clearNotifications };
};

// Usage
const { notifications } = useNotifications();
```

### 5. Performance
```tsx
// ✅ Bon - Mémoisation appropriée
const expensiveCalculation = useMemo(() => {
  return processLargeDataset(data);
}, [data]);

// ✅ Bon - Callbacks mémorisés
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

## 🔍 Débogage

### 1. Console de Développement
```tsx
// Logs structurés
console.group('🎨 ThemeBuilder');
console.log('Configuration actuelle:', currentTheme);
console.log('Modifications non sauvées:', hasUnsavedChanges);
console.groupEnd();
```

### 2. Tauri DevTools
```bash
# Console Tauri
npm run tauri dev

# Logs détaillés
RUST_LOG=debug npm run tauri dev
```

### 3. React DevTools
- Extension React DevTools pour inspecter les composants
- Zustand DevTools pour l'état global

## 📝 Contribution

### Ajout de Nouveaux Composants

1. **Créer le fichier composant**
```tsx
// src/components/MonNouveauComposant.tsx
/**
 * Description du composant
 * @param props - Props du composant
 */
export function MonNouveauComposant(props: MonNouveauComposantProps) {
  // Implémentation
}
```

2. **Définir les types**
```typescript
// Dans src/types/voidbot.d.ts ou interface locale
interface MonNouveauComposantProps {
  /** Description de la prop */
  maProp: string;
  /** Callback optionnel */
  onAction?: () => void;
}
```

3. **Ajouter la documentation**
- JSDoc détaillé pour le composant
- Exemples d'utilisation
- API Tauri utilisée
- Tests si applicable

4. **Intégrer au routing**
```tsx
// Ajouter à la navigation
const routes = [
  // ...
  { path: '/mon-composant', component: MonNouveauComposant },
];
```

### Standards de Code

- **TypeScript strict** activé
- **ESLint + Prettier** pour la cohérence
- **Commentaires en français** pour la documentation
- **Interface en français** pour l'utilisateur final
- **Gestion d'erreurs** systématique
- **Tests unitaires** pour la logique critique

## 🚀 Performance et Optimisation

### 1. Bundle Splitting
```tsx
// Lazy loading des pages
const Dashboard = lazy(() => import('./pages/Dashboard'));
const ThemeBuilder = lazy(() => import('./components/ThemeBuilder'));
```

### 2. Mémoisation
```tsx
// Mémoriser les composants coûteux
export const ExpensiveComponent = memo(({ data }: Props) => {
  return <ComplexVisualization data={data} />;
});
```

### 3. Optimisation Tauri
```rust
// Côté Rust - Commands asynchrones
#[tauri::command]
async fn expensive_operation() -> Result<String, String> {
  // Opération en arrière-plan
  tokio::task::spawn_blocking(|| {
    // Calcul intensif
  }).await.map_err(|e| e.to_string())
}
```

Cette documentation est maintenue à jour avec l'évolution du projet VoidBot. Pour toute question ou contribution, consultez le README principal du projet.