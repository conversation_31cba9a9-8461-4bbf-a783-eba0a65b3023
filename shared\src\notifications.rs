use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Types d'événements Discord surveillés par le Notification Center
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NotificationEventType {
    /// Message supprimé avec mention (ghostping)
    Ghostping,
    /// Ami retiré de la liste d'amis
    FriendRemoved,
    /// Bloqué par un ami
    FriendBlocked,
    /// Expulsé d'un serveur
    ServerKicked,
    /// Banni d'un serveur
    ServerBanned,
    /// Rôle modifié dans un serveur
    RoleChanged,
    /// Pseudo modifié
    NicknameChanged,
    /// Mot-clé détecté dans un message
    KeywordDetected,
    /// Réponse à un ticket support
    TicketReplied,
    /// Utilisateur tape en DM
    UserTyping,
    /// Nouveau giveaway détecté
    GiveawayDetected,
    /// Code Nitro récupéré
    NitroSniped,
    /// Nouveau message dans canal surveillé
    ChannelMessage,
    /// Mention dans un serveur
    Mentioned,
}

/// Niveaux de priorité des notifications
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NotificationPriority {
    Low,
    Medium,
    High,
    Critical,
}

/// Structure d'une notification VoidBot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VoidNotification {
    /// ID unique de la notification
    pub id: String,
    /// Type d'événement Discord
    pub event_type: NotificationEventType,
    /// Titre de la notification
    pub title: String,
    /// Description détaillée
    pub description: String,
    /// Priorité de la notification
    pub priority: NotificationPriority,
    /// Timestamp de création
    pub timestamp: DateTime<Utc>,
    /// ID du serveur Discord (optionnel)
    pub guild_id: Option<String>,
    /// ID du canal Discord (optionnel)
    pub channel_id: Option<String>,
    /// ID de l'utilisateur concerné (optionnel)
    pub user_id: Option<String>,
    /// Données supplémentaires spécifiques à l'événement
    pub metadata: HashMap<String, String>,
    /// Notification lue ou non
    pub read: bool,
    /// URL webhook Discord pour notification externe (optionnel)
    pub webhook_url: Option<String>,
}

impl VoidNotification {
    /// Créer une nouvelle notification
    pub fn new(
        event_type: NotificationEventType,
        title: String,
        description: String,
        priority: NotificationPriority,
    ) -> Self {
        Self {
            id: chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0).to_string(),
            event_type,
            title,
            description,
            priority,
            timestamp: Utc::now(),
            guild_id: None,
            channel_id: None,
            user_id: None,
            metadata: HashMap::new(),
            read: false,
            webhook_url: None,
        }
    }

    /// Ajouter des métadonnées à la notification
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// Définir le serveur Discord
    pub fn with_guild(mut self, guild_id: String) -> Self {
        self.guild_id = Some(guild_id);
        self
    }

    /// Définir le canal Discord
    pub fn with_channel(mut self, channel_id: String) -> Self {
        self.channel_id = Some(channel_id);
        self
    }

    /// Définir l'utilisateur concerné
    pub fn with_user(mut self, user_id: String) -> Self {
        self.user_id = Some(user_id);
        self
    }

    /// Marquer comme lue
    pub fn mark_as_read(&mut self) {
        self.read = true;
    }

    /// Obtenir l'icône associée au type de notification
    pub fn get_icon(&self) -> &'static str {
        match self.event_type {
            NotificationEventType::Ghostping => "👻",
            NotificationEventType::FriendRemoved => "💔",
            NotificationEventType::FriendBlocked => "🚫",
            NotificationEventType::ServerKicked => "🦵",
            NotificationEventType::ServerBanned => "🔨",
            NotificationEventType::RoleChanged => "🎭",
            NotificationEventType::NicknameChanged => "📝",
            NotificationEventType::KeywordDetected => "🔍",
            NotificationEventType::TicketReplied => "🎫",
            NotificationEventType::UserTyping => "⌨️",
            NotificationEventType::GiveawayDetected => "🎁",
            NotificationEventType::NitroSniped => "💎",
            NotificationEventType::ChannelMessage => "💬",
            NotificationEventType::Mentioned => "📢",
        }
    }

    /// Obtenir la couleur associée au niveau de priorité
    pub fn get_priority_color(&self) -> &'static str {
        match self.priority {
            NotificationPriority::Low => "#10B981", // Vert
            NotificationPriority::Medium => "#F59E0B", // Orange
            NotificationPriority::High => "#EF4444", // Rouge
            NotificationPriority::Critical => "#DC2626", // Rouge foncé
        }
    }
}

/// Configuration du Notification Center
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    /// Événements activés
    pub enabled_events: Vec<NotificationEventType>,
    /// Mots-clés à surveiller
    pub keywords: Vec<String>,
    /// URLs webhook Discord pour notifications externes
    pub webhook_urls: Vec<String>,
    /// Activer les notifications desktop Windows
    pub desktop_notifications: bool,
    /// Activer les sons de notification
    pub sound_enabled: bool,
    /// Volume des sons (0.0 à 1.0)
    pub sound_volume: f32,
    /// Conserver l'historique (en jours)
    pub history_retention_days: u32,
    /// Canaux Discord à surveiller spécifiquement
    pub monitored_channels: Vec<String>,
    /// Utilisateurs à surveiller spécifiquement
    pub monitored_users: Vec<String>,
}

impl Default for NotificationConfig {
    fn default() -> Self {
        Self {
            enabled_events: vec![
                NotificationEventType::Ghostping,
                NotificationEventType::FriendRemoved,
                NotificationEventType::ServerKicked,
                NotificationEventType::ServerBanned,
                NotificationEventType::Mentioned,
            ],
            keywords: vec![],
            webhook_urls: vec![],
            desktop_notifications: true,
            sound_enabled: true,
            sound_volume: 0.5,
            history_retention_days: 30,
            monitored_channels: vec![],
            monitored_users: vec![],
        }
    }
}

/// Gestionnaire des notifications VoidBot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationManager {
    /// Configuration actuelle
    pub config: NotificationConfig,
    /// Historique des notifications
    pub notifications: Vec<VoidNotification>,
}

impl NotificationManager {
    /// Créer un nouveau gestionnaire de notifications
    pub fn new() -> Self {
        Self {
            config: NotificationConfig::default(),
            notifications: Vec::new(),
        }
    }

    /// Ajouter une nouvelle notification
    pub fn add_notification(&mut self, notification: VoidNotification) {
        // Vérifier si ce type d'événement est activé
        if !self.config.enabled_events.contains(&notification.event_type) {
            return;
        }

        self.notifications.push(notification);
        
        // Nettoyer l'historique si nécessaire
        self.cleanup_old_notifications();
    }

    /// Marquer une notification comme lue
    pub fn mark_as_read(&mut self, notification_id: &str) -> bool {
        if let Some(notification) = self.notifications.iter_mut()
            .find(|n| n.id == notification_id) {
            notification.mark_as_read();
            true
        } else {
            false
        }
    }

    /// Marquer toutes les notifications comme lues
    pub fn mark_all_as_read(&mut self) {
        for notification in &mut self.notifications {
            notification.mark_as_read();
        }
    }

    /// Obtenir les notifications non lues
    pub fn get_unread_notifications(&self) -> Vec<&VoidNotification> {
        self.notifications.iter()
            .filter(|n| !n.read)
            .collect()
    }

    /// Obtenir les notifications par type
    pub fn get_notifications_by_type(&self, event_type: &NotificationEventType) -> Vec<&VoidNotification> {
        self.notifications.iter()
            .filter(|n| &n.event_type == event_type)
            .collect()
    }

    /// Supprimer les anciennes notifications - OPTIMISÉ MEMORY LEAKS
    fn cleanup_old_notifications(&mut self) {
        let now = Utc::now();
        let retention_duration = chrono::Duration::days(self.config.history_retention_days as i64);
        
        // Nettoyer par âge ET par limite de count pour éviter les memory leaks
        let mut removed_count = 0;
        
        // 1. Supprimer les notifications expirées
        self.notifications.retain(|notification| {
            let is_expired = now.signed_duration_since(notification.timestamp) > retention_duration;
            if is_expired {
                removed_count += 1;
            }
            !is_expired
        });
        
        // 2. Si encore trop de notifications, garder seulement les plus récentes (limite à 1000)
        let max_notifications = 1000;
        if self.notifications.len() > max_notifications {
            // Trier par timestamp (plus récent en premier)
            self.notifications.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
            
            // Garder seulement max_notifications
            let excess = self.notifications.len() - max_notifications;
            self.notifications.truncate(max_notifications);
            removed_count += excess;
        }
        
        // 3. Nettoyer explicitement la mémoire si beaucoup supprimé
        if removed_count > 50 {
            self.notifications.shrink_to_fit();
        }
        
        // 4. Log pour monitoring
        if removed_count > 0 {
            tracing::debug!("Cleaned up {} old notifications, {} remaining", removed_count, self.notifications.len());
        }
    }

    /// Obtenir le nombre de notifications non lues
    pub fn get_unread_count(&self) -> usize {
        self.notifications.iter()
            .filter(|n| !n.read)
            .count()
    }

    /// Supprimer une notification
    pub fn remove_notification(&mut self, notification_id: &str) -> bool {
        let initial_len = self.notifications.len();
        self.notifications.retain(|n| n.id != notification_id);
        self.notifications.len() != initial_len
    }

    /// Vider toutes les notifications
    pub fn clear_all_notifications(&mut self) {
        self.notifications.clear();
    }
}