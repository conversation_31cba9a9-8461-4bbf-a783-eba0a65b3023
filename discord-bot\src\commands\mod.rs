use anyhow::Result;
use serenity::{
    builder::CreateCommand,
    model::application::{Command, CommandOptionType, CommandType},
    prelude::*,
};

pub mod animate;
pub mod backup;
pub mod general;
pub mod images;
pub mod rpc;
pub mod snipe;
pub mod troll;

use animate::*;
use backup::*;
use general::*;
use images::*;
use rpc::*;
use snipe::*;
use troll::*;

pub async fn register_commands(ctx: &Context) -> Result<()> {
    let mut commands = vec![
        // General commands
        CreateCommand::new("ping")
            .description("Check bot latency"),
        
        CreateCommand::new("help")
            .description("Show available commands"),

        CreateCommand::new("settings")
            .description("Configure bot settings"),

        // Animation commands
        CreateCommand::new("animate")
            .description("Start profile animation")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "type",
                    "Type of animation"
                )
                .required(true)
                .add_string_choice("status", "status")
                .add_string_choice("bio", "bio")
                .add_string_choice("avatar", "avatar")
            ),

        CreateCommand::new("animate_stop")
            .description("Stop all animations"),

        // Rich Presence commands
        CreateCommand::new("rpc")
            .description("Configure Rich Presence")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "preset",
                    "Preset to use"
                )
                .required(false)
            ),

        CreateCommand::new("rpc_presets")
            .description("List available RPC presets"),

        // Backup & Restore commands
        CreateCommand::new("backup")
            .description("Backup current server"),

        CreateCommand::new("restore")
            .description("Restore server from backup")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "backup_id",
                    "Backup ID to restore"
                )
                .required(true)
            ),

        CreateCommand::new("clone")
            .description("Clone server structure")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "target_guild",
                    "Target guild ID"
                )
                .required(true)
            ),

        // Utility commands
        CreateCommand::new("snipe")
            .description("Show last deleted message"),

        CreateCommand::new("clear")
            .description("Clear your messages")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::Integer,
                    "amount",
                    "Number of messages to clear"
                )
                .required(false)
                .min_int_value(1)
                .max_int_value(100)
            ),

        // Fun commands
        CreateCommand::new("8ball")
            .description("Ask the magic 8-ball")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "question",
                    "Your question"
                )
                .required(true)
            ),

        CreateCommand::new("coinflip")
            .description("Flip a coin"),

        CreateCommand::new("dice")
            .description("Roll a dice")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::Integer,
                    "sides",
                    "Number of sides"
                )
                .required(false)
                .min_int_value(2)
                .max_int_value(100)
            ),

        // Tools
        CreateCommand::new("translate")
            .description("Translate text")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "text",
                    "Text to translate"
                )
                .required(true)
            )
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "target_lang",
                    "Target language"
                )
                .required(true)
            ),

        CreateCommand::new("calculate")
            .description("Simple calculator")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "expression",
                    "Mathematical expression"
                )
                .required(true)
            ),

        // Server management
        CreateCommand::new("server_info")
            .description("Show server information"),

        CreateCommand::new("user_info")
            .description("Show user information")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::User,
                    "user",
                    "User to get info about"
                )
                .required(false)
            ),

        // Sniper commands
        CreateCommand::new("nitro_sniper")
            .description("Toggle Nitro sniper")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::Boolean,
                    "enabled",
                    "Enable or disable"
                )
                .required(true)
            ),

        CreateCommand::new("giveaway_sniper")
            .description("Toggle giveaway sniper")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::Boolean,
                    "enabled",
                    "Enable or disable"
                )
                .required(true)
            ),
    ];

    // Ajouter les commandes troll
    commands.extend(create_troll_commands());
    
    // Ajouter les commandes d'images
    commands.extend(create_image_commands());

    Command::set_global_commands(&ctx.http, commands).await?;
    Ok(())
}