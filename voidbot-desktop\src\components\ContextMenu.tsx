import React, { useState, useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { listen } from '@tauri-apps/api/event';

interface ContextMenuProps {
  x: number;
  y: number;
  onClose: () => void;
  messageData?: {
    id: string;
    content: string;
    authorId: string;
    timestamp: string;
    guildId?: string;
    channelId?: string;
  };
}

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  shortcut?: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, onClose, messageData }) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);

    // Fermer le menu si on clique en dehors
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    // Fermer avec Escape
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  const copyToClipboard = async (text: string, message: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showNotification(message);
      onClose();
    } catch (err) {
      console.error('Erreur copie:', err);
      showNotification('Erreur lors de la copie', 'error');
    }
  };

  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    // Émettre un événement pour afficher la notification dans l'app principale
    window.dispatchEvent(new CustomEvent('showNotification', {
      detail: { message, type }
    }));
  };

  const menuItems: MenuItem[] = [
    {
      id: 'copy-emoji',
      label: 'Copier emoji',
      icon: '😀',
      action: async () => {
        try {
          // Extraire les emojis du message
          const emojiRegex = /<:(.*?):\d+>/g;
          const matches = messageData?.content.match(emojiRegex);
          if (matches && matches.length > 0) {
            const emojiNames = matches.map(match => {
              const name = match.match(/:(.*):/)?.[1];
              return name ? `:${name}:` : match;
            }).join(' ');
            await copyToClipboard(emojiNames, 'Emojis copiés');
          } else {
            showNotification('Aucun emoji trouvé dans ce message', 'error');
          }
        } catch (error) {
          showNotification('Erreur lors de la copie des emojis', 'error');
        }
      }
    },
    {
      id: 'translate',
      label: 'Traduire',
      icon: '🌐',
      shortcut: 'Ctrl+T',
      action: async () => {
        try {
          if (messageData?.content) {
            const translated = await invoke('translate_text', { 
              text: messageData.content,
              targetLang: 'fr'
            });
            await copyToClipboard(translated as string, 'Traduction copiée');
          }
        } catch (error) {
          showNotification('Erreur lors de la traduction', 'error');
        }
      }
    },
    {
      id: 'separator1',
      label: '',
      icon: '',
      action: () => {},
      separator: true
    },
    {
      id: 'copy-content',
      label: 'Copier le contenu',
      icon: '📋',
      shortcut: 'Ctrl+C',
      action: () => {
        if (messageData?.content) {
          copyToClipboard(messageData.content, 'Contenu copié');
        }
      }
    },
    {
      id: 'copy-message-id',
      label: 'Copier ID message',
      icon: '🆔',
      action: () => {
        if (messageData?.id) {
          copyToClipboard(messageData.id, 'ID message copié');
        }
      }
    },
    {
      id: 'copy-message-link',
      label: 'Copier lien message',
      icon: '🔗',
      action: () => {
        if (messageData?.id && messageData?.channelId) {
          const link = messageData.guildId 
            ? `https://discord.com/channels/${messageData.guildId}/${messageData.channelId}/${messageData.id}`
            : `https://discord.com/channels/@me/${messageData.channelId}/${messageData.id}`;
          copyToClipboard(link, 'Lien message copié');
        }
      }
    },
    {
      id: 'separator2',
      label: '',
      icon: '',
      action: () => {},
      separator: true
    },
    {
      id: 'copy-user-id',
      label: 'Copier ID utilisateur',
      icon: '👤',
      action: () => {
        if (messageData?.authorId) {
          copyToClipboard(messageData.authorId, 'ID utilisateur copié');
        }
      }
    },
    {
      id: 'user-creation-date',
      label: 'Date de création compte',
      icon: '📅',
      action: async () => {
        try {
          if (messageData?.authorId) {
            const date = await invoke('get_user_creation_date', { 
              userId: messageData.authorId 
            });
            showNotification(`Compte créé le ${date}`);
          }
        } catch (error) {
          showNotification('Erreur lors de la récupération de la date', 'error');
        }
      }
    }
  ];

  const handleItemClick = (item: MenuItem) => {
    if (!item.disabled && !item.separator) {
      item.action();
    }
  };

  // Ajuster la position pour éviter que le menu sorte de l'écran
  const adjustedX = Math.min(x, window.innerWidth - 300);
  const adjustedY = Math.min(y, window.innerHeight - 400);

  return (
    <div 
      ref={menuRef}
      className={`fixed z-50 min-w-[280px] bg-gray-900 border border-gray-600 rounded-lg shadow-2xl overflow-hidden transition-all duration-200 ${
        isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
      }`}
      style={{ 
        left: `${adjustedX}px`, 
        top: `${adjustedY}px`,
        backdropFilter: 'blur(20px)',
        backgroundColor: 'rgba(15, 15, 20, 0.98)'
      }}
    >
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-600 bg-gradient-to-r from-purple-600/20 to-pink-600/20">
        <div className="flex items-center gap-2 text-sm font-semibold text-purple-300">
          🔮 VoidBot Utils
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-2">
        {menuItems.map((item) => {
          if (item.separator) {
            return (
              <div key={item.id} className="h-px bg-gray-600 my-1" />
            );
          }

          return (
            <div
              key={item.id}
              className={`px-4 py-2 text-sm cursor-pointer transition-colors duration-150 flex items-center gap-3 ${
                item.disabled
                  ? 'text-gray-500 cursor-not-allowed'
                  : 'text-white hover:bg-gray-700 hover:text-purple-300'
              }`}
              onClick={() => handleItemClick(item)}
            >
              <span className="text-base">{item.icon}</span>
              <span className="flex-1">{item.label}</span>
              {item.shortcut && (
                <span className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">
                  {item.shortcut}
                </span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
