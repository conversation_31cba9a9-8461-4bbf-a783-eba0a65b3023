# Dockerfile pour VoidBot Discord Bot
FROM rust:1.75 as builder

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers source
WORKDIR /app
COPY . .

# Build en mode release
RUN cargo build --release --bin voidbot-bot

# Image finale légère
FROM debian:bookworm-slim

# Installer les dépendances runtime
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Copier l'exécutable
COPY --from=builder /app/target/release/voidbot-bot /usr/local/bin/voidbot-bot

# Port par défaut
EXPOSE 3000

# Commande de démarrage
CMD ["voidbot-bot"]