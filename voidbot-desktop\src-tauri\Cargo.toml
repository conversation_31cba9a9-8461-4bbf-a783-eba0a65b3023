[package]
name = "voidbot-desktop"
version = "1.0.0"
description = "VoidBot - Ultimate Discord Toolkit"
authors = ["VoidBot Team"]
edition = "2021"

[lib]
name = "voidbot_desktop_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0", features = [] }

[dependencies]
# Workspace deps
voidbot-shared = { path = "../../shared" }
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true

# Tauri - VERSIONS LATEST STABLES  
tauri = { version = "2.6.2", features = [ "tray-icon", "wry"], default-features = false }
tauri-plugin-opener = "2.4.0"
tauri-plugin-shell = "2.3.0"
tauri-plugin-updater = "2.4.0"

# Utils - VERSIONS COMPATIBLES
once_cell = "1.21"
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }
chrono = { version = "0.4", features = ["serde"] }
sysinfo = "0.30"
auto-launch = "0.5"
rdev = "0.5"
regex = "1.10"

# OAuth server dependencies
warp = "0.3"
urlencoding = "2.1"

[profile.release]
# Optimize for size and performance
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.release-with-debug]
inherits = "release"
debug = true
strip = false

