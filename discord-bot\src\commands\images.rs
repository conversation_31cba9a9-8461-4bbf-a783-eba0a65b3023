use serenity::all::*;
use anyhow::Result;
use std::collections::HashMap;
use reqwest;
use base64::{Engine as _, engine::general_purpose};
use serde::{Deserialize, Serialize};
use image::{ImageFormat, DynamicImage, RgbaImage};
use image::Rgba;
use imageproc::drawing::{draw_filled_rect_mut};

/// Types de génération d'images supportés
#[derive(Debug, Clone)]
pub enum ImageType {
    /// Meme avec texte supérieur/inférieur
    Meme { top_text: String, bottom_text: Option<String> },
    /// Avatar avec overlay (couronne, chapeau, etc.)
    AvatarOverlay { overlay_type: OverlayType },
    /// Bannière personnalisée
    Banner { text: String, style: BannerStyle },
    /// Reaction image avec texte
    Reaction { reaction_type: ReactionType, text: Option<String> },
    /// Statistiques visuelles
    Stats { user_id: u64 },
}

/// Types d'overlays pour avatars
#[derive(Debug, Clone)]
pub enum OverlayType {
    <PERSON>,
    Hat,
    Sunglass<PERSON>,
    <PERSON>ame,
    Jail,
    Wanted,
}

/// Styles de bannières
#[derive(Debug, Clone)]
pub enum BannerStyle {
    Gradient,
    Neon,
    Minimal,
    Cyberpunk,
}

/// Types de réactions
#[derive(Debug, Clone)]
pub enum ReactionType {
    Triggered,
    Stonks,
    NotStonks,
    Drake,
    Surprised,
    Confused,
    Think,
}

/// Service de génération d'images ultra-performant
pub struct ImageGenerator {
    /// Client HTTP optimisé
    http_client: reqwest::Client,
    /// Templates d'images en cache
    templates_cache: HashMap<String, Vec<u8>>,
}

/// Configuration pour génération d'image
#[derive(Debug, Serialize, Deserialize)]
pub struct ImageConfig {
    pub width: u32,
    pub height: u32,
    pub font_size: f32,
    pub text_color: [u8; 4], // RGBA
    pub background_color: [u8; 4], // RGBA
    pub quality: u8, // 1-100
}

impl Default for ImageConfig {
    fn default() -> Self {
        Self {
            width: 800,
            height: 600,
            font_size: 48.0,
            text_color: [255, 255, 255, 255], // Blanc
            background_color: [0, 0, 0, 255], // Noir
            quality: 85,
        }
    }
}

impl ImageGenerator {
    /// Créer un nouveau générateur d'images
    pub fn new() -> Result<Self> {
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()?;

        Ok(Self {
            http_client,
            templates_cache: HashMap::new(),
        })
    }

    /// Générer une image selon le type spécifié
    pub async fn generate_image(&mut self, image_type: ImageType, config: Option<ImageConfig>) -> Result<Vec<u8>> {
        let config = config.unwrap_or_default();

        match image_type {
            ImageType::Meme { top_text, bottom_text } => {
                self.generate_meme(&top_text, bottom_text.as_deref(), &config).await
            },
            ImageType::AvatarOverlay { overlay_type } => {
                self.generate_avatar_overlay(overlay_type, &config).await
            },
            ImageType::Banner { text, style } => {
                self.generate_banner(&text, style, &config).await
            },
            ImageType::Reaction { reaction_type, text } => {
                self.generate_reaction(reaction_type, text.as_deref(), &config).await
            },
            ImageType::Stats { user_id } => {
                self.generate_stats_image(user_id, &config).await
            },
        }
    }

    /// Générer un meme avec texte supérieur/inférieur
    async fn generate_meme(&mut self, top_text: &str, bottom_text: Option<&str>, config: &ImageConfig) -> Result<Vec<u8>> {
        // Créer une image de base
        let mut img = RgbaImage::new(config.width, config.height);
        
        // Remplir avec la couleur de fond
        for pixel in img.pixels_mut() {
            *pixel = image::Rgba(config.background_color);
        }

        // Dessiner des zones de texte simples (rectangles avec couleur)
        let text_height = 60;
        
        // Zone supérieure pour le texte
        draw_filled_rect_mut(
            &mut img,
            imageproc::rect::Rect::at(50, 50).of_size(config.width - 100, text_height),
            Rgba(config.text_color)
        );

        // Zone inférieure si texte inférieur présent
        if bottom_text.is_some() {
            draw_filled_rect_mut(
                &mut img,
                imageproc::rect::Rect::at(50, config.height as i32 - 110).of_size(config.width - 100, text_height),
                Rgba(config.text_color)
            );
        }

        // Convertir en bytes PNG
        let mut bytes = Vec::new();
        let dynamic_img = DynamicImage::ImageRgba8(img);
        dynamic_img.write_to(&mut std::io::Cursor::new(&mut bytes), ImageFormat::Png)?;

        Ok(bytes)
    }

    /// Générer un overlay sur avatar
    async fn generate_avatar_overlay(&mut self, overlay_type: OverlayType, config: &ImageConfig) -> Result<Vec<u8>> {
        // Pour la démonstration, on génère une image simple
        // En production, on combinerait l'avatar utilisateur avec l'overlay
        let mut img = RgbaImage::new(config.width, config.height);
        
        // Couleur selon le type d'overlay
        let color = match overlay_type {
            OverlayType::Crown => [255, 215, 0, 255], // Or
            OverlayType::Hat => [128, 0, 128, 255], // Violet
            OverlayType::Sunglasses => [0, 0, 0, 255], // Noir
            OverlayType::Frame => [139, 69, 19, 255], // Marron
            OverlayType::Jail => [192, 192, 192, 255], // Gris
            OverlayType::Wanted => [220, 20, 60, 255], // Rouge
        };

        for pixel in img.pixels_mut() {
            *pixel = image::Rgba(color);
        }

        // Ajouter une barre centrale pour indiquer le type
        draw_filled_rect_mut(
            &mut img,
            imageproc::rect::Rect::at(config.width as i32 / 4, config.height as i32 / 2 - 20).of_size(config.width / 2, 40),
            Rgba([255, 255, 255, 255])
        );

        let mut bytes = Vec::new();
        let dynamic_img = DynamicImage::ImageRgba8(img);
        dynamic_img.write_to(&mut std::io::Cursor::new(&mut bytes), ImageFormat::Png)?;

        Ok(bytes)
    }

    /// Générer une bannière personnalisée
    async fn generate_banner(&mut self, text: &str, style: BannerStyle, config: &ImageConfig) -> Result<Vec<u8>> {
        let mut img = RgbaImage::new(config.width, config.height);
        
        // Couleurs selon le style
        let (bg_color, text_color) = match style {
            BannerStyle::Gradient => ([75, 0, 130, 255], [255, 255, 255, 255]), // Violet/Blanc
            BannerStyle::Neon => ([0, 0, 0, 255], [0, 255, 255, 255]), // Noir/Cyan
            BannerStyle::Minimal => ([248, 248, 255, 255], [0, 0, 0, 255]), // Blanc/Noir
            BannerStyle::Cyberpunk => ([26, 26, 46, 255], [255, 20, 147, 255]), // Sombre/Rose
        };

        for pixel in img.pixels_mut() {
            *pixel = image::Rgba(bg_color);
        }

        // Dessiner une barre centrale stylée pour la bannière
        draw_filled_rect_mut(
            &mut img,
            imageproc::rect::Rect::at(100, config.height as i32 / 2 - 30).of_size(config.width - 200, 60),
            Rgba(text_color)
        );

        let mut bytes = Vec::new();
        let dynamic_img = DynamicImage::ImageRgba8(img);
        dynamic_img.write_to(&mut std::io::Cursor::new(&mut bytes), ImageFormat::Png)?;

        Ok(bytes)
    }

    /// Générer une image de réaction
    async fn generate_reaction(&mut self, reaction_type: ReactionType, text: Option<&str>, config: &ImageConfig) -> Result<Vec<u8>> {
        let mut img = RgbaImage::new(config.width, config.height);
        
        // Couleur selon le type de réaction
        let bg_color = match reaction_type {
            ReactionType::Triggered => [255, 0, 0, 255], // Rouge
            ReactionType::Stonks => [0, 255, 0, 255], // Vert
            ReactionType::NotStonks => [255, 0, 0, 255], // Rouge
            ReactionType::Drake => [255, 215, 0, 255], // Or
            ReactionType::Surprised => [255, 165, 0, 255], // Orange
            ReactionType::Confused => [128, 128, 128, 255], // Gris
            ReactionType::Think => [0, 0, 255, 255], // Bleu
        };

        for pixel in img.pixels_mut() {
            *pixel = image::Rgba(bg_color);
        }

        // Dessiner des formes pour représenter la réaction
        let center_x = config.width as i32 / 2;
        let center_y = config.height as i32 / 2;
        
        // Forme principale selon le type de réaction
        match reaction_type {
            ReactionType::Triggered => {
                // Multiples rectangles pour "triggered"
                for i in 0..3 {
                    draw_filled_rect_mut(
                        &mut img,
                        imageproc::rect::Rect::at(center_x - 50 + i * 10, center_y - 20 + i * 5).of_size(100, 40),
                        Rgba([255, 255, 255, 200])
                    );
                }
            },
            _ => {
                // Forme simple pour les autres
                draw_filled_rect_mut(
                    &mut img,
                    imageproc::rect::Rect::at(center_x - 75, center_y - 30).of_size(150, 60),
                    Rgba([255, 255, 255, 255])
                );
            }
        }

        let mut bytes = Vec::new();
        let dynamic_img = DynamicImage::ImageRgba8(img);
        dynamic_img.write_to(&mut std::io::Cursor::new(&mut bytes), ImageFormat::Png)?;

        Ok(bytes)
    }

    /// Générer une image de statistiques utilisateur
    async fn generate_stats_image(&mut self, user_id: u64, config: &ImageConfig) -> Result<Vec<u8>> {
        let mut img = RgbaImage::new(config.width, config.height);
        
        // Fond gradient
        for pixel in img.pixels_mut() {
            *pixel = image::Rgba([30, 30, 30, 255]);
        }

        // Dessiner des barres pour représenter les statistiques
        let bar_width = config.width - 100;
        let bar_height = 30;
        let start_y = 100;
        
        // Titre (barre principale)
        draw_filled_rect_mut(
            &mut img,
            imageproc::rect::Rect::at(50, 50).of_size(bar_width, 40),
            Rgba([255, 255, 255, 255])
        );
        
        // Barres de statistiques (longueurs variables pour simuler des données)
        let stats_values = vec![80, 60, 40, 90]; // Pourcentages fictifs
        let colors = vec![
            [255, 100, 100, 255], // Rouge
            [100, 255, 100, 255], // Vert
            [100, 100, 255, 255], // Bleu
            [255, 255, 100, 255], // Jaune
        ];
        
        for (i, (value, color)) in stats_values.iter().zip(colors.iter()).enumerate() {
            let y = start_y + (i as i32 * 60);
            let width = (bar_width as f32 * (*value as f32 / 100.0)) as u32;
            
            draw_filled_rect_mut(
                &mut img,
                imageproc::rect::Rect::at(50, y).of_size(width, bar_height),
                Rgba(*color)
            );
        }

        let mut bytes = Vec::new();
        let dynamic_img = DynamicImage::ImageRgba8(img);
        dynamic_img.write_to(&mut std::io::Cursor::new(&mut bytes), ImageFormat::Png)?;

        Ok(bytes)
    }


    /// Télécharger et mettre en cache une image template
    async fn fetch_template(&mut self, url: &str) -> Result<Vec<u8>> {
        if let Some(cached) = self.templates_cache.get(url) {
            return Ok(cached.clone());
        }

        let response = self.http_client.get(url).send().await?;
        let bytes = response.bytes().await?.to_vec();
        
        self.templates_cache.insert(url.to_string(), bytes.clone());
        Ok(bytes)
    }
}

/// Commandes slash pour la génération d'images
pub async fn handle_image_commands(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let mut generator = ImageGenerator::new()?;

    match command.data.name.as_str() {
        "meme" => {
            let top_text = command.data.options.iter()
                .find(|opt| opt.name == "top_text")
                .and_then(|opt| opt.value.as_str())
                .unwrap_or("Texte supérieur");
                
            let bottom_text = command.data.options.iter()
                .find(|opt| opt.name == "bottom_text")
                .and_then(|opt| opt.value.as_str());

            let image_type = ImageType::Meme { 
                top_text: top_text.to_string(), 
                bottom_text: bottom_text.map(|s| s.to_string()) 
            };

            let image_bytes = generator.generate_image(image_type, None).await?;
            send_image_response(ctx, command, image_bytes, "meme.png").await?;
        },
        "avatar_overlay" => {
            let overlay_str = command.data.options.iter()
                .find(|opt| opt.name == "type")
                .and_then(|opt| opt.value.as_str())
                .unwrap_or("crown");

            let overlay_type = match overlay_str {
                "crown" => OverlayType::Crown,
                "hat" => OverlayType::Hat,
                "sunglasses" => OverlayType::Sunglasses,
                "frame" => OverlayType::Frame,
                "jail" => OverlayType::Jail,
                "wanted" => OverlayType::Wanted,
                _ => OverlayType::Crown,
            };

            let image_type = ImageType::AvatarOverlay { overlay_type };
            let image_bytes = generator.generate_image(image_type, None).await?;
            send_image_response(ctx, command, image_bytes, "avatar_overlay.png").await?;
        },
        "banner" => {
            let text = command.data.options.iter()
                .find(|opt| opt.name == "text")
                .and_then(|opt| opt.value.as_str())
                .unwrap_or("VoidBot");

            let style_str = command.data.options.iter()
                .find(|opt| opt.name == "style")
                .and_then(|opt| opt.value.as_str())
                .unwrap_or("cyberpunk");

            let style = match style_str {
                "gradient" => BannerStyle::Gradient,
                "neon" => BannerStyle::Neon,
                "minimal" => BannerStyle::Minimal,
                "cyberpunk" => BannerStyle::Cyberpunk,
                _ => BannerStyle::Cyberpunk,
            };

            let image_type = ImageType::Banner { 
                text: text.to_string(), 
                style 
            };
            let image_bytes = generator.generate_image(image_type, None).await?;
            send_image_response(ctx, command, image_bytes, "banner.png").await?;
        },
        "reaction" => {
            let reaction_str = command.data.options.iter()
                .find(|opt| opt.name == "type")
                .and_then(|opt| opt.value.as_str())
                .unwrap_or("think");

            let text = command.data.options.iter()
                .find(|opt| opt.name == "text")
                .and_then(|opt| opt.value.as_str());

            let reaction_type = match reaction_str {
                "triggered" => ReactionType::Triggered,
                "stonks" => ReactionType::Stonks,
                "notstonks" => ReactionType::NotStonks,
                "drake" => ReactionType::Drake,
                "surprised" => ReactionType::Surprised,
                "confused" => ReactionType::Confused,
                "think" => ReactionType::Think,
                _ => ReactionType::Think,
            };

            let image_type = ImageType::Reaction { 
                reaction_type, 
                text: text.map(|s| s.to_string()) 
            };
            let image_bytes = generator.generate_image(image_type, None).await?;
            send_image_response(ctx, command, image_bytes, "reaction.png").await?;
        },
        "user_stats" => {
            let user_id = command.data.options.iter()
                .find(|opt| opt.name == "user")
                .and_then(|opt| {
                    if let CommandDataOptionValue::User(user_id) = &opt.value {
                        Some(*user_id)
                    } else {
                        None
                    }
                })
                .unwrap_or(command.user.id);

            let image_type = ImageType::Stats { user_id: user_id.get() };
            let image_bytes = generator.generate_image(image_type, None).await?;
            send_image_response(ctx, command, image_bytes, "user_stats.png").await?;
        },
        _ => {
            command.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content("❌ Commande d'image inconnue")
                    .ephemeral(true)
            )).await?;
        }
    }

    Ok(())
}

/// Envoyer une réponse avec image générée
async fn send_image_response(
    ctx: &Context, 
    command: &CommandInteraction, 
    image_bytes: Vec<u8>, 
    filename: &str
) -> Result<()> {
    let attachment = CreateAttachment::bytes(image_bytes, filename);
    
    command.create_response(&ctx.http, CreateInteractionResponse::Message(
        CreateInteractionResponseMessage::new()
            .content("✨ Image générée avec succès !")
            .add_file(attachment)
    )).await?;

    Ok(())
}

/// Créer les commandes slash pour les images
pub fn create_image_commands() -> Vec<serenity::builder::CreateCommand> {
    vec![
        // Commande meme
        serenity::builder::CreateCommand::new("meme")
            .description("Générer un meme avec texte")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "top_text",
                    "Texte supérieur"
                ).required(true).max_length(100)
            )
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "bottom_text", 
                    "Texte inférieur (optionnel)"
                ).required(false).max_length(100)
            ),

        // Commande avatar overlay
        serenity::builder::CreateCommand::new("avatar_overlay")
            .description("Ajouter un overlay à un avatar")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "type",
                    "Type d'overlay"
                )
                .required(true)
                .add_string_choice("Couronne", "crown")
                .add_string_choice("Chapeau", "hat")
                .add_string_choice("Lunettes", "sunglasses")
                .add_string_choice("Cadre", "frame")
                .add_string_choice("Prison", "jail")
                .add_string_choice("Wanted", "wanted")
            ),

        // Commande bannière
        serenity::builder::CreateCommand::new("banner")
            .description("Créer une bannière personnalisée")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "text",
                    "Texte de la bannière"
                ).required(true).max_length(50)
            )
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "style",
                    "Style de la bannière"
                )
                .required(false)
                .add_string_choice("Gradient", "gradient")
                .add_string_choice("Néon", "neon")
                .add_string_choice("Minimal", "minimal")
                .add_string_choice("Cyberpunk", "cyberpunk")
            ),

        // Commande réaction
        serenity::builder::CreateCommand::new("reaction")
            .description("Générer une image de réaction")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "type",
                    "Type de réaction"
                )
                .required(true)
                .add_string_choice("Triggered", "triggered")
                .add_string_choice("Stonks", "stonks")
                .add_string_choice("Not Stonks", "notstonks")
                .add_string_choice("Drake", "drake")
                .add_string_choice("Surpris", "surprised")
                .add_string_choice("Confus", "confused")
                .add_string_choice("Réfléchir", "think")
            )
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::String,
                    "text",
                    "Texte personnalisé (optionnel)"
                ).required(false).max_length(100)
            ),

        // Commande stats utilisateur
        serenity::builder::CreateCommand::new("user_stats")
            .description("Afficher les statistiques d'un utilisateur en image")
            .add_option(
                serenity::builder::CreateCommandOption::new(
                    CommandOptionType::User,
                    "user",
                    "Utilisateur (par défaut: vous)"
                ).required(false)
            ),
    ]
}