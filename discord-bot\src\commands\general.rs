use anyhow::Result;
use serenity::{
    model::application::CommandInteraction,
    prelude::*,
};
use crate::{BotData, stealth::StealthManager};

pub async fn handle_ping(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    // Note: Serenity 0.12.2 doesn't have ctx.ping() method
    // Using a placeholder latency value for now
    let content = "🏓 Pong! Bot is responsive.".to_string();

    stealth.send_response(ctx, command, content, false).await?;
    Ok(())
}

pub async fn handle_help(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let help_text = r#"
**🤖 VoidBot Commands**

**🎭 Animation**
• `/animate` - Start profile animation
• `/animate_stop` - Stop all animations

**🎮 Rich Presence**
• `/rpc` - Configure Rich Presence
• `/rpc_presets` - List available presets

**💾 Backup & Restore**
• `/backup` - Backup current server
• `/restore` - Restore from backup
• `/clone` - Clone server structure

**🔧 Utilities**
• `/snipe` - Show last deleted message
• `/clear` - Clear your messages
• `/ping` - Check latency

**🎲 Fun**
• `/8ball` - Ask the magic 8-ball
• `/coinflip` - Flip a coin
• `/dice` - Roll a dice

**🛠️ Tools**
• `/translate` - Translate text
• `/calculate` - Simple calculator

**ℹ️ Info**
• `/server_info` - Server information
• `/user_info` - User information

**👻 Stealth Mode**
Current mode: **{}**
• Normal: Responses visible to everyone
• Ghost: Responses only visible to you
    "#;

    let mode_text = match stealth.get_mode() {
        voidbot_shared::StealthMode::Normal => "Normal",
        voidbot_shared::StealthMode::Ghost => "Ghost 👻",
    };

    stealth.send_response(ctx, command, format!("{}", help_text.replace("{}", mode_text)), false).await?;
    Ok(())
}

pub async fn handle_settings(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let settings_text = r#"
**⚙️ Bot Settings**

Current configuration:
• Stealth Mode: **{}**
• Animations: **Enabled**
• Sniper: **Enabled**
• Auto-delete: **Disabled**

Use the desktop app to modify these settings.
    "#;

    let mode_text = match stealth.get_mode() {
        voidbot_shared::StealthMode::Normal => "Normal",
        voidbot_shared::StealthMode::Ghost => "Ghost 👻",
    };

    stealth.send_response(ctx, command, format!("{}", settings_text.replace("{}", mode_text)), false).await?;
    Ok(())
}