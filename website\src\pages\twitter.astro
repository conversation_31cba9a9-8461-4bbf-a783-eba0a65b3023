---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';
---

<Layout title="Twitter VoidBot - Bientôt Disponible" description="Le compte Twitter officiel VoidBot sera bientôt disponible pour les actualités et annonces.">
  <Navbar />
  
  <main class="pt-16">
    <section class="min-h-screen flex items-center justify-center py-20">
      <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="text-8xl mb-8">🐦</div>
        
        <h1 class="text-4xl sm:text-5xl font-bold mb-6">
          <span class="text-white">Compte</span>
          <br>
          <span class="gradient-text">Twitter</span>
        </h1>
        
        <p class="text-xl text-gray-300 mb-8">
          Le compte Twitter officiel @VoidBotApp sera bientôt disponible !
          <br class="hidden sm:block">
          Suivez-nous pour les dernières actualités.
        </p>
        
        <div class="bg-cyan-500/10 border border-cyan-500/20 rounded-lg p-6 mb-8">
          <div class="flex items-center justify-center gap-3 mb-2">
            <div class="w-3 h-3 bg-cyan-400 rounded-full animate-pulse"></div>
            <span class="font-semibold text-cyan-400">Bientôt Actif</span>
          </div>
          <p class="text-gray-400">
            Nous préparons notre présence sur Twitter pour partager les actualités, mises à jour et astuces VoidBot.
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div class="bg-gray-900/50 rounded-lg p-4 border border-gray-800">
            <div class="text-2xl mb-2">📢</div>
            <h3 class="font-semibold text-white mb-1">Annonces</h3>
            <p class="text-gray-400 text-sm">Nouvelles versions et features</p>
          </div>
          <div class="bg-gray-900/50 rounded-lg p-4 border border-gray-800">
            <div class="text-2xl mb-2">💡</div>
            <h3 class="font-semibold text-white mb-1">Astuces</h3>
            <p class="text-gray-400 text-sm">Tips et tutoriels quotidiens</p>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/" class="btn btn-secondary text-lg px-8 py-4">
            Retour à l'Accueil
          </a>
          <a href="/support" class="btn btn-primary text-lg px-8 py-4">
            Autres Contacts
          </a>
        </div>
      </div>
    </section>
  </main>
</Layout>