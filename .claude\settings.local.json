{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(cargo check:*)", "mcp__firecrawl__firecrawl_check_crawl_status", "Bash(npm run tauri:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm ls:*)", "mcp__firecrawl__firecrawl_scrape", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(npm run build:*)", "Bash(npm run:*)", "Bash(rg:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(cargo clippy:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(rm:*)", "Bash(cargo run:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python3:*)"], "deny": []}}