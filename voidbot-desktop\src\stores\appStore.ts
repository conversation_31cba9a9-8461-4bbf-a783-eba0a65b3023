import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';

/**
 * Mode de furtivité pour les réponses des commandes Discord
 * - 'normal': Réponses visibles par tous les membres du serveur
 * - 'ghost': Réponses ephemeral (visible uniquement par l'utilisateur)
 */
export type StealthMode = 'normal' | 'ghost';

/**
 * Informations de l'utilisateur Discord connecté
 * Contient les données de base pour l'affichage et l'identification
 */
export interface DiscordUser {
  /** ID unique Discord de l'utilisateur */
  id: string;
  /** Nom d'utilisateur Discord (sans le #) */
  username: string;
  /** Discriminant numérique à 4 chiffres */
  discriminator: string;
  /** Hash de l'avatar Discord (optionnel) */
  avatar?: string;
  /** Adresse email du compte (optionnel) */
  email?: string;
}

/**
 * État global de l'application VoidBot
 * Gestion centralisée avec Zustand pour la synchronisation entre composants
 */
export interface AppState {
  // === ÉTAT DE CONNEXION BOT ===
  /** Indique si le bot Discord est connecté et opérationnel */
  isConnected: boolean;
  /** Statut de connexion du bot avec états de transition */
  botStatus: 'offline' | 'connecting' | 'online' | 'error';
  /** Informations de l'utilisateur Discord connecté (null si déconnecté) */
  discordUser: DiscordUser | null;
  
  // === CONFIGURATION ===
  /** Mode de furtivité actuel pour les réponses de commandes */
  stealthMode: StealthMode;
  /** Page actuellement affichée dans l'interface */
  currentPage: string;
  
  // === STATUT DES FONCTIONNALITÉS ===
  /** Indique si les animations Discord sont activées */
  animationsEnabled: boolean;
  /** Indique si les snipers (Nitro/Giveaway) sont actifs */
  sniperEnabled: boolean;
  
  // === STATISTIQUES D'UTILISATION ===
  stats: {
    /** Nombre total de commandes exécutées */
    commandsUsed: number;
    /** Nombre de serveurs sauvegardés */
    serversBackedUp: number;
    /** Nombre d'animations actuellement actives */
    animationsActive: number;
    /** Temps de fonctionnement en secondes */
    uptime: number;
  };
  
  // === ACTIONS DE MISE À JOUR D'ÉTAT ===
  /** Met à jour le statut de connexion */
  setConnected: (connected: boolean) => void;
  /** Met à jour le statut du bot Discord */
  setBotStatus: (status: 'offline' | 'connecting' | 'online' | 'error') => void;
  /** Change le mode de furtivité (avec synchronisation Tauri) */
  setStealthMode: (mode: StealthMode) => Promise<void>;
  /** Change la page active dans l'interface */
  setCurrentPage: (page: string) => void;
  /** Active/désactive les animations Discord */
  setAnimationsEnabled: (enabled: boolean) => void;
  /** Active/désactive les snipers */
  setSniperEnabled: (enabled: boolean) => void;
  /** Met à jour les statistiques (fusion partielle) */
  updateStats: (stats: Partial<AppState['stats']>) => void;
  
  // === ACTIONS DE CONNEXION BOT ===
  /** 
   * Connecte le bot Discord via token utilisateur (selfbot)
   * @param discordToken - Token utilisateur Discord (extrait via webview)
   * @param saveSession - Sauvegarder la session pour reconnexion auto (défaut: true)
   */
  connectBot: (discordToken: string, saveSession?: boolean) => Promise<void>;
  /** Déconnecte le bot et remet à zéro l'état */
  disconnectBot: () => Promise<void>;
  
  // === GESTION DES SESSIONS ===
  /** Vérifie s'il existe une session sauvegardée valide */
  checkSavedSession: () => Promise<boolean>;
  /** Tente une reconnexion automatique depuis la session sauvegardée */
  autoConnectFromSession: () => Promise<boolean>;
  /** Supprime la session sauvegardée */
  clearSession: () => Promise<void>;
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  isConnected: false,
  botStatus: 'offline',
  discordUser: null,
  stealthMode: 'normal',
  currentPage: 'dashboard',
  animationsEnabled: true,
  sniperEnabled: false,
  stats: {
    commandsUsed: 0,
    serversBackedUp: 0,
    animationsActive: 0,
    uptime: 0,
  },

  // Actions
  setConnected: (connected) => set({ isConnected: connected }),
  
  setBotStatus: (status) => set({ botStatus: status }),
  
  setStealthMode: async (mode) => {
    try {
      await invoke('set_stealth_mode', { mode });
      set({ stealthMode: mode });
    } catch (error) {
      console.error('Failed to set stealth mode:', error);
    }
  },
  
  setCurrentPage: (page) => set({ currentPage: page }),
  
  setAnimationsEnabled: (enabled) => set({ animationsEnabled: enabled }),
  
  setSniperEnabled: (enabled) => set({ sniperEnabled: enabled }),
  
  updateStats: (newStats) => set((state) => ({
    stats: { ...state.stats, ...newStats }
  })),
  
  /**
   * Connecte le bot Discord et gère la session utilisateur
   * 
   * Processus de connexion :
   * 1. Tentative de connexion avec le token fourni
   * 2. Récupération des informations utilisateur
   * 3. Sauvegarde optionnelle de la session (chiffrée)
   * 4. Mise à jour de l'état global
   * 
   * @param discordToken - Token utilisateur Discord (extrait via webview)
   * @param saveSession - Si true, sauvegarde la session pour reconnexion auto
   * @throws Erreur si la connexion échoue
   */
  connectBot: async (discordToken, saveSession = true) => {
    const { setBotStatus } = get();
    try {
      setBotStatus('connecting');
      
      // Valider le token Discord et obtenir les infos utilisateur
      const userInfo = await invoke('verify_discord_token', { token: discordToken });
      
      // Connecter le bot avec le token utilisateur
      const result = await invoke('connect_discord_bot', { token: discordToken });
      setBotStatus('online');
      set({ isConnected: true });
      
      // Si la connexion réussit et qu'on veut sauvegarder la session
      if (saveSession && userInfo) {
        try {
          const userData = userInfo as DiscordUser;
          set({ discordUser: userData });
          
          // Sauvegarder la session Discord (token chiffré côté Rust)
          await invoke('save_user_session', {
            userData,
            encryptedToken: discordToken,
            autoLogin: true
          });
        } catch (sessionError) {
          console.warn('Échec sauvegarde session Discord:', sessionError);
          // La connexion a réussi mais la sauvegarde a échoué, ce n'est pas grave
        }
      }
    } catch (error) {
      console.error('Échec connexion bot Discord:', error);
      setBotStatus('error');
      set({ isConnected: false });
      throw error;
    }
  },
  
  disconnectBot: async () => {
    try {
      await invoke('disconnect_discord_bot');
      set({ 
        isConnected: false, 
        botStatus: 'offline',
        discordUser: null,
        stats: {
          commandsUsed: 0,
          serversBackedUp: 0,
          animationsActive: 0,
          uptime: 0,
        }
      });
    } catch (error) {
      console.error('Failed to disconnect bot:', error);
      throw error;
    }
  },

  // Session management
  checkSavedSession: async () => {
    try {
      const hasSaved = await invoke<boolean>('has_saved_session');
      return hasSaved;
    } catch (error) {
      console.error('Failed to check saved session:', error);
      return false;
    }
  },

  /**
   * Tente une reconnexion automatique depuis une session sauvegardée
   * 
   * Processus :
   * 1. Vérifie l'existence d'une session valide
   * 2. Déchiffre et valide le token sauvegardé
   * 3. Tente la reconnexion Discord
   * 4. Met à jour l'état selon le résultat
   * 
   * @returns true si la reconnexion a réussi, false sinon
   */
  autoConnectFromSession: async () => {
    const { setBotStatus } = get();
    try {
      // Note: auto-connect géré de façon plus resiliente
      
      setBotStatus('connecting');
      const userData = await invoke<DiscordUser | null>('auto_connect_from_session');
      
      if (userData) {
        // La connexion automatique a réussi
        setBotStatus('online');
        set({ 
          isConnected: true, 
          discordUser: userData 
        });
        return true;
      } else {
        // Pas de session sauvegardée ou échec de connexion
        setBotStatus('offline');
        set({ isConnected: false });
        return false;
      }
    } catch (error) {
      console.error('Échec reconnexion auto depuis session:', error);
      setBotStatus('error');
      set({ isConnected: false });
      return false;
    }
  },

  clearSession: async () => {
    try {
      await invoke('clear_saved_session');
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  },
}));