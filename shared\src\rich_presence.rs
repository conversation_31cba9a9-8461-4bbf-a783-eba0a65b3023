use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use crate::DynamicVariableManager;

/// Gestionnaire de Rich Presence étendu
#[derive(Debug)]
pub struct RichPresenceManager {
    pub config: RichPresenceConfig,
    pub current_presence: Option<RichPresence>,
    pub presets: HashMap<String, RichPresencePreset>,
    pub history: Vec<PresenceHistoryEntry>,
    pub variable_manager: Option<DynamicVariableManager>,
}

/// Configuration Rich Presence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RichPresenceConfig {
    pub enabled: bool,
    pub auto_update_interval_seconds: u64,
    pub use_dynamic_variables: bool,
    pub max_presets: usize,
    pub max_history_entries: usize,
    pub platforms_enabled: Vec<PlatformType>,
    pub fallback_on_error: bool,
}

impl Default for RichPresenceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            auto_update_interval_seconds: 15, // Update toutes les 15 secondes
            use_dynamic_variables: true,
            max_presets: 20,
            max_history_entries: 100,
            platforms_enabled: vec![
                PlatformType::Discord,
                PlatformType::Spotify,
                PlatformType::Steam,
            ],
            fallback_on_error: true,
        }
    }
}

/// Rich Presence complet
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RichPresence {
    pub activity_type: ActivityType,
    pub name: String,
    pub details: Option<String>,
    pub state: Option<String>,
    pub timestamps: Option<ActivityTimestamps>,
    pub assets: Option<ActivityAssets>,
    pub party: Option<ActivityParty>,
    pub secrets: Option<ActivitySecrets>,
    pub buttons: Vec<ActivityButton>,
    pub platform: PlatformType,
    pub flags: u32,
    pub metadata: HashMap<String, String>,
}

/// Types d'activité étendus
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ActivityType {
    // Discord standard
    Playing,
    Streaming,
    Listening,
    Watching,
    Custom,
    Competing,
    
    // Extensions pour autres plateformes
    Gaming,      // Xbox/PlayStation
    Reading,     // Kindle/Books
    Coding,      // IDE/Development
    Browsing,    // Web browsing
    Learning,    // Educational content
    Working,     // Professional activities
}

/// Types de plateformes
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PlatformType {
    Discord,
    Spotify,
    Steam,
    Xbox,
    PlayStation,
    Crunchyroll,
    Netflix,
    YouTube,
    Twitch,
    VisualStudioCode,
    Chrome,
    Custom(String),
}

/// Timestamps d'activité
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityTimestamps {
    pub start: Option<DateTime<Utc>>,
    pub end: Option<DateTime<Utc>>,
}

/// Assets visuels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityAssets {
    pub large_image: Option<String>,
    pub large_text: Option<String>,
    pub small_image: Option<String>,
    pub small_text: Option<String>,
}

/// Informations de party/groupe
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityParty {
    pub id: Option<String>,
    pub size: Option<(u32, u32)>, // (current, max)
}

/// Secrets pour invitations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivitySecrets {
    pub join: Option<String>,
    pub spectate: Option<String>,
    pub match_secret: Option<String>,
}

/// Boutons d'action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityButton {
    pub label: String,
    pub url: String,
}

/// Preset de Rich Presence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RichPresencePreset {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub presence: RichPresence,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub use_count: u64,
    pub tags: Vec<String>,
    pub auto_variables: bool, // Utiliser les variables dynamiques
}

/// Entrée d'historique
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PresenceHistoryEntry {
    pub presence: RichPresence,
    pub started_at: DateTime<Utc>,
    pub ended_at: Option<DateTime<Utc>>,
    pub duration_seconds: Option<u64>,
    pub source: PresenceSource,
}

/// Source de la présence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PresenceSource {
    Manual,           // Défini manuellement
    Preset(String),   // Depuis un preset
    Auto(String),     // Détection automatique
    Platform(PlatformType), // Depuis une plateforme
}

impl RichPresenceManager {
    pub fn new() -> Self {
        Self {
            config: RichPresenceConfig::default(),
            current_presence: None,
            presets: HashMap::new(),
            history: Vec::new(),
            variable_manager: None,
        }
    }
    
    /// Attacher un gestionnaire de variables dynamiques
    pub fn attach_variable_manager(&mut self, manager: DynamicVariableManager) {
        self.variable_manager = Some(manager);
    }
    
    /// Définir une Rich Presence
    pub async fn set_presence(&mut self, mut presence: RichPresence) -> Result<(), RichPresenceError> {
        if !self.config.enabled {
            return Err(RichPresenceError::Disabled);
        }
        
        // Résoudre les variables dynamiques si activé
        if self.config.use_dynamic_variables {
            presence = self.resolve_presence_variables(presence).await?;
        }
        
        // Valider la présence
        self.validate_presence(&presence)?;
        
        // Terminer la présence actuelle dans l'historique
        if let Some(current) = &self.current_presence {
            self.add_to_history(current.clone(), PresenceSource::Manual);
        }
        
        // Définir la nouvelle présence
        self.current_presence = Some(presence);
        
        Ok(())
    }
    
    /// Utiliser un preset
    pub async fn use_preset(&mut self, preset_id: &str) -> Result<(), RichPresenceError> {
        let preset = self.presets.get(preset_id)
            .ok_or_else(|| RichPresenceError::PresetNotFound(preset_id.to_string()))?
            .clone();
        
        // Mettre à jour les stats du preset
        if let Some(preset_mut) = self.presets.get_mut(preset_id) {
            preset_mut.last_used = Some(Utc::now());
            preset_mut.use_count += 1;
        }
        
        let mut presence = preset.presence.clone();
        
        // Résoudre les variables si le preset l'autorise
        if preset.auto_variables && self.config.use_dynamic_variables {
            presence = self.resolve_presence_variables(presence).await?;
        }
        
        // Terminer la présence actuelle
        if let Some(current) = &self.current_presence {
            self.add_to_history(current.clone(), PresenceSource::Preset(preset_id.to_string()));
        }
        
        self.current_presence = Some(presence);
        
        Ok(())
    }
    
    /// Créer un preset
    pub fn create_preset(&mut self, name: String, description: Option<String>, presence: RichPresence, auto_variables: bool) -> Result<String, RichPresenceError> {
        if self.presets.len() >= self.config.max_presets {
            return Err(RichPresenceError::LimitReached("presets".to_string()));
        }
        
        let id = format!("preset_{}", uuid::Uuid::new_v4().to_string()[..8].to_string());
        
        let preset = RichPresencePreset {
            id: id.clone(),
            name,
            description,
            presence,
            created_at: Utc::now(),
            last_used: None,
            use_count: 0,
            tags: Vec::new(),
            auto_variables,
        };
        
        self.presets.insert(id.clone(), preset);
        
        Ok(id)
    }
    
    /// Supprimer un preset
    pub fn delete_preset(&mut self, preset_id: &str) -> Result<(), RichPresenceError> {
        if self.presets.remove(preset_id).is_none() {
            return Err(RichPresenceError::PresetNotFound(preset_id.to_string()));
        }
        
        Ok(())
    }
    
    /// Créer une présence pour une plateforme spécifique
    pub fn create_platform_presence(&self, platform: PlatformType) -> RichPresence {
        match platform {
            PlatformType::Spotify => RichPresence {
                activity_type: ActivityType::Listening,
                name: "Spotify".to_string(),
                details: Some("{spotify_track}".to_string()),
                state: Some("by {spotify_artist}".to_string()),
                timestamps: Some(ActivityTimestamps {
                    start: Some(Utc::now()),
                    end: None,
                }),
                assets: Some(ActivityAssets {
                    large_image: Some("spotify_logo".to_string()),
                    large_text: Some("Spotify".to_string()),
                    small_image: Some("play_icon".to_string()),
                    small_text: Some("{spotify_status}".to_string()),
                }),
                party: None,
                secrets: None,
                buttons: vec![
                    ActivityButton {
                        label: "Listen on Spotify".to_string(),
                        url: "https://open.spotify.com".to_string(),
                    }
                ],
                platform,
                flags: 0,
                metadata: HashMap::new(),
            },
            
            PlatformType::Xbox => RichPresence {
                activity_type: ActivityType::Gaming,
                name: "Xbox Game".to_string(),
                details: Some("Playing a game".to_string()),
                state: Some("Online".to_string()),
                timestamps: Some(ActivityTimestamps {
                    start: Some(Utc::now()),
                    end: None,
                }),
                assets: Some(ActivityAssets {
                    large_image: Some("xbox_logo".to_string()),
                    large_text: Some("Xbox".to_string()),
                    small_image: None,
                    small_text: None,
                }),
                party: None,
                secrets: None,
                buttons: vec![],
                platform,
                flags: 0,
                metadata: HashMap::new(),
            },
            
            PlatformType::PlayStation => RichPresence {
                activity_type: ActivityType::Gaming,
                name: "PlayStation Game".to_string(),
                details: Some("Playing a game".to_string()),
                state: Some("Online".to_string()),
                timestamps: Some(ActivityTimestamps {
                    start: Some(Utc::now()),
                    end: None,
                }),
                assets: Some(ActivityAssets {
                    large_image: Some("playstation_logo".to_string()),
                    large_text: Some("PlayStation".to_string()),
                    small_image: None,
                    small_text: None,
                }),
                party: None,
                secrets: None,
                buttons: vec![],
                platform,
                flags: 0,
                metadata: HashMap::new(),
            },
            
            PlatformType::Crunchyroll => RichPresence {
                activity_type: ActivityType::Watching,
                name: "Crunchyroll".to_string(),
                details: Some("Watching anime".to_string()),
                state: Some("Episode {episode_number}".to_string()),
                timestamps: Some(ActivityTimestamps {
                    start: Some(Utc::now()),
                    end: None,
                }),
                assets: Some(ActivityAssets {
                    large_image: Some("crunchyroll_logo".to_string()),
                    large_text: Some("Crunchyroll".to_string()),
                    small_image: Some("play_icon".to_string()),
                    small_text: Some("Watching".to_string()),
                }),
                party: None,
                secrets: None,
                buttons: vec![
                    ActivityButton {
                        label: "Watch on Crunchyroll".to_string(),
                        url: "https://crunchyroll.com".to_string(),
                    }
                ],
                platform,
                flags: 0,
                metadata: HashMap::new(),
            },
            
            _ => RichPresence {
                activity_type: ActivityType::Custom,
                name: format!("{:?}", platform),
                details: Some("Using application".to_string()),
                state: Some("{local_time}".to_string()),
                timestamps: Some(ActivityTimestamps {
                    start: Some(Utc::now()),
                    end: None,
                }),
                assets: None,
                party: None,
                secrets: None,
                buttons: vec![],
                platform,
                flags: 0,
                metadata: HashMap::new(),
            }
        }
    }
    
    /// Résoudre les variables dynamiques dans une présence
    async fn resolve_presence_variables(&mut self, mut presence: RichPresence) -> Result<RichPresence, RichPresenceError> {
        if let Some(var_manager) = &mut self.variable_manager {
            presence.name = var_manager.resolve_text(&presence.name).await;
            
            if let Some(details) = &presence.details {
                presence.details = Some(var_manager.resolve_text(details).await);
            }
            
            if let Some(state) = &presence.state {
                presence.state = Some(var_manager.resolve_text(state).await);
            }
            
            if let Some(assets) = &mut presence.assets {
                if let Some(large_text) = &assets.large_text {
                    assets.large_text = Some(var_manager.resolve_text(large_text).await);
                }
                if let Some(small_text) = &assets.small_text {
                    assets.small_text = Some(var_manager.resolve_text(small_text).await);
                }
            }
        }
        
        Ok(presence)
    }
    
    /// Valider une présence
    fn validate_presence(&self, presence: &RichPresence) -> Result<(), RichPresenceError> {
        if presence.name.is_empty() {
            return Err(RichPresenceError::ValidationError("Name cannot be empty".to_string()));
        }
        
        if presence.name.len() > 128 {
            return Err(RichPresenceError::ValidationError("Name too long (max 128 chars)".to_string()));
        }
        
        if let Some(details) = &presence.details {
            if details.len() > 128 {
                return Err(RichPresenceError::ValidationError("Details too long (max 128 chars)".to_string()));
            }
        }
        
        if let Some(state) = &presence.state {
            if state.len() > 128 {
                return Err(RichPresenceError::ValidationError("State too long (max 128 chars)".to_string()));
            }
        }
        
        if presence.buttons.len() > 2 {
            return Err(RichPresenceError::ValidationError("Maximum 2 buttons allowed".to_string()));
        }
        
        for button in &presence.buttons {
            if button.label.len() > 32 {
                return Err(RichPresenceError::ValidationError("Button label too long (max 32 chars)".to_string()));
            }
        }
        
        Ok(())
    }
    
    /// Ajouter à l'historique
    fn add_to_history(&mut self, presence: RichPresence, source: PresenceSource) {
        let entry = PresenceHistoryEntry {
            presence,
            started_at: Utc::now(),
            ended_at: Some(Utc::now()),
            duration_seconds: None, // TODO: Calculer la durée
            source,
        };
        
        self.history.push(entry);
        
        // Limiter la taille de l'historique
        if self.history.len() > self.config.max_history_entries {
            self.history.remove(0);
        }
    }
    
    /// Arrêter la présence actuelle
    pub fn stop_presence(&mut self) {
        if let Some(current) = self.current_presence.take() {
            self.add_to_history(current, PresenceSource::Manual);
        }
    }
    
    /// Obtenir les presets par tag
    pub fn get_presets_by_tag(&self, tag: &str) -> Vec<&RichPresencePreset> {
        self.presets.values()
            .filter(|preset| preset.tags.contains(&tag.to_string()))
            .collect()
    }
    
    /// Rechercher des presets
    pub fn search_presets(&self, query: &str) -> Vec<&RichPresencePreset> {
        let query_lower = query.to_lowercase();
        self.presets.values()
            .filter(|preset| {
                preset.name.to_lowercase().contains(&query_lower) ||
                preset.description.as_ref().map_or(false, |d| d.to_lowercase().contains(&query_lower)) ||
                preset.tags.iter().any(|tag| tag.to_lowercase().contains(&query_lower))
            })
            .collect()
    }
    
    /// Exporter les presets
    pub fn export_presets(&self) -> Result<String, RichPresenceError> {
        serde_json::to_string_pretty(&self.presets)
            .map_err(|e| RichPresenceError::SerializationError(e.to_string()))
    }
    
    /// Importer des presets
    pub fn import_presets(&mut self, json_data: &str) -> Result<usize, RichPresenceError> {
        let imported_presets: HashMap<String, RichPresencePreset> = serde_json::from_str(json_data)
            .map_err(|e| RichPresenceError::SerializationError(e.to_string()))?;
        
        let mut imported_count = 0;
        
        for (id, preset) in imported_presets {
            if self.presets.len() >= self.config.max_presets {
                break;
            }
            
            // Générer un nouvel ID si collision
            let new_id = if self.presets.contains_key(&id) {
                format!("imported_{}", uuid::Uuid::new_v4().to_string()[..8].to_string())
            } else {
                id
            };
            
            self.presets.insert(new_id, preset);
            imported_count += 1;
        }
        
        Ok(imported_count)
    }
}

/// Erreurs Rich Presence
#[derive(Debug, thiserror::Error)]
pub enum RichPresenceError {
    #[error("Rich Presence désactivé")]
    Disabled,
    
    #[error("Preset non trouvé: {0}")]
    PresetNotFound(String),
    
    #[error("Limite atteinte: {0}")]
    LimitReached(String),
    
    #[error("Erreur de validation: {0}")]
    ValidationError(String),
    
    #[error("Erreur de sérialisation: {0}")]
    SerializationError(String),
    
    #[error("Plateforme non supportée: {0:?}")]
    PlatformNotSupported(PlatformType),
    
    #[error("Erreur de variables: {0}")]
    VariableError(String),
}