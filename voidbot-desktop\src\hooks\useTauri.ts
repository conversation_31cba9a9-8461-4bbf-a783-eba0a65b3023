import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { useEffect, useState } from 'react';

/**
 * Hook pour exécuter des commandes Tauri avec gestion d'état automatique
 * 
 * Fonctionnalités :
 * - Exécution automatique au montage du composant
 * - Gestion des états loading/error/data
 * - Re-exécution manuelle via execute() ou refetch()
 * - Re-exécution automatique si les arguments changent
 * 
 * @template T - Type de retour attendu de la commande Tauri
 * @param command - Nom de la commande Tauri à exécuter
 * @param args - Arguments à passer à la commande (optionnel)
 * 
 * @returns Objet contenant :
 *   - data: Résultat de la commande (T | null)
 *   - loading: Indicateur de chargement en cours
 *   - error: Message d'erreur si échec (string | null)
 *   - execute: Fonction pour re-exécuter avec de nouveaux arguments
 *   - refetch: Alias pour execute() sans arguments
 * 
 * @example
 * ```tsx
 * // Utilisation basique
 * const { data, loading, error } = useTauriCommand<string>('get_user_info');
 * 
 * // Avec arguments
 * const { data, loading, execute } = useTauriCommand<ServerInfo>('get_server_info', { serverId: '123' });
 * 
 * // Re-exécution manuelle
 * const handleRefresh = () => execute({ serverId: '456' });
 * ```
 */
export function useTauriCommand<T>(command: string, args?: any) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Exécute la commande Tauri avec gestion d'état
   * @param newArgs - Nouveaux arguments à utiliser (optionnel)
   */
  const execute = async (newArgs?: any) => {
    try {
      setLoading(true);
      setError(null);
      const result = await invoke<T>(command, newArgs || args);
      setData(result);
    } catch (err) {
      setError(err as string);
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  // Exécution automatique au montage et quand les arguments changent
  useEffect(() => {
    execute();
  }, [command, JSON.stringify(args)]);

  return { data, loading, error, execute, refetch: execute };
}

/**
 * Hook pour écouter les événements Tauri en temps réel
 * 
 * Gestion automatique :
 * - Attachement de l'écouteur au montage
 * - Détachement au démontage (prévention fuites mémoire)
 * - Re-attachement si l'événement ou callback change
 * 
 * @template T - Type du payload de l'événement
 * @param event - Nom de l'événement Tauri à écouter
 * @param callback - Fonction appelée à chaque réception d'événement
 * 
 * @example
 * ```tsx
 * // Écoute des notifications en temps réel
 * useTauriEvent<Notification>('notification-received', (notification) => {
 *   console.log('Nouvelle notification:', notification.title);
 *   showToast(notification.message);
 * });
 * 
 * // Écoute des changements de statut du bot
 * useTauriEvent<BotStatus>('bot-status-changed', (status) => {
 *   setBotStatus(status.isOnline ? 'online' : 'offline');
 * });
 * ```
 */
export function useTauriEvent<T>(event: string, callback: (payload: T) => void) {
  useEffect(() => {
    // Attacher l'écouteur d'événement
    const unlisten = listen<T>(event, (event) => {
      callback(event.payload);
    });

    // Nettoyage : détacher l'écouteur au démontage
    return () => {
      unlisten.then(fn => fn());
    };
  }, [event, callback]);
}

/**
 * Hook pour synchroniser un état React avec une valeur côté Tauri/Rust
 * 
 * Fonctionnalités :
 * - Chargement initial automatique de la valeur depuis Rust
 * - Synchronisation bidirectionnelle (React ↔ Rust)
 * - Gestion d'état loading pendant les opérations
 * - Fonction refresh pour recharger depuis Rust
 * - Gestion d'erreurs avec propagation d'exceptions
 * 
 * @template T - Type de la valeur d'état
 * @param getCommand - Commande Tauri pour récupérer la valeur
 * @param setCommand - Commande Tauri pour mettre à jour la valeur
 * @param initialValue - Valeur initiale optionnelle avant le chargement
 * 
 * @returns Objet contenant :
 *   - value: Valeur actuelle de l'état (T | undefined)
 *   - setValue: Fonction pour mettre à jour l'état (sync React + Rust)
 *   - loading: Indicateur de chargement en cours
 *   - refresh: Fonction pour recharger la valeur depuis Rust
 * 
 * @example
 * ```tsx
 * // Synchronisation d'un paramètre utilisateur
 * const { value: theme, setValue: setTheme, loading } = useTauriState<string>(
 *   'get_user_theme',
 *   'set_user_theme',
 *   'dark' // valeur par défaut
 * );
 * 
 * // Utilisation
 * const handleThemeChange = async (newTheme: string) => {
 *   try {
 *     await setTheme(newTheme); // Met à jour côté Rust et React
 *   } catch (error) {
 *     console.error('Erreur changement thème:', error);
 *   }
 * };
 * ```
 */
export function useTauriState<T>(
  getCommand: string,
  setCommand: string,
  initialValue?: T
) {
  const [value, setValue] = useState<T | undefined>(initialValue);
  const [loading, setLoading] = useState(true);

  /**
   * Met à jour la valeur côté Rust ET React
   * @param newValue - Nouvelle valeur à assigner
   * @throws Erreur si la commande Tauri échoue
   */
  const updateValue = async (newValue: T) => {
    try {
      // Mettre à jour côté Rust en premier
      await invoke(setCommand, { value: newValue });
      // Puis mettre à jour l'état React
      setValue(newValue);
    } catch (error) {
      console.error(`Échec mise à jour état via ${setCommand}:`, error);
      throw error; // Propager l'erreur pour gestion par le composant
    }
  };

  /**
   * Recharge la valeur depuis le côté Rust
   * Utile après des modifications externes ou pour synchroniser
   */
  const refresh = async () => {
    try {
      setLoading(true);
      const result = await invoke<T>(getCommand);
      setValue(result);
    } catch (error) {
      console.error(`Échec récupération état via ${getCommand}:`, error);
      // Ne pas propager l'erreur de refresh (non critique)
    } finally {
      setLoading(false);
    }
  };

  // Chargement initial au montage du composant
  useEffect(() => {
    refresh();
  }, [getCommand]);

  return {
    value,
    setValue: updateValue,
    loading,
    refresh,
  };
}