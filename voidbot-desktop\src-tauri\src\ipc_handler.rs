use std::sync::Arc;
use voidbot_shared::{IpcMessage, Database};
use voidbot_shared::database::{EventSeverity, CommandLog};
use tracing::{info, error, warn};

/**
 * Gestionnaire IPC pour traiter les messages du bot Discord
 * 
 * Responsabilités :
 * - Réception et traitement des messages IPC du bot Discord
 * - Logging des événements et commandes dans la base de données
 * - Gestion des statistiques d'utilisation des fonctionnalités
 * - Traitement des notifications temps réel
 * 
 * Architecture :
 * - Thread-safe avec Arc<Database> partagé
 * - Traitement asynchrone des messages
 * - Gestion d'erreurs robuste avec propagation
 */
pub struct IpcHandler {
    /// Référence partagée thread-safe vers la base de données SQLite
    database: Arc<Database>,
}

impl IpcHandler {
    pub fn new(database: Arc<Database>) -> Self {
        Self { database }
    }
    
    /**
     * Traite un message IPC reçu du bot Discord
     * 
     * Dispatche le message vers le handler approprié selon son type :
     * - LogEvent: Événements généraux du bot
     * - IncrementUsage: Statistiques d'utilisation des fonctionnalités
     * - LogCommand: Exécution de commandes Discord
     * - GiveawayDetected: Détection de giveaways
     * - NitroCodeDetected: Détection de codes Nitro
     * - NotificationEvent: Événements de notification
     * 
     * @param message Message IPC à traiter
     * @return Résultat vide ou erreur formatée
     */
    pub async fn handle_message(&self, message: IpcMessage) -> Result<(), String> {
        match message {
            IpcMessage::LogEvent { event_type, severity, message, context } => {
                self.handle_log_event(event_type, severity, message, context).await
            },
            
            IpcMessage::IncrementUsage { feature, action, metadata } => {
                self.handle_increment_usage(feature, action, metadata).await
            },
            
            IpcMessage::LogCommand { 
                command_name, 
                parameters, 
                user_id, 
                guild_id, 
                channel_id, 
                success, 
                error_message, 
                execution_time_ms 
            } => {
                self.handle_log_command(
                    command_name, 
                    parameters, 
                    user_id, 
                    guild_id, 
                    channel_id, 
                    success, 
                    error_message, 
                    execution_time_ms.map(|t| t as i64)
                ).await
            },
            
            IpcMessage::GiveawayDetected { 
                guild_id, 
                channel_id, 
                message_id, 
                title, 
                description, 
                end_time, 
                requirements, 
                host 
            } => {
                self.handle_giveaway_detected(
                    guild_id, 
                    channel_id, 
                    message_id, 
                    title, 
                    description, 
                    end_time, 
                    requirements, 
                    host
                ).await
            },
            
            IpcMessage::NitroCodeDetected { 
                code, 
                channel_id, 
                guild_id, 
                claimed, 
                claim_time_ms, 
                value 
            } => {
                self.handle_nitro_code_detected(
                    code, 
                    channel_id, 
                    guild_id, 
                    claimed, 
                    claim_time_ms, 
                    value
                ).await
            },
            
            IpcMessage::NotificationEvent {
                notification,
                timestamp,
            } => {
                self.handle_notification_event_new(notification, timestamp).await
            },
            
            // Messages de contrôle (Desktop → Bot) - ignorés ici
            IpcMessage::ConfigUpdated { .. } => Ok(()),
            IpcMessage::BotControl { .. } => Ok(()),
        }
    }
    
    /**
     * Enregistre un événement général du bot dans la base de données
     * 
     * @param event_type Type d'événement (ex: "command_executed", "user_banned")
     * @param severity Niveau de sévérité (DEBUG, INFO, WARNING, ERROR, CRITICAL)
     * @param message Description de l'événement
     * @param context Informations contextuelles optionnelles (JSON, IDs, etc.)
     * @return Résultat vide ou erreur de base de données
     */
    async fn handle_log_event(
        &self, 
        event_type: String, 
        severity: String, 
        message: String, 
        context: Option<String>
    ) -> Result<(), String> {
        let severity_enum = match severity.as_str() {
            "DEBUG" => EventSeverity::Debug,
            "INFO" => EventSeverity::Info,
            "WARNING" => EventSeverity::Warning,
            "ERROR" => EventSeverity::Error,
            "CRITICAL" => EventSeverity::Critical,
            _ => EventSeverity::Info,
        };
        
        match self.database.log_event(&event_type, severity_enum, &message, context.as_deref()).await {
            Ok(_) => {
                info!("Événement logué via IPC: {} - {}", event_type, message);
                Ok(())
            },
            Err(e) => {
                error!("Erreur lors du log via IPC: {}", e);
                Err(format!("Erreur DB: {}", e))
            }
        }
    }
    
    async fn handle_increment_usage(
        &self, 
        feature: String, 
        action: String, 
        metadata: Option<String>
    ) -> Result<(), String> {
        let data = serde_json::json!({
            "action": action,
            "metadata": metadata
        }).to_string();
        
        match self.database.log_event(
            &feature,
            EventSeverity::Info,
            &data,
            None
        ).await {
            Ok(_) => {
                info!("Usage incrémenté via IPC: {}/{}", feature, action);
                Ok(())
            },
            Err(e) => {
                error!("Erreur lors de l'incrémentation via IPC: {}", e);
                Err(format!("Erreur DB: {}", e))
            }
        }
    }
    
    async fn handle_log_command(
        &self,
        command_name: String,
        parameters: Option<String>,
        user_id: Option<String>,
        guild_id: Option<String>,
        channel_id: Option<String>,
        success: bool,
        error_message: Option<String>,
        execution_time_ms: Option<i64>,
    ) -> Result<(), String> {
        let command_name_clone = command_name.clone();
        match self.database.log_command(CommandLog {
            command_name,
            parameters,
            user_id,
            guild_id,
            channel_id,
            success,
            error_message,
            execution_time_ms,
            created_at: chrono::Utc::now(),
        }).await {
            Ok(_) => {
                info!("Commande loggée via IPC: {} (succès: {})", command_name_clone, success);
                Ok(())
            },
            Err(e) => {
                error!("Erreur lors du log de commande via IPC: {}", e);
                Err(format!("Erreur DB: {}", e))
            }
        }
    }
    
    /**
     * Traite la détection d'un nouveau giveaway Discord
     * 
     * Fonctionnalités :
     * - Enregistre l'événement de détection avec tous les détails
     * - Incrémente les statistiques du Giveaway Joiner
     * - Log structuré avec contexte pour analyse ultérieure
     * 
     * @param guild_id ID du serveur Discord
     * @param channel_id ID du canal où le giveaway a été détecté
     * @param message_id ID du message du giveaway
     * @param title Titre du giveaway
     * @param description Description optionnelle du giveaway
     * @param end_time Date/heure de fin du giveaway (si détectée)
     * @param requirements Liste des conditions de participation
     * @param host Nom de l'organisateur du giveaway
     * @return Résultat vide ou erreur de traitement
     */
    async fn handle_giveaway_detected(
        &self,
        guild_id: String,
        channel_id: String,
        message_id: String,
        title: String,
        _description: Option<String>,
        end_time: Option<chrono::DateTime<chrono::Utc>>,
        requirements: Vec<String>,
        host: String,
    ) -> Result<(), String> {
        // Loguer l'événement de détection
        let event_message = format!(
            "Giveaway détecté: {} dans {}/{} par {}", 
            title, guild_id, channel_id, host
        );
        
        let context = format!(
            "message_id:{}, requirements:{}, end_time:{:?}", 
            message_id, 
            requirements.join(","), 
            end_time
        );
        
        match self.database.log_event(
            "giveaway_detected", 
            EventSeverity::Info, 
            &event_message, 
            Some(&context)
        ).await {
            Ok(_) => {
                info!("Giveaway détecté et logué: {}", title);
                
                // Incrémenter les stats
                let _ = self.database.increment_usage_stat(
                    "giveaway_joiner", 
                    "detection", 
                    Some(&guild_id)
                ).await;
                
                Ok(())
            },
            Err(e) => {
                error!("Erreur lors du log de giveaway: {}", e);
                Err(format!("Erreur DB: {}", e))
            }
        }
    }
    
    /**
     * Traite la détection d'un code Nitro Discord
     * 
     * Fonctionnalités :
     * - Enregistre la détection avec anonymisation partielle du code
     * - Distingue les codes récupérés des codes ratés
     * - Tracking des performances (temps de réaction)
     * - Statistiques pour le Nitro Sniper
     * 
     * @param code Code Nitro détecté (sera tronqué dans les logs)
     * @param channel_id Canal où le code a été détecté
     * @param guild_id Serveur optionnel (peut être un DM)
     * @param claimed Indique si le code a été récupéré avec succès
     * @param claim_time_ms Temps de réaction en millisecondes
     * @param value Valeur du code Nitro (ex: "1 mois", "3 mois")
     * @return Résultat vide ou erreur de traitement
     */
    async fn handle_nitro_code_detected(
        &self,
        code: String,
        channel_id: String,
        guild_id: Option<String>,
        claimed: bool,
        claim_time_ms: Option<u64>,
        value: Option<String>,
    ) -> Result<(), String> {
        let event_message = format!(
            "Code Nitro {} dans {} (récupéré: {})", 
            if code.len() > 8 { &code[..8] } else { &code }, 
            channel_id, 
            claimed
        );
        
        let context = format!(
            "guild_id:{:?}, claim_time_ms:{:?}, value:{:?}", 
            guild_id, claim_time_ms, value
        );
        
        let severity = if claimed { 
            EventSeverity::Info 
        } else { 
            EventSeverity::Warning 
        };
        
        match self.database.log_event(
            "nitro_code_detected", 
            severity, 
            &event_message, 
            Some(&context)
        ).await {
            Ok(_) => {
                info!("Code Nitro détecté et logué: récupéré={}", claimed);
                
                // Incrémenter les stats
                let action = if claimed { "successful_claim" } else { "detection" };
                let _ = self.database.increment_usage_stat(
                    "nitro_sniper", 
                    action, 
                    guild_id.as_deref()
                ).await;
                
                Ok(())
            },
            Err(e) => {
                error!("Erreur lors du log de code Nitro: {}", e);
                Err(format!("Erreur DB: {}", e))
            }
        }
    }

    /// Gérer un événement de notification (nouvelle version)
    async fn handle_notification_event_new(
        &self,
        notification: voidbot_shared::VoidNotification,
        timestamp: chrono::DateTime<chrono::Utc>,
    ) -> Result<(), String> {
        info!("📢 Notification reçue: {} - {}", notification.title, notification.message);

        // Envoyer la notification à l'interface utilisateur
        if let Err(e) = self.app_handle.emit("notification", &notification) {
            error!("Erreur lors de l'émission de notification vers l'interface: {}", e);
            return Err(format!("Échec émission notification: {}", e));
        }

        // Sauvegarder en base de données si nécessaire
        // TODO: Implémenter la sauvegarde en base

        Ok(())
    }

    async fn handle_notification_event(
        &self,
        notification_type: String,
        title: String,
        message: String,
        guild_id: Option<String>,
        channel_id: Option<String>,
        user_id: Option<String>,
        metadata: Option<String>,
    ) -> Result<(), String> {
        let event_message = format!("{}: {}", title, message);
        
        let context = format!(
            "guild_id:{:?}, channel_id:{:?}, user_id:{:?}, metadata:{:?}",
            guild_id, channel_id, user_id, metadata
        );
        
        match self.database.log_event(
            &format!("notification_{}", notification_type),
            EventSeverity::Info,
            &event_message,
            Some(&context)
        ).await {
            Ok(_) => {
                info!("Notification loggée: {} - {}", notification_type, title);
                
                // Incrémenter les stats de notifications
                let _ = self.database.increment_usage_stat(
                    "notifications", 
                    &notification_type, 
                    guild_id.as_deref()
                ).await;
                
                Ok(())
            },
            Err(e) => {
                error!("Erreur lors du log de notification: {}", e);
                Err(format!("Erreur DB: {}", e))
            }
        }
    }
}

/**
 * Lance le processeur IPC en arrière-plan pour traiter les messages du bot
 * 
 * Architecture :
 * - Boucle infinie de traitement des messages
 * - Gestion d'erreurs non-bloquante (logs + continue)
 * - Utilise un canal MPSC unbounded pour les performances
 * - Thread séparé pour ne pas bloquer l'interface
 * 
 * Cycle de vie :
 * 1. Démarrage automatique au lancement de l'app
 * 2. Traitement continu des messages IPC
 * 3. Arrêt propre à la fermeture de l'app
 * 
 * @param database Référence partagée vers la base de données
 * @param receiver Récepteur de messages IPC du bot Discord
 */
pub async fn start_ipc_processor(
    database: Arc<Database>,
    mut receiver: tokio::sync::mpsc::UnboundedReceiver<IpcMessage>,
) {
    let handler = IpcHandler::new(database);
    
    info!("Processeur IPC démarré");
    
    while let Some(message) = receiver.recv().await {
        if let Err(e) = handler.handle_message(message).await {
            warn!("Erreur lors du traitement IPC: {}", e);
        }
    }
    
    warn!("Processeur IPC arrêté");
}