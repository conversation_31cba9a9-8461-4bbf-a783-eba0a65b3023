@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --accent: #6366f1;
    --accent-secondary: #8b5cf6;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    --border: rgba(255, 255, 255, 0.1);
    --glow: rgba(99, 102, 241, 0.5);
  }

  * {
    box-sizing: border-box;
  }

  body {
    @apply bg-voidbot-dark text-voidbot-primary;
    font-family: 'Inter', system-ui, sans-serif;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-voidbot-darker;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-voidbot-primary rounded-full;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-voidbot-secondary;
  }
}

@layer components {
  .titlebar {
    @apply fixed top-0 left-0 right-0 h-8 bg-voidbot-darker;
    @apply flex items-center justify-between px-4 z-50;
    -webkit-app-region: drag;
    border-bottom: 1px solid var(--border);
  }

  .titlebar-title {
    @apply text-sm font-medium text-voidbot-primary;
    -webkit-app-region: no-drag;
  }

  .titlebar-buttons {
    @apply flex items-center gap-2;
    -webkit-app-region: no-drag;
  }

  .titlebar-button {
    @apply w-6 h-6 rounded flex items-center justify-center;
    @apply hover:bg-white/10 transition-colors duration-200;
    @apply text-xs text-voidbot-secondary hover:text-voidbot-primary;
  }

  .titlebar-button.close:hover {
    @apply bg-red-500 text-white;
  }

  .sidebar {
    @apply fixed left-0 top-8 bottom-0 w-64 bg-voidbot-darker;
    @apply border-r overflow-y-auto;
    border-color: var(--border);
  }

  .sidebar-header {
    @apply p-6 border-b;
    border-color: var(--border);
  }

  .sidebar-logo {
    @apply text-2xl font-bold bg-gradient-to-r from-voidbot-primary to-voidbot-secondary;
    @apply bg-clip-text text-transparent;
  }

  .sidebar-nav {
    @apply p-4 space-y-2;
  }

  .nav-item {
    @apply flex items-center gap-3 p-3 rounded-lg text-voidbot-secondary;
    @apply hover:bg-voidbot-primary/10 hover:text-voidbot-primary;
    @apply transition-all duration-200 cursor-pointer;
  }

  .nav-item.active {
    @apply bg-voidbot-primary/20 text-voidbot-primary;
    @apply shadow-lg shadow-voidbot-primary/20;
  }

  .nav-icon {
    @apply w-5 h-5;
  }

  .content {
    @apply ml-64 mt-8 h-screen overflow-y-auto;
    @apply bg-gradient-to-br from-voidbot-dark to-voidbot-darker;
  }

  .page-container {
    @apply p-6 min-h-full;
  }

  .page-header {
    @apply mb-8;
  }

  .page-title {
    @apply text-3xl font-bold text-voidbot-primary mb-2;
  }

  .page-subtitle {
    @apply text-voidbot-secondary;
  }

  .card {
    @apply bg-voidbot-darker/50 backdrop-blur-xl rounded-xl border p-6 shadow-lg;
    border-color: var(--border);
  }

  .card-hover {
    @apply hover:border-voidbot-primary/50 transition-all duration-300;
    @apply hover:shadow-xl hover:shadow-voidbot-primary/10;
  }

  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-voidbot-primary text-white hover:bg-voidbot-primary/80;
    @apply shadow-lg shadow-voidbot-primary/25;
  }

  .btn-secondary {
    @apply bg-voidbot-secondary text-white hover:bg-voidbot-secondary/80;
    @apply shadow-lg shadow-voidbot-secondary/25;
  }

  .btn-ghost {
    @apply bg-transparent text-voidbot-secondary hover:bg-voidbot-primary/10;
    @apply hover:text-voidbot-primary border;
    border-color: var(--border);
  }

  .status-indicator {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm;
  }

  .status-online {
    @apply bg-green-500/20 text-green-400 border border-green-500/30;
  }

  .status-offline {
    @apply bg-red-500/20 text-red-400 border border-red-500/30;
  }

  .status-ghost {
    @apply bg-purple-500/20 text-purple-400 border border-purple-500/30;
  }

  .glow-effect {
    @apply animate-glow;
  }

  .float-effect {
    @apply animate-float;
  }

  .mode-card {
    @apply p-6 rounded-xl border-2 transition-all duration-300 cursor-pointer;
    @apply hover:scale-105 hover:shadow-xl;
  }

  .mode-card.active {
    @apply border-voidbot-primary bg-voidbot-primary/10;
    @apply shadow-lg shadow-voidbot-primary/20;
  }

  .mode-card.inactive {
    @apply border bg-voidbot-darker/50;
    border-color: var(--border);
  }

  .mode-card.inactive:hover {
    border-color: rgba(99, 102, 241, 0.5);
  }

  .feature-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  .stats-grid {
    @apply grid grid-cols-2 lg:grid-cols-4 gap-4;
  }

  .stat-card {
    @apply bg-voidbot-darker/30 rounded-lg p-4 text-center border;
    border-color: var(--border);
  }

  .stat-value {
    @apply text-2xl font-bold text-voidbot-primary;
  }

  .stat-label {
    @apply text-sm text-voidbot-secondary mt-1;
  }

  .input-group {
    @apply space-y-2;
  }

  .input-label {
    @apply text-sm font-medium text-voidbot-primary;
  }

  .input-field {
    @apply w-full px-3 py-2 bg-voidbot-darker border rounded-lg text-voidbot-primary;
    @apply focus:outline-none focus:ring-2 focus:ring-voidbot-primary focus:border-transparent;
    border-color: var(--border);
    placeholder-color: var(--text-secondary);
  }

  .toggle {
    @apply relative inline-flex h-6 w-11 items-center rounded-full;
    @apply transition-colors duration-200 cursor-pointer;
  }

  .toggle.enabled {
    @apply bg-voidbot-primary;
  }

  .toggle.disabled {
    background-color: var(--border);
  }

  .toggle-thumb {
    @apply inline-block h-4 w-4 transform rounded-full bg-white;
    @apply transition-transform duration-200;
  }

  .toggle.enabled .toggle-thumb {
    @apply translate-x-6;
  }

  .toggle.disabled .toggle-thumb {
    @apply translate-x-1;
  }
}