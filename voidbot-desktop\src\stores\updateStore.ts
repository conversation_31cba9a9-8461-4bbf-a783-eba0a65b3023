import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

/**
 * Configuration des mises à jour automatiques
 */
export interface UpdateConfig {
  /** Vérification automatique des mises à jour */
  auto_check: boolean;
  /** Téléchargement automatique des mises à jour */
  auto_download: boolean;
  /** Installation automatique des mises à jour */
  auto_install: boolean;
  /** Intervalle de vérification en heures */
  check_interval_hours: number;
  /** Inclure les versions bêta */
  beta_updates: boolean;
  /** Timestamp de la dernière vérification */
  last_check: string | null;
}

/**
 * Informations sur une mise à jour disponible
 */
export interface UpdateInfo {
  /** Version de la mise à jour */
  version: string;
  /** Date de release */
  release_date: string;
  /** Notes de mise à jour */
  notes: string;
  /** URL de téléchargement */
  download_url: string;
  /** Signature de sécurité */
  signature: string;
  /** Taille en bytes */
  size: number;
}

/**
 * Statut actuel du processus de mise à jour
 */
export interface UpdateStatus {
  /** En cours de vérification */
  checking: boolean;
  /** Mise à jour disponible */
  available: boolean;
  /** En cours de téléchargement */
  downloading: boolean;
  /** En cours d'installation */
  installing: boolean;
  /** Message d'erreur s'il y en a un */
  error: string | null;
  /** Progression du téléchargement (0-100) */
  progress: number;
  /** Informations de la mise à jour */
  update_info: UpdateInfo | null;
}

/**
 * Store Zustand pour la gestion des mises à jour
 */
interface UpdateStore {
  // État
  config: UpdateConfig;
  status: UpdateStatus;
  currentVersion: string;
  isInitialized: boolean;
  
  // Actions
  initialize: () => Promise<void>;
  checkForUpdates: () => Promise<void>;
  downloadAndInstall: () => Promise<void>;
  cancelUpdate: () => Promise<void>;
  restartApplication: () => Promise<void>;
  updateConfig: (newConfig: Partial<UpdateConfig>) => Promise<void>;
  setStatus: (status: Partial<UpdateStatus>) => void;
  setConfig: (config: UpdateConfig) => void;
  
  // Utilitaires
  formatFileSize: (bytes: number) => string;
  isUpdateRequired: () => boolean;
  getTimeSinceLastCheck: () => string | null;
}

export const useUpdateStore = create<UpdateStore>((set, get) => ({
  // État initial
  config: {
    auto_check: true,
    auto_download: false,
    auto_install: false,
    check_interval_hours: 24,
    beta_updates: false,
    last_check: null,
  },
  
  status: {
    checking: false,
    available: false,
    downloading: false,
    installing: false,
    error: null,
    progress: 0,
    update_info: null,
  },
  
  currentVersion: '1.0.0',
  isInitialized: false,
  
  // Actions
  initialize: async () => {
    try {
      // Charger la configuration actuelle
      const config = await invoke<UpdateConfig>('get_update_config');
      set({ config });
      
      // Charger le statut actuel
      const status = await invoke<UpdateStatus>('get_update_status');
      set({ status });
      
      // Charger la version actuelle
      const currentVersion = await invoke<string>('get_current_version');
      set({ currentVersion });
      
      // Écouter les événements de mise à jour
      await get().setupEventListeners();
      
      set({ isInitialized: true });
      
      console.log('✅ UpdateStore initialisé');
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation UpdateStore:', error);
      set({ 
        status: { 
          ...get().status, 
          error: 'Erreur d\'initialisation: ' + String(error) 
        } 
      });
    }
  },
  
  checkForUpdates: async () => {
    try {
      set({ 
        status: { 
          ...get().status, 
          checking: true, 
          error: null 
        } 
      });
      
      const status = await invoke<UpdateStatus>('check_for_updates');
      set({ status });
      
      console.log('🔄 Vérification des mises à jour terminée:', status);
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des mises à jour:', error);
      set({ 
        status: { 
          ...get().status, 
          checking: false,
          error: String(error) 
        } 
      });
    }
  },
  
  downloadAndInstall: async () => {
    try {
      const currentStatus = get().status;
      if (!currentStatus.available || currentStatus.update_info === null) {
        throw new Error('Aucune mise à jour disponible');
      }
      
      set({ 
        status: { 
          ...currentStatus, 
          downloading: true, 
          error: null,
          progress: 0
        } 
      });
      
      await invoke('download_and_install_update');
      
      console.log('✅ Mise à jour téléchargée et installée avec succès');
    } catch (error) {
      console.error('❌ Erreur lors du téléchargement/installation:', error);
      set({ 
        status: { 
          ...get().status, 
          downloading: false,
          installing: false,
          error: String(error) 
        } 
      });
    }
  },
  
  cancelUpdate: async () => {
    try {
      await invoke('cancel_update');
      
      set({ 
        status: { 
          ...get().status, 
          downloading: false,
          installing: false,
          progress: 0,
          error: 'Mise à jour annulée'
        } 
      });
      
      console.log('🚫 Mise à jour annulée');
    } catch (error) {
      console.error('❌ Erreur lors de l\'annulation:', error);
    }
  },
  
  restartApplication: async () => {
    try {
      await invoke('restart_application');
    } catch (error) {
      console.error('❌ Erreur lors du redémarrage:', error);
    }
  },
  
  updateConfig: async (newConfig: Partial<UpdateConfig>) => {
    try {
      const updatedConfig = { ...get().config, ...newConfig };
      
      await invoke('set_update_config', { config: updatedConfig });
      set({ config: updatedConfig });
      
      console.log('⚙️ Configuration mise à jour:', updatedConfig);
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour de la configuration:', error);
    }
  },
  
  setStatus: (newStatus: Partial<UpdateStatus>) => {
    set({ 
      status: { 
        ...get().status, 
        ...newStatus 
      } 
    });
  },
  
  setConfig: (config: UpdateConfig) => {
    set({ config });
  },
  
  // Utilitaires
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },
  
  isUpdateRequired: (): boolean => {
    const { status } = get();
    return status.available && status.update_info !== null;
  },
  
  getTimeSinceLastCheck: (): string | null => {
    const { config } = get();
    
    if (!config.last_check) {
      return null;
    }
    
    const lastCheck = new Date(config.last_check);
    const now = new Date();
    const diffMs = now.getTime() - lastCheck.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else {
      return 'à l\'instant';
    }
  },
  
  // Configuration des écouteurs d'événements (fonction interne)
  setupEventListeners: async () => {
    try {
      // Événement : Vérification démarrée
      await listen('update-check-started', () => {
        set({ 
          status: { 
            ...get().status, 
            checking: true, 
            error: null 
          } 
        });
      });
      
      // Événement : Mise à jour disponible
      await listen<UpdateInfo>('update-available', (event) => {
        set({ 
          status: { 
            ...get().status, 
            checking: false,
            available: true,
            update_info: event.payload
          } 
        });
      });
      
      // Événement : Pas de mise à jour
      await listen('update-not-available', () => {
        set({ 
          status: { 
            ...get().status, 
            checking: false,
            available: false,
            update_info: null
          } 
        });
      });
      
      // Événement : Erreur de vérification
      await listen<string>('update-error', (event) => {
        set({ 
          status: { 
            ...get().status, 
            checking: false,
            error: event.payload
          } 
        });
      });
      
      // Événement : Téléchargement démarré
      await listen('update-download-started', () => {
        set({ 
          status: { 
            ...get().status, 
            downloading: true,
            progress: 0,
            error: null
          } 
        });
      });
      
      // Événement : Progression du téléchargement
      await listen<number>('update-download-progress', (event) => {
        set({ 
          status: { 
            ...get().status, 
            progress: event.payload
          } 
        });
      });
      
      // Événement : Téléchargement terminé
      await listen('update-download-finished', () => {
        set({ 
          status: { 
            ...get().status, 
            downloading: false,
            progress: 100
          } 
        });
      });
      
      // Événement : Installation démarrée
      await listen('update-install-started', () => {
        set({ 
          status: { 
            ...get().status, 
            installing: true
          } 
        });
      });
      
      // Événement : Installation terminée
      await listen('update-installed', () => {
        set({ 
          status: { 
            ...get().status, 
            installing: false
          } 
        });
      });
      
      // Événement : Erreur de téléchargement
      await listen<string>('update-download-error', (event) => {
        set({ 
          status: { 
            ...get().status, 
            downloading: false,
            error: event.payload
          } 
        });
      });
      
      // Événement : Erreur d'installation
      await listen<string>('update-install-error', (event) => {
        set({ 
          status: { 
            ...get().status, 
            installing: false,
            error: event.payload
          } 
        });
      });
      
      console.log('🎧 Écouteurs d\'événements de mise à jour configurés');
    } catch (error) {
      console.error('❌ Erreur lors de la configuration des écouteurs:', error);
    }
  },
}));