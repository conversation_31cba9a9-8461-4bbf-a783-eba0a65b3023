# 🧪 Guide de Test Local VoidBot

## 🚀 **Démarrage rapide du test complet**

### **Option 1 : Script automatique (recommandé)**
```bash
cd /mnt/c/Users/<USER>/Desktop/VoidBot
./start_test_local.sh
```

### **Option 2 : Démarrage manuel**

#### **Terminal 1 : Bot Discord (Backend)**
```bash
cd discord-bot
cargo run
```

#### **Terminal 2 : Interface <PERSON> (Frontend)**
```bash
cd voidbot-desktop  
npm run tauri dev
```

---

## 🎯 **Ce qui fonctionne en mode test**

### ✅ **Interface complète**
- **Login Screen** - Connexion simulée Discord
- **Dashboard** - Navigation et statuts
- **Toutes les pages** - Commands, Notifications, Trolls, Settings, etc.
- **Thèmes** - Changement de thème temps réel
- **Animations** - Transitions fluides

### ✅ **Fonctionnalités testables**
- **Authentification webview** - Simulée avec délais réalistes
- **Connexion bot** - Status connecté avec utilisateur factice
- **Base de données** - SQLite locale fonctionnelle
- **Notifications** - Interface et configuration
- **Trolls** - Interface de contrôle 
- **Auto-commands** - Configuration et paramètres
- **Rate limiting** - Dashboard et statistiques
- **Image generation** - Configuration des paramètres

### ✅ **Simulation réaliste**
- **Délais API** - Simule les temps de réponse Discord
- **États de chargement** - Spinners et transitions
- **Gestion d'erreurs** - Tests de robustesse
- **Persistance données** - Sauvegarde configurations

---

## ⚠️ **Limitations mode test**

### **Discord non connecté**
- Pas de vraies commandes Discord
- Pas de vrais messages/serveurs
- Webview ne s'ouvre pas en WSL2

### **Seulement simulation UX/UI**
- Parfait pour tester l'interface
- Idéal pour développement frontend
- Validation de la navigation

---

## 📱 **URLs de test**

- **Interface principale** : Se lance automatiquement (Tauri)
- **Frontend dev** : http://localhost:5173
- **Backend status** : http://localhost:3001/status
- **Base de données** : `./voidbot_test.db`

---

## 🛑 **Arrêter les tests**

### **Script automatique**
```bash
Ctrl+C  # Dans le terminal du script
```

### **Manuel**
```bash
# Arrêter les processus cargo/npm individuellement
Ctrl+C  # Dans chaque terminal
```

---

## 🐛 **Dépannage**

### **Compilation erreur**
```bash
# Nettoyer et recompiler
cd discord-bot && cargo clean && cargo build
cd voidbot-desktop && rm -rf node_modules && npm install
```

### **Port occupé**
```bash
# Vérifier les ports
netstat -tulpn | grep :3001
netstat -tulpn | grep :5173

# Tuer les processus si nécessaire
pkill -f "cargo run"
pkill -f "npm run tauri"
```

### **Base de données corrompue**
```bash
# Supprimer et recréer
rm discord-bot/voidbot_test.db
rm voidbot-desktop/voidbot.db
```

---

## 🎉 **Test complet workflow**

1. **Lancer script** : `./start_test_local.sh`
2. **Interface s'ouvre** automatiquement
3. **Cliquer "Connexion Discord Automatique"**
4. **Voir simulation** avec délais réalistes
5. **Naviguer dashboard** pour tester toutes les fonctionnalités
6. **Tester configurations** : Trolls, Notifications, Settings
7. **Vérifier persistance** : Redémarrer et voir si configs restent
8. **Arrêter avec Ctrl+C**

---

## 💡 **Prochaines étapes pour vrai test**

### **Test Windows natif**
- Installer sur Windows (pas WSL2)
- Webview Discord fonctionnera
- Token extraction réelle possible

### **Test avec vrai token**
- Modifier `.env` avec vrai token Discord
- Désactiver `VOIDBOT_TEST_MODE=false`
- Test fonctionnalités Discord réelles

### **Build production**
```bash
cd voidbot-desktop
npm run tauri build  # Crée l'exécutable final
```

---

**Ce mode test permet de valider 90% de VoidBot sans avoir besoin d'un token Discord réel !** 🎯