#!/bin/bash

# Script de développement VoidBot
echo "🛠️ Mode développement VoidBot"

case "$1" in
  "bot")
    echo "🤖 Lancement du bot Discord local..."
    cd discord-bot && cargo run
    ;;
  "web") 
    echo "🌐 Lancement du frontend web..."
    cd voidbot-desktop && npm run dev
    ;;
  "both")
    echo "🚀 Lancement bot + frontend..."
    # Terminal 1: Bot Discord
    gnome-terminal -- bash -c "cd discord-bot && cargo run; exec bash" &
    # Terminal 2: Frontend
    gnome-terminal -- bash -c "cd voidbot-desktop && npm run dev; exec bash" &
    ;;
  "deploy")
    echo "🚀 Déploiement sur Railway..."
    git add .
    read -p "📝 Message de commit: " commit_msg
    git commit -m "$commit_msg"
    git push origin main
    echo "✅ Push effectué, déploiement automatique en cours..."
    ;;
  "build")
    echo "🏗️ Build de l'exécutable desktop..."
    ./scripts/build-desktop.sh
    ;;
  *)
    echo "Usage: ./scripts/dev.sh [bot|web|both|deploy|build]"
    echo ""
    echo "  bot     - Lance le bot Discord local"
    echo "  web     - Lance le frontend web local" 
    echo "  both    - Lance bot + frontend"
    echo "  deploy  - Commit + push → déploiement auto"
    echo "  build   - Build exécutable desktop"
    ;;
esac