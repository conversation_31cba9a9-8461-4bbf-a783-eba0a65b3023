import { useState, useCallback, useRef, useEffect } from 'react';

interface ContextMenuState {
  isVisible: boolean;
  x: number;
  y: number;
  data?: any;
}

interface UseContextMenuReturn {
  contextMenu: ContextMenuState;
  showContextMenu: (event: React.MouseEvent, data?: any) => void;
  hideContextMenu: () => void;
  contextMenuRef: React.RefObject<HTMLDivElement>;
}

export const useContextMenu = (): UseContextMenuReturn => {
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    isVisible: false,
    x: 0,
    y: 0,
    data: undefined
  });
  
  const contextMenuRef = useRef<HTMLDivElement>(null);

  const showContextMenu = useCallback((event: React.MouseEvent, data?: any) => {
    event.preventDefault();
    event.stopPropagation();

    const { clientX, clientY } = event;
    
    setContextMenu({
      isVisible: true,
      x: clientX,
      y: clientY,
      data
    });
  }, []);

  const hideContextMenu = useCallback(() => {
    setContextMenu(prev => ({
      ...prev,
      isVisible: false
    }));
  }, []);

  // Gérer les clics en dehors du menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        hideContextMenu();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        hideContextMenu();
      }
    };

    if (contextMenu.isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [contextMenu.isVisible, hideContextMenu]);

  return {
    contextMenu,
    showContextMenu,
    hideContextMenu,
    contextMenuRef
  };
};
