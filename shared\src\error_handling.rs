use serde::{Deserialize, Serialize};
use std::fmt;
use chrono::{DateTime, Utc};

/// Système d'error handling uniforme pour VoidBot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VoidBotError {
    pub error_type: ErrorType,
    pub code: String,
    pub message: String,
    pub details: Option<String>,
    pub context: Option<ErrorContext>,
    pub timestamp: DateTime<Utc>,
    pub severity: ErrorSeverity,
    pub recoverable: bool,
    pub suggested_action: Option<String>,
}

/// Types d'erreurs
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ErrorType {
    // Erreurs Discord
    DiscordApi,
    DiscordAuth,
    DiscordRateLimit,
    DiscordToken,
    
    // Erreurs de réseau
    Network,
    Timeout,
    Connection,
    
    // Erreurs de base de données
    Database,
    Migration,
    Transaction,
    
    // Erreurs de sécurité
    Security,
    Encryption,
    Validation,
    Authentication,
    
    // Erreurs système
    System,
    FileSystem,
    Permission,
    
    // Erreurs de configuration
    Config,
    InvalidInput,
    MissingField,
    
    // Erreurs métier
    Business,
    RateLimit,
    Quota,
    
    // Erreurs internes
    Internal,
    Unknown,
}

/// Contexte d'erreur
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub component: String,
    pub function: Option<String>,
    pub user_id: Option<String>,
    pub guild_id: Option<String>,
    pub channel_id: Option<String>,
    pub request_id: Option<String>,
    pub additional_data: std::collections::HashMap<String, String>,
}

/// Sévérité d'erreur
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
    Fatal,
}

/// Builder pour créer des erreurs
pub struct ErrorBuilder {
    error_type: ErrorType,
    code: String,
    message: String,
    details: Option<String>,
    context: Option<ErrorContext>,
    severity: ErrorSeverity,
    recoverable: bool,
    suggested_action: Option<String>,
}

impl ErrorBuilder {
    pub fn new(error_type: ErrorType, code: impl Into<String>, message: impl Into<String>) -> Self {
        Self {
            error_type,
            code: code.into(),
            message: message.into(),
            details: None,
            context: None,
            severity: ErrorSeverity::Error,
            recoverable: true,
            suggested_action: None,
        }
    }
    
    pub fn details(mut self, details: impl Into<String>) -> Self {
        self.details = Some(details.into());
        self
    }
    
    pub fn context(mut self, context: ErrorContext) -> Self {
        self.context = Some(context);
        self
    }
    
    pub fn severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }
    
    pub fn recoverable(mut self, recoverable: bool) -> Self {
        self.recoverable = recoverable;
        self
    }
    
    pub fn suggested_action(mut self, action: impl Into<String>) -> Self {
        self.suggested_action = Some(action.into());
        self
    }
    
    pub fn build(self) -> VoidBotError {
        VoidBotError {
            error_type: self.error_type,
            code: self.code,
            message: self.message,
            details: self.details,
            context: self.context,
            timestamp: Utc::now(),
            severity: self.severity,
            recoverable: self.recoverable,
            suggested_action: self.suggested_action,
        }
    }
}

impl VoidBotError {
    /// Créer une erreur Discord API
    pub fn discord_api(code: impl Into<String>, message: impl Into<String>) -> Self {
        ErrorBuilder::new(ErrorType::DiscordApi, code, message)
            .severity(ErrorSeverity::Warning)
            .suggested_action("Vérifiez votre connexion Discord et réessayez")
            .build()
    }
    
    /// Créer une erreur d'authentification Discord
    pub fn discord_auth(message: impl Into<String>) -> Self {
        ErrorBuilder::new(ErrorType::DiscordAuth, "DISCORD_AUTH", message)
            .severity(ErrorSeverity::Error)
            .recoverable(false)
            .suggested_action("Reconnectez-vous à Discord")
            .build()
    }
    
    /// Créer une erreur de rate limit Discord
    pub fn discord_rate_limit(retry_after: u64) -> Self {
        ErrorBuilder::new(
            ErrorType::DiscordRateLimit, 
            "DISCORD_RATE_LIMIT", 
            format!("Rate limit atteint, réessayez dans {} secondes", retry_after)
        )
        .severity(ErrorSeverity::Warning)
        .suggested_action("Patientez avant de réessayer")
        .build()
    }
    
    /// Créer une erreur de token invalide
    pub fn invalid_token() -> Self {
        ErrorBuilder::new(
            ErrorType::DiscordToken,
            "INVALID_TOKEN",
            "Token Discord invalide ou expiré"
        )
        .severity(ErrorSeverity::Error)
        .recoverable(false)
        .suggested_action("Obtenez un nouveau token Discord")
        .build()
    }
    
    /// Créer une erreur de validation
    pub fn validation_error(field: impl Into<String>, reason: impl Into<String>) -> Self {
        ErrorBuilder::new(
            ErrorType::Validation,
            "VALIDATION_ERROR",
            format!("Validation échouée pour '{}': {}", field.into(), reason.into())
        )
        .severity(ErrorSeverity::Warning)
        .suggested_action("Vérifiez les données saisies")
        .build()
    }
    
    /// Créer une erreur de base de données
    pub fn database_error(operation: impl Into<String>, details: impl Into<String>) -> Self {
        ErrorBuilder::new(
            ErrorType::Database,
            "DATABASE_ERROR",
            format!("Erreur base de données lors de: {}", operation.into())
        )
        .details(details.into())
        .severity(ErrorSeverity::Error)
        .suggested_action("Vérifiez la base de données et réessayez")
        .build()
    }
    
    /// Créer une erreur de sécurité
    pub fn security_error(message: impl Into<String>) -> Self {
        ErrorBuilder::new(ErrorType::Security, "SECURITY_ERROR", message)
            .severity(ErrorSeverity::Critical)
            .recoverable(false)
            .suggested_action("Contactez l'administrateur")
            .build()
    }
    
    /// Créer une erreur de configuration
    pub fn config_error(setting: impl Into<String>, reason: impl Into<String>) -> Self {
        ErrorBuilder::new(
            ErrorType::Config,
            "CONFIG_ERROR",
            format!("Configuration invalide pour '{}': {}", setting.into(), reason.into())
        )
        .severity(ErrorSeverity::Warning)
        .suggested_action("Vérifiez la configuration")
        .build()
    }
    
    /// Créer une erreur interne
    pub fn internal_error(component: impl Into<String>, details: impl Into<String>) -> Self {
        ErrorBuilder::new(
            ErrorType::Internal,
            "INTERNAL_ERROR",
            format!("Erreur interne dans {}", component.into())
        )
        .details(details.into())
        .severity(ErrorSeverity::Error)
        .suggested_action("Redémarrez l'application")
        .build()
    }
    
    /// Obtenir un message d'erreur formaté pour l'utilisateur
    pub fn user_message(&self) -> String {
        match self.severity {
            ErrorSeverity::Debug | ErrorSeverity::Info => {
                format!("ℹ️ {}", self.message)
            },
            ErrorSeverity::Warning => {
                format!("⚠️ {}", self.message)
            },
            ErrorSeverity::Error => {
                format!("❌ {}", self.message)
            },
            ErrorSeverity::Critical | ErrorSeverity::Fatal => {
                format!("🚨 ERREUR CRITIQUE: {}", self.message)
            },
        }
    }
    
    /// Obtenir un message d'erreur détaillé pour les logs
    pub fn detailed_message(&self) -> String {
        let mut msg = format!(
            "[{}] {}: {} ({})",
            self.severity_string(),
            self.code,
            self.message,
            self.timestamp.format("%Y-%m-%d %H:%M:%S UTC")
        );
        
        if let Some(details) = &self.details {
            msg.push_str(&format!("\nDétails: {}", details));
        }
        
        if let Some(context) = &self.context {
            msg.push_str(&format!("\nContexte: {}", serde_json::to_string(context).unwrap_or_default()));
        }
        
        if let Some(action) = &self.suggested_action {
            msg.push_str(&format!("\nAction suggérée: {}", action));
        }
        
        msg
    }
    
    /// Obtenir la sévérité sous forme de string
    pub fn severity_string(&self) -> &'static str {
        match self.severity {
            ErrorSeverity::Debug => "DEBUG",
            ErrorSeverity::Info => "INFO",
            ErrorSeverity::Warning => "WARN",
            ErrorSeverity::Error => "ERROR",
            ErrorSeverity::Critical => "CRIT",
            ErrorSeverity::Fatal => "FATAL",
        }
    }
    
    /// Vérifier si l'erreur nécessite une intervention immédiate
    pub fn requires_immediate_attention(&self) -> bool {
        matches!(self.severity, ErrorSeverity::Critical | ErrorSeverity::Fatal)
    }
    
    /// Vérifier si l'erreur peut être résolue automatiquement
    pub fn can_auto_recover(&self) -> bool {
        self.recoverable && !self.requires_immediate_attention()
    }
}

impl fmt::Display for VoidBotError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.user_message())
    }
}

impl std::error::Error for VoidBotError {
    fn description(&self) -> &str {
        &self.message
    }
}

/// Macro pour créer facilement des erreurs avec contexte
#[macro_export]
macro_rules! voidbot_error {
    ($error_type:expr, $code:expr, $message:expr) => {
        VoidBotError::ErrorBuilder::new($error_type, $code, $message).build()
    };
    
    ($error_type:expr, $code:expr, $message:expr, $context:expr) => {
        VoidBotError::ErrorBuilder::new($error_type, $code, $message)
            .context($context)
            .build()
    };
}

/// Trait pour convertir les erreurs en VoidBotError
pub trait IntoVoidBotError {
    fn into_voidbot_error(self, error_type: ErrorType, code: impl Into<String>) -> VoidBotError;
}

impl<E: std::error::Error> IntoVoidBotError for E {
    fn into_voidbot_error(self, error_type: ErrorType, code: impl Into<String>) -> VoidBotError {
        ErrorBuilder::new(error_type, code, self.to_string())
            .details(format!("{:?}", self))
            .build()
    }
}

/// Result type uniforme pour VoidBot
pub type VoidBotResult<T> = Result<T, VoidBotError>;

/// Helper pour créer des contextes d'erreur
impl ErrorContext {
    pub fn new(component: impl Into<String>) -> Self {
        Self {
            component: component.into(),
            function: None,
            user_id: None,
            guild_id: None,
            channel_id: None,
            request_id: None,
            additional_data: std::collections::HashMap::new(),
        }
    }
    
    pub fn function(mut self, function: impl Into<String>) -> Self {
        self.function = Some(function.into());
        self
    }
    
    pub fn user_id(mut self, user_id: impl Into<String>) -> Self {
        self.user_id = Some(user_id.into());
        self
    }
    
    pub fn guild_id(mut self, guild_id: impl Into<String>) -> Self {
        self.guild_id = Some(guild_id.into());
        self
    }
    
    pub fn channel_id(mut self, channel_id: impl Into<String>) -> Self {
        self.channel_id = Some(channel_id.into());
        self
    }
    
    pub fn request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }
    
    pub fn add_data(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.additional_data.insert(key.into(), value.into());
        self
    }
}