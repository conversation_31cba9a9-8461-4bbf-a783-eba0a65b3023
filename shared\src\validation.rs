use anyhow::Result;

/// Erreurs de validation
#[derive(Debug, thiserror::Error)]
pub enum ValidationError {
    #[error("Valeur trop longue: {field} ({actual} > {max})")]
    TooLong { field: String, actual: usize, max: usize },
    
    #[error("Valeur trop courte: {field} ({actual} < {min})")]
    TooShort { field: String, actual: usize, min: usize },
    
    #[error("Format invalide: {field} - {reason}")]
    InvalidFormat { field: String, reason: String },
    
    #[error("Caractères interdits dans {field}: {chars}")]
    InvalidCharacters { field: String, chars: String },
    
    #[error("Valeur manquante: {field}")]
    Required { field: String },
    
    #[error("Valeur hors limites: {field} ({value} pas dans [{min}, {max}])")]
    OutOfRange { field: String, value: i64, min: i64, max: i64 },
}

/// Trait pour valider les structures de données
pub trait Validate {
    fn validate(&self) -> Result<(), ValidationError>;
}

/// Validateur générique pour les chaînes de caractères
#[derive(Debug, Clone)]
pub struct StringValidator {
    pub min_length: Option<usize>,
    pub max_length: Option<usize>,
    pub allowed_chars: Option<String>,
    pub forbidden_chars: Option<String>,
    pub regex_pattern: Option<String>,
    pub required: bool,
}

impl Default for StringValidator {
    fn default() -> Self {
        Self {
            min_length: None,
            max_length: Some(1000), // Limite par défaut
            allowed_chars: None,
            forbidden_chars: Some("\0\t\n\r".to_string()), // Caractères de contrôle interdits
            regex_pattern: None,
            required: false,
        }
    }
}

impl StringValidator {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn min_length(mut self, min: usize) -> Self {
        self.min_length = Some(min);
        self
    }
    
    pub fn max_length(mut self, max: usize) -> Self {
        self.max_length = Some(max);
        self
    }
    
    pub fn allowed_chars(mut self, chars: &str) -> Self {
        self.allowed_chars = Some(chars.to_string());
        self
    }
    
    pub fn forbidden_chars(mut self, chars: &str) -> Self {
        self.forbidden_chars = Some(chars.to_string());
        self
    }
    
    pub fn required(mut self) -> Self {
        self.required = true;
        self
    }
    
    pub fn validate(&self, field_name: &str, value: &str) -> Result<(), ValidationError> {
        // Vérification requise
        if self.required && value.is_empty() {
            return Err(ValidationError::Required { 
                field: field_name.to_string() 
            });
        }
        
        if value.is_empty() && !self.required {
            return Ok(());
        }
        
        // Vérification longueur minimum
        if let Some(min) = self.min_length {
            if value.len() < min {
                return Err(ValidationError::TooShort {
                    field: field_name.to_string(),
                    actual: value.len(),
                    min,
                });
            }
        }
        
        // Vérification longueur maximum
        if let Some(max) = self.max_length {
            if value.len() > max {
                return Err(ValidationError::TooLong {
                    field: field_name.to_string(),
                    actual: value.len(),
                    max,
                });
            }
        }
        
        // Vérification caractères interdits
        if let Some(forbidden) = &self.forbidden_chars {
            let invalid_chars: Vec<char> = value.chars()
                .filter(|c| forbidden.contains(*c))
                .collect();
            
            if !invalid_chars.is_empty() {
                return Err(ValidationError::InvalidCharacters {
                    field: field_name.to_string(),
                    chars: invalid_chars.iter().collect::<String>(),
                });
            }
        }
        
        // Vérification caractères autorisés
        if let Some(allowed) = &self.allowed_chars {
            for c in value.chars() {
                if !allowed.contains(c) {
                    return Err(ValidationError::InvalidCharacters {
                        field: field_name.to_string(),
                        chars: c.to_string(),
                    });
                }
            }
        }
        
        Ok(())
    }
}

/// Validateur pour les ID Discord
pub struct DiscordIdValidator;

impl DiscordIdValidator {
    pub fn validate(field_name: &str, value: &str) -> Result<(), ValidationError> {
        if value.is_empty() {
            return Err(ValidationError::Required { 
                field: field_name.to_string() 
            });
        }
        
        // Les ID Discord sont des entiers 64-bit en chaîne
        if value.len() < 10 || value.len() > 25 {
            return Err(ValidationError::InvalidFormat {
                field: field_name.to_string(),
                reason: "Les ID Discord doivent faire entre 10 et 25 caractères".to_string(),
            });
        }
        
        // Doit être uniquement des chiffres
        if !value.chars().all(|c| c.is_ascii_digit()) {
            return Err(ValidationError::InvalidFormat {
                field: field_name.to_string(),
                reason: "Les ID Discord ne doivent contenir que des chiffres".to_string(),
            });
        }
        
        Ok(())
    }
}

/// Validateur pour les commandes Discord slash
pub struct SlashCommandValidator;

impl SlashCommandValidator {
    pub fn validate(field_name: &str, command: &str) -> Result<(), ValidationError> {
        if command.is_empty() {
            return Err(ValidationError::Required { 
                field: field_name.to_string() 
            });
        }
        
        // Longueur max des commandes Discord
        if command.len() > 32 {
            return Err(ValidationError::TooLong {
                field: field_name.to_string(),
                actual: command.len(),
                max: 32,
            });
        }
        
        // Format : doit commencer par une lettre, puis lettres/chiffres/tirets
        if !command.chars().next().unwrap().is_ascii_alphabetic() {
            return Err(ValidationError::InvalidFormat {
                field: field_name.to_string(),
                reason: "Les commandes doivent commencer par une lettre".to_string(),
            });
        }
        
        // Caractères autorisés : lettres minuscules, chiffres, tirets
        if !command.chars().all(|c| c.is_ascii_lowercase() || c.is_ascii_digit() || c == '-') {
            return Err(ValidationError::InvalidFormat {
                field: field_name.to_string(),
                reason: "Les commandes ne peuvent contenir que des lettres minuscules, chiffres et tirets".to_string(),
            });
        }
        
        Ok(())
    }
}

/// Validateur pour les URL
pub struct UrlValidator;

impl UrlValidator {
    pub fn validate(field_name: &str, url: &str) -> Result<(), ValidationError> {
        if url.is_empty() {
            return Err(ValidationError::Required { 
                field: field_name.to_string() 
            });
        }
        
        // Vérification basique du format URL
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err(ValidationError::InvalidFormat {
                field: field_name.to_string(),
                reason: "L'URL doit commencer par http:// ou https://".to_string(),
            });
        }
        
        // Longueur max raisonnable
        if url.len() > 2048 {
            return Err(ValidationError::TooLong {
                field: field_name.to_string(),
                actual: url.len(),
                max: 2048,
            });
        }
        
        Ok(())
    }
}

/// Validateur pour les nombres entiers
pub struct IntValidator {
    min: Option<i64>,
    max: Option<i64>,
}

impl IntValidator {
    pub fn new() -> Self {
        Self { min: None, max: None }
    }
    
    pub fn min(mut self, min: i64) -> Self {
        self.min = Some(min);
        self
    }
    
    pub fn max(mut self, max: i64) -> Self {
        self.max = Some(max);
        self
    }
    
    pub fn validate(&self, field_name: &str, value: i64) -> Result<(), ValidationError> {
        if let Some(min) = self.min {
            if value < min {
                return Err(ValidationError::OutOfRange {
                    field: field_name.to_string(),
                    value,
                    min,
                    max: self.max.unwrap_or(i64::MAX),
                });
            }
        }
        
        if let Some(max) = self.max {
            if value > max {
                return Err(ValidationError::OutOfRange {
                    field: field_name.to_string(),
                    value,
                    min: self.min.unwrap_or(i64::MIN),
                    max,
                });
            }
        }
        
        Ok(())
    }
}

/// Implémentations de validation pour les types VoidBot

impl Validate for crate::auto_commands::SlashTrigger {
    fn validate(&self) -> Result<(), ValidationError> {
        SlashCommandValidator::validate("command", &self.command)?;
        
        IntValidator::new().min(0).max(60000)
            .validate("delay_ms", self.delay_ms as i64)?;
        
        // Validation des options
        for (key, value) in &self.options {
            StringValidator::new()
                .max_length(32)
                .allowed_chars("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-")
                .validate(&format!("option_key_{}", key), key)?;
                
            StringValidator::new()
                .max_length(1000)
                .validate(&format!("option_value_{}", key), value)?;
        }
        
        Ok(())
    }
}

impl Validate for crate::auto_commands::AutoReply {
    fn validate(&self) -> Result<(), ValidationError> {
        StringValidator::new()
            .required()
            .max_length(2000) // Limite Discord
            .validate("response", &self.response)?;
        
        // Validation des réponses multiples
        for (i, response) in self.random_responses.iter().enumerate() {
            StringValidator::new()
                .max_length(2000)
                .validate(&format!("random_response_{}", i), response)?;
        }
        
        // Validation de la probabilité
        if self.probability < 0.0 || self.probability > 1.0 {
            return Err(ValidationError::OutOfRange {
                field: "probability".to_string(),
                value: (self.probability * 100.0) as i64,
                min: 0,
                max: 100,
            });
        }
        
        Ok(())
    }
}

impl Validate for crate::auto_commands::AutoTranslateConfig {
    fn validate(&self) -> Result<(), ValidationError> {
        // Validation des codes de langue (format ISO 639-1)
        let lang_validator = StringValidator::new()
            .min_length(2)
            .max_length(5)
            .allowed_chars("abcdefghijklmnopqrstuvwxyz-");
        
        lang_validator.validate("source_lang", &self.source_lang)?;
        lang_validator.validate("target_lang", &self.target_lang)?;
        
        // Validation longueur minimum
        IntValidator::new().min(1).max(1000)
            .validate("min_length", self.min_length as i64)?;
        
        // Validation des canaux
        for (i, channel_id) in self.channels.iter().enumerate() {
            DiscordIdValidator::validate(&format!("channel_{}", i), &channel_id.to_string())?;
        }
        
        Ok(())
    }
}

impl Validate for crate::auto_commands::AutoSlashConfig {
    fn validate(&self) -> Result<(), ValidationError> {
        // Limiter le nombre de triggers
        if self.triggers.len() > 1000 {
            return Err(ValidationError::TooLong {
                field: "triggers".to_string(),
                actual: self.triggers.len(),
                max: 1000,
            });
        }
        
        // Validation de chaque trigger
        for (keyword, trigger) in &self.triggers {
            StringValidator::new()
                .required()
                .max_length(50)
                .forbidden_chars("\0\t\n\r<>\"'&")
                .validate("trigger_keyword", keyword)?;
            
            trigger.validate()?;
        }
        
        // Validation des guild settings
        for (guild_id, settings) in &self.guild_settings {
            DiscordIdValidator::validate("guild_id", &guild_id.to_string())?;
            settings.validate()?;
        }
        
        Ok(())
    }
}

impl Validate for crate::auto_commands::GuildSlashSettings {
    fn validate(&self) -> Result<(), ValidationError> {
        // Validation cooldown
        IntValidator::new().min(0).max(3600)
            .validate("cooldown_seconds", self.cooldown_seconds as i64)?;
        
        // Validation des canaux autorisés
        for (i, channel_id) in self.allowed_channels.iter().enumerate() {
            DiscordIdValidator::validate(&format!("allowed_channel_{}", i), &channel_id.to_string())?;
        }
        
        // Validation des rôles autorisés
        for (i, role_id) in self.allowed_roles.iter().enumerate() {
            DiscordIdValidator::validate(&format!("allowed_role_{}", i), &role_id.to_string())?;
        }
        
        Ok(())
    }
}

impl Validate for crate::auto_commands::AutoReplyConfig {
    fn validate(&self) -> Result<(), ValidationError> {
        // Validation des cooldowns
        IntValidator::new().min(0).max(86400) // Max 24h
            .validate("global_cooldown_seconds", self.global_cooldown_seconds as i64)?;
        
        IntValidator::new().min(0).max(86400)
            .validate("per_user_cooldown_seconds", self.per_user_cooldown_seconds as i64)?;
        
        // Limiter le nombre de triggers
        if self.triggers.len() > 500 {
            return Err(ValidationError::TooLong {
                field: "triggers".to_string(),
                actual: self.triggers.len(),
                max: 500,
            });
        }
        
        // Validation de chaque trigger
        for (keyword, reply) in &self.triggers {
            StringValidator::new()
                .required()
                .max_length(50)
                .forbidden_chars("\0\t\n\r<>\"'&")
                .validate("reply_keyword", keyword)?;
            
            reply.validate()?;
        }
        
        Ok(())
    }
}

impl Validate for crate::notifications::NotificationConfig {
    fn validate(&self) -> Result<(), ValidationError> {
        // Validation du volume (0.0 à 1.0)
        if self.sound_volume < 0.0 || self.sound_volume > 1.0 {
            return Err(ValidationError::OutOfRange {
                field: "sound_volume".to_string(),
                value: (self.sound_volume * 100.0) as i64,
                min: 0,
                max: 100,
            });
        }
        
        // Validation de la rétention (max 365 jours)
        IntValidator::new().min(1).max(365)
            .validate("history_retention_days", self.history_retention_days as i64)?;
        
        // Validation des mots-clés
        if self.keywords.len() > 100 {
            return Err(ValidationError::TooLong {
                field: "keywords".to_string(),
                actual: self.keywords.len(),
                max: 100,
            });
        }
        
        for (i, keyword) in self.keywords.iter().enumerate() {
            StringValidator::new()
                .required()
                .max_length(100)
                .forbidden_chars("\0\t\n\r<>\"'&")
                .validate(&format!("keyword_{}", i), keyword)?;
        }
        
        // Validation des URLs webhook
        if self.webhook_urls.len() > 10 {
            return Err(ValidationError::TooLong {
                field: "webhook_urls".to_string(),
                actual: self.webhook_urls.len(),
                max: 10,
            });
        }
        
        for (i, url) in self.webhook_urls.iter().enumerate() {
            UrlValidator::validate(&format!("webhook_url_{}", i), url)?;
        }
        
        // Validation des canaux surveillés
        if self.monitored_channels.len() > 500 {
            return Err(ValidationError::TooLong {
                field: "monitored_channels".to_string(),
                actual: self.monitored_channels.len(),
                max: 500,
            });
        }
        
        for (i, channel_id) in self.monitored_channels.iter().enumerate() {
            DiscordIdValidator::validate(&format!("monitored_channel_{}", i), channel_id)?;
        }
        
        // Validation des utilisateurs surveillés
        if self.monitored_users.len() > 1000 {
            return Err(ValidationError::TooLong {
                field: "monitored_users".to_string(),
                actual: self.monitored_users.len(),
                max: 1000,
            });
        }
        
        for (i, user_id) in self.monitored_users.iter().enumerate() {
            DiscordIdValidator::validate(&format!("monitored_user_{}", i), user_id)?;
        }
        
        Ok(())
    }
}

/// Fonction utilitaire pour valider et sanitiser une chaîne
pub fn sanitize_and_validate(
    value: &str, 
    validator: &StringValidator, 
    field_name: &str
) -> Result<String, ValidationError> {
    validator.validate(field_name, value)?;
    
    // Sanitise en retirant les caractères de contrôle
    let sanitized: String = value
        .chars()
        .filter(|c| !c.is_control() || *c == '\n' || *c == '\t')
        .collect();
    
    Ok(sanitized)
}

/// Macro pour simplifier la validation
#[macro_export]
macro_rules! validate_field {
    ($field:expr, $validator:expr, $field_name:expr) => {
        $validator.validate($field_name, $field)?;
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_string_validator() {
        let validator = StringValidator::new()
            .min_length(3)
            .max_length(10)
            .required();
        
        // Test valide
        assert!(validator.validate("test", "hello").is_ok());
        
        // Test trop court
        assert!(validator.validate("test", "hi").is_err());
        
        // Test trop long
        assert!(validator.validate("test", "hello world!").is_err());
        
        // Test vide (requis)
        assert!(validator.validate("test", "").is_err());
    }

    #[test]
    fn test_discord_id_validator() {
        // ID valide
        assert!(DiscordIdValidator::validate("user_id", "123456789012345678").is_ok());
        
        // ID invalide (trop court)
        assert!(DiscordIdValidator::validate("user_id", "123").is_err());
        
        // ID invalide (caractères non numériques)
        assert!(DiscordIdValidator::validate("user_id", "12345abc78901234567").is_err());
    }

    #[test]
    fn test_slash_command_validator() {
        // Commande valide
        assert!(SlashCommandValidator::validate("command", "test-command").is_ok());
        
        // Commande invalide (commence par chiffre)
        assert!(SlashCommandValidator::validate("command", "1test").is_err());
        
        // Commande invalide (caractères spéciaux)
        assert!(SlashCommandValidator::validate("command", "test@command").is_err());
    }
}