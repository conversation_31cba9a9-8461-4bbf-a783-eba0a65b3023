import { useState } from 'react';
import { motion } from 'framer-motion';
import { Bot, Zap, Shield, Activity } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';

/**
 * Composant principal du tableau de bord VoidBot
 * 
 * Fonctionnalités :
 * - Affichage du statut de connexion du bot Discord
 * - Interface de connexion avec token utilisateur
 * - Indicateurs de statut des fonctionnalités (Stealth Mode, animations, snipers)
 * - Statistiques d'utilisation en temps réel
 * - Actions rapides pour les fonctionnalités principales
 * 
 * État géré via useAppStore pour synchronisation globale
 * Interface responsive avec animations Framer Motion
 * 
 * @returns Composant React du tableau de bord
 */
export function Dashboard() {
  const { 
    isConnected, 
    botStatus, 
    stealthMode, 
    stats, 
    connectBot,
    animationsEnabled,
    sniperEnabled,
    discordUser 
  } = useAppStore();
  
  const [token, setToken] = useState('');
  const [connecting, setConnecting] = useState(false);

  /**
   * Gère la connexion du bot Discord avec le token fourni
   * Valide le token, tente la connexion et met à jour l'état
   */
  const handleConnect = async () => {
    if (!token.trim()) return;
    
    try {
      setConnecting(true);
      await connectBot(token);
    } catch (error) {
      console.error('Échec connexion bot:', error);
      // TODO: Afficher une notification d'erreur à l'utilisateur
    } finally {
      setConnecting(false);
    }
  };

  /**
   * Couleurs CSS correspondant aux différents statuts du bot
   * Utilisé pour l'affichage visuel du statut de connexion
   */
  const statusColor = {
    offline: 'text-red-400',
    connecting: 'text-yellow-400', 
    online: 'text-green-400',
    error: 'text-red-400',
  }[botStatus];

  /**
   * Icônes emoji correspondant aux différents statuts du bot
   * Fournit un indicateur visuel rapide du statut de connexion
   */
  const statusIcon = {
    offline: '🔴',    // Rouge - Bot déconnecté
    connecting: '🟡', // Jaune - Connexion en cours
    online: '🟢',     // Vert - Bot connecté et opérationnel
    error: '❌',        // Croix - Erreur de connexion
  }[botStatus];

  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Tableau de bord</h1>
        <p className="page-subtitle">
          Surveiller et contrôler votre instance VoidBot
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Connection Status */}
        <motion.div 
          className="card card-hover"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center gap-3 mb-4">
            <Bot className="w-6 h-6 text-voidbot-primary" />
            <h3 className="text-lg font-semibold">Statut Selfbot</h3>
          </div>
          
          <div className="flex items-center gap-2 mb-4">
            <span className="text-2xl">{statusIcon}</span>
            <div>
              <div className={`font-medium ${statusColor}`}>
                {botStatus.charAt(0).toUpperCase() + botStatus.slice(1)}
              </div>
              {discordUser && (
                <div className="text-sm text-voidbot-secondary">
                  {discordUser.username}#{discordUser.discriminator}
                </div>
              )}
            </div>
          </div>

          {!isConnected && (
            <div className="space-y-3">
              <div className="input-group">
                <label className="input-label">Token Utilisateur Discord</label>
                <input
                  type="password"
                  className="input-field"
                  placeholder="Votre token utilisateur Discord..."
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                />
                <p className="text-xs text-voidbot-secondary mt-1">
                  ⚠️ Utilisez votre token utilisateur, pas un token de bot
                </p>
              </div>
              <button
                className="btn btn-primary w-full"
                onClick={handleConnect}
                disabled={connecting || !token.trim()}
              >
                {connecting ? 'Connexion...' : 'Connecter Selfbot'}
              </button>
            </div>
          )}
        </motion.div>

        {/* Stealth Mode */}
        <motion.div 
          className="card card-hover"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-6 h-6 text-voidbot-secondary" />
            <h3 className="text-lg font-semibold">Mode Furtif</h3>
          </div>
          
          <div className="flex items-center gap-2 mb-2">
            <span className="text-2xl">
              {stealthMode === 'ghost' ? '👻' : '💬'}
            </span>
            <span className="font-medium text-voidbot-primary">
              {stealthMode === 'ghost' ? 'Mode Fantôme' : 'Mode Normal'}
            </span>
          </div>
          
          <p className="text-sm text-voidbot-secondary">
            {stealthMode === 'ghost' 
              ? 'Réponses visibles uniquement par vous'
              : 'Réponses visibles par tous'
            }
          </p>
        </motion.div>

        {/* Features Status */}
        <motion.div 
          className="card card-hover"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center gap-3 mb-4">
            <Zap className="w-6 h-6 text-voidbot-accent" />
            <h3 className="text-lg font-semibold">Fonctionnalités</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Animations</span>
              <div className={`w-2 h-2 rounded-full ${
                animationsEnabled ? 'bg-green-500' : 'bg-gray-500'
              }`} />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Snipers</span>
              <div className={`w-2 h-2 rounded-full ${
                sniperEnabled ? 'bg-green-500' : 'bg-gray-500'
              }`} />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Présence Riche</span>
              <div className="w-2 h-2 rounded-full bg-green-500" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Statistics */}
      <motion.div 
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-center gap-3 mb-6">
          <Activity className="w-6 h-6 text-voidbot-primary" />
          <h3 className="text-xl font-semibold">Statistiques</h3>
        </div>

        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{stats.commandsUsed}</div>
            <div className="stat-label">Commandes Utilisées</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{stats.serversBackedUp}</div>
            <div className="stat-label">Serveurs Sauvegardés</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{stats.animationsActive}</div>
            <div className="stat-label">Animations Actives</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">
              {Math.floor(stats.uptime / 3600)}h {Math.floor((stats.uptime % 3600) / 60)}m
            </div>
            <div className="stat-label">Temps de Fonctionnement</div>
          </div>
        </div>
      </motion.div>

      {/* Quick Actions */}
      {isConnected && (
        <motion.div 
          className="card mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <h3 className="text-xl font-semibold mb-4">Actions Rapides</h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="btn btn-ghost">
              Démarrer Animation
            </button>
            <button className="btn btn-ghost">
              Sauvegarder Serveur
            </button>
            <button className="btn btn-ghost">
              Basculer Snipers
            </button>
            <button className="btn btn-ghost">
              Voir Commandes
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
}