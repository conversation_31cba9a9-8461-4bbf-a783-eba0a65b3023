// Serveur OAuth local pour récupérer le code d'autorisation Discord
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{Mutex, oneshot};
use tokio::time::timeout;
use warp::{Filter, Reply};
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};

/// Paramètres reçus du callback OAuth Discord
#[derive(Debug, Deserialize)]
pub struct OAuthCallback {
    pub code: Option<String>,
    pub state: Option<String>,
    pub error: Option<String>,
    pub error_description: Option<String>,
}

/// Résultat du flow OAuth
#[derive(Debug, Serialize, Clone)]
pub struct OAuthResult {
    pub success: bool,
    pub code: Option<String>,
    pub state: Option<String>,
    pub error: Option<String>,
    pub message: String,
}

/// Serveur OAuth local avec timeout et sécurité
pub struct OAuthCallbackServer {
    port: u16,
    timeout_secs: u64,
    expected_state: Option<String>,
}

impl OAuthCallbackServer {
    /// Crée un nouveau serveur OAuth
    pub fn new(port: u16, timeout_secs: u64) -> Self {
        Self {
            port,
            timeout_secs,
            expected_state: None,
        }
    }
    
    /// Configure l'état attendu pour la validation CSRF
    pub fn with_expected_state(mut self, state: String) -> Self {
        self.expected_state = Some(state);
        self
    }
    
    /// Démarre le serveur et attend le callback OAuth
    pub async fn listen_for_callback(self) -> Result<OAuthResult, Box<dyn std::error::Error + Send + Sync>> {
        let (tx, rx) = oneshot::channel();
        let result_sender = Arc::new(Mutex::new(Some(tx)));
        let expected_state = Arc::new(self.expected_state);
        
        // Clone pour le closure
        let result_sender_clone = result_sender.clone();
        let expected_state_clone = expected_state.clone();
        
        // Route de callback OAuth
        let oauth_callback = warp::path("oauth")
            .and(warp::path("callback"))
            .and(warp::query::<OAuthCallback>())
            .and_then(move |params: OAuthCallback| {
                let sender = result_sender_clone.clone();
                let state = expected_state_clone.clone();
                async move {
                    let result = Self::process_callback(params, &state).await;
                    
                    // Envoyer le résultat via le channel
                    if let Some(tx) = sender.lock().await.take() {
                        let _ = tx.send(result.clone());
                    }
                    
                    // Retourner une page HTML de succès/erreur
                    Ok::<Box<dyn Reply>, warp::Rejection>(Box::new(Self::create_response_page(&result)))
                }
            });
        
        // Route de santé pour vérifier que le serveur fonctionne
        let health = warp::path("health")
            .map(|| "VoidBot OAuth Server OK");
        
        // Route pour servir une page d'accueil simple
        let index = warp::path::end()
            .map(|| Self::create_waiting_page());
        
        let routes = oauth_callback
            .or(health)
            .or(index)
            .with(warp::cors()
                .allow_any_origin()
                .allow_methods(vec!["GET", "POST"])
                .allow_headers(vec!["content-type"]));
        
        info!("🚀 Serveur OAuth démarré sur http://localhost:{}", self.port);
        info!("📡 En attente du callback Discord...");
        
        // Démarrer le serveur avec timeout
        let server_future = warp::serve(routes)
            .run(([127, 0, 0, 1], self.port));
        
        // Attendre soit le callback, soit le timeout
        let result = timeout(
            Duration::from_secs(self.timeout_secs),
            async {
                tokio::select! {
                    result = rx => {
                        match result {
                            Ok(oauth_result) => oauth_result,
                            Err(_) => OAuthResult {
                                success: false,
                                code: None,
                                state: None,
                                error: Some("CHANNEL_ERROR".to_string()),
                                message: "Erreur de communication interne".to_string(),
                            }
                        }
                    }
                    _ = server_future => {
                        OAuthResult {
                            success: false,
                            code: None,
                            state: None,
                            error: Some("SERVER_ERROR".to_string()),
                            message: "Le serveur s'est arrêté de manière inattendue".to_string(),
                        }
                    }
                }
            }
        ).await;
        
        match result {
            Ok(oauth_result) => {
                info!("✅ Callback OAuth reçu avec succès");
                Ok(oauth_result)
            }
            Err(_) => {
                warn!("⏱️ Timeout du serveur OAuth après {} secondes", self.timeout_secs);
                Ok(OAuthResult {
                    success: false,
                    code: None,
                    state: None,
                    error: Some("TIMEOUT".to_string()),
                    message: format!("Timeout après {} secondes", self.timeout_secs),
                })
            }
        }
    }
    
    /// Traite le callback OAuth et valide les paramètres
    async fn process_callback(
        params: OAuthCallback,
        expected_state: &Arc<Option<String>>,
    ) -> OAuthResult {
        info!("📨 Callback OAuth reçu: {:?}", params);
        
        // Vérifier s'il y a une erreur Discord
        if let Some(error) = &params.error {
            error!("❌ Erreur OAuth Discord: {}", error);
            let description = params.error_description
                .unwrap_or_else(|| "Erreur inconnue".to_string());
            
            return OAuthResult {
                success: false,
                code: None,
                state: params.state,
                error: Some(error.clone()),
                message: format!("Erreur Discord: {} - {}", error, description),
            };
        }
        
        // Vérifier que le code est présent
        let code = match params.code {
            Some(code) if !code.is_empty() => code,
            _ => {
                error!("❌ Code d'autorisation manquant dans le callback");
                return OAuthResult {
                    success: false,
                    code: None,
                    state: params.state,
                    error: Some("MISSING_CODE".to_string()),
                    message: "Code d'autorisation manquant".to_string(),
                };
            }
        };
        
        // Valider le state pour prévenir les attaques CSRF
        if let Some(expected) = expected_state.as_ref() {
            match &params.state {
                Some(state) if state == expected => {
                    info!("✅ Validation CSRF réussie");
                }
                Some(state) => {
                    error!("❌ State OAuth invalide - attaque CSRF potentielle");
                    error!("   Attendu: {}", expected);
                    error!("   Reçu: {}", state);
                    return OAuthResult {
                        success: false,
                        code: None,
                        state: params.state,
                        error: Some("CSRF_ATTACK".to_string()),
                        message: "State invalide - tentative d'attaque CSRF détectée".to_string(),
                    };
                }
                None => {
                    error!("❌ State manquant dans le callback OAuth");
                    return OAuthResult {
                        success: false,
                        code: None,
                        state: None,
                        error: Some("MISSING_STATE".to_string()),
                        message: "State manquant - sécurité compromise".to_string(),
                    };
                }
            }
        }
        
        info!("🎉 Callback OAuth valide - code reçu");
        OAuthResult {
            success: true,
            code: Some(code),
            state: params.state,
            error: None,
            message: "Autorisation réussie ! Vous pouvez fermer cette page.".to_string(),
        }
    }
    
    /// Crée la page HTML de réponse après le callback
    fn create_response_page(result: &OAuthResult) -> warp::http::Response<String> {
        let (status, title, message, color) = if result.success {
            (200, "Connexion réussie !", &result.message, "#10b981")
        } else {
            (400, "Erreur de connexion", &result.message, "#ef4444")
        };
        
        let html = format!(r#"
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoidBot OAuth - {}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
            color: white;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}
        .container {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            max-width: 500px;
            margin: 1rem;
            backdrop-filter: blur(10px);
        }}
        .icon {{
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
        }}
        .success {{ color: {}; }}
        .error {{ color: {}; }}
        h1 {{
            margin: 0 0 1rem 0;
            font-size: 1.5rem;
            font-weight: 600;
        }}
        p {{
            margin: 0 0 1.5rem 0;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }}
        .button {{
            background: {};
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: opacity 0.2s;
        }}
        .button:hover {{ opacity: 0.8; }}
        .footer {{
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.875rem;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="icon {}">{}</div>
        <h1>{}</h1>
        <p>{}</p>
        <button class="button" onclick="window.close()">Fermer cette page</button>
        <div class="footer">
            VoidBot OAuth Server
        </div>
    </div>
    <script>
        // Auto-fermer après 5 secondes si succès
        {}
    </script>
</body>
</html>"#,
            title,
            color, color, color,
            if result.success { "success" } else { "error" },
            if result.success { "✅" } else { "❌" },
            title,
            message,
            if result.success { "setTimeout(() => window.close(), 5000);" } else { "" }
        );
        
        warp::http::Response::builder()
            .status(status)
            .header("Content-Type", "text/html; charset=utf-8")
            .body(html)
            .unwrap()
    }
    
    /// Crée la page d'attente pendant le flow OAuth
    fn create_waiting_page() -> warp::http::Response<String> {
        let html = r#"
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoidBot OAuth Server</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
            color: white;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            max-width: 500px;
            margin: 1rem;
            backdrop-filter: blur(10px);
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        h1 {
            margin: 0 0 1rem 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #6366f1;
        }
        p {
            margin: 0 0 1.5rem 0;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h1>VoidBot OAuth Server</h1>
        <p>🔐 Serveur en attente du callback Discord...</p>
        <p>Connectez-vous via Discord pour continuer.</p>
    </div>
</body>
</html>"#;
        
        warp::http::Response::builder()
            .status(200)
            .header("Content-Type", "text/html; charset=utf-8")
            .body(html.to_string())
            .unwrap()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_oauth_server_creation() {
        let server = OAuthCallbackServer::new(3001, 60);
        assert_eq!(server.port, 3001);
        assert_eq!(server.timeout_secs, 60);
        assert!(server.expected_state.is_none());
    }
    
    #[tokio::test]
    async fn test_oauth_server_with_state() {
        let server = OAuthCallbackServer::new(3002, 30)
            .with_expected_state("test-state-123".to_string());
        assert_eq!(server.expected_state, Some("test-state-123".to_string()));
    }
}