# Déploiement automatique VoidBot
name: Deploy VoidBot

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

env:
  CARGO_TERM_COLOR: always

jobs:
  # Test et build Discord Bot
  test-bot:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Cache cargo
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        
    - name: Run tests
      run: cargo test --verbose
      
    - name: Build Discord Bot
      run: cargo build --release --bin voidbot-bot

  # Déploiement Railway automatique
  deploy-railway:
    needs: test-bot
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Railway CLI
      run: npm install -g @railway/cli
      
    - name: Deploy to Railway
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      run: railway deploy
      
  # Build Desktop (releases)
  build-desktop:
    needs: test-bot
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Install Linux dependencies
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt update
        sudo apt install -y \
          libwebkit2gtk-4.1-dev \
          libgtk-3-dev \
          libayatana-appindicator3-dev \
          librsvg2-dev
          
    - name: Build Desktop App
      run: |
        cd voidbot-desktop
        npm install
        npm run tauri build
        
    - name: Upload Release Assets
      if: startsWith(github.ref, 'refs/tags/')
      uses: actions/upload-artifact@v3
      with:
        name: voidbot-${{ matrix.os }}
        path: voidbot-desktop/src-tauri/target/release/bundle/