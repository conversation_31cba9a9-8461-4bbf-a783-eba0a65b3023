export { Dashboard } from './Dashboard';
export { StealthMode } from './StealthMode';
export { WebLogin } from './WebLogin';
export { NotificationCenter } from './NotificationCenter';
export { TrollControl } from './TrollControl';
export { AutoCommands } from './AutoCommands';

// Placeholder components for other pages
export function Commands() {
  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Commandes</h1>
        <p className="page-subtitle">Gérer et voir les commandes disponibles du bot</p>
      </div>
      <div className="card">
        <p className="text-voidbot-secondary">Page des commandes à venir...</p>
      </div>
    </div>
  );
}

export function Animations() {
  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Animations</h1>
        <p className="page-subtitle"><PERSON><PERSON><PERSON> et gérer les animations de profil</p>
      </div>
      <div className="card">
        <p className="text-voidbot-secondary">Page des animations à venir...</p>
      </div>
    </div>
  );
}

export function RichPresence() {
  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Rich Presence</h1>
        <p className="page-subtitle">Configurer votre Discord Rich Presence</p>
      </div>
      <div className="card">
        <p className="text-voidbot-secondary">Page Rich Presence à venir...</p>
      </div>
    </div>
  );
}

export function Backup() {
  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Sauvegarde & Clone</h1>
        <p className="page-subtitle">Sauvegarder et cloner des serveurs Discord</p>
      </div>
      <div className="card">
        <p className="text-voidbot-secondary">Page de sauvegarde à venir...</p>
      </div>
    </div>
  );
}

export function Statistics() {
  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Statistiques</h1>
        <p className="page-subtitle">Voir les statistiques détaillées d'utilisation</p>
      </div>
      <div className="card">
        <p className="text-voidbot-secondary">Page des statistiques à venir...</p>
      </div>
    </div>
  );
}

export function Settings() {
  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Paramètres</h1>
        <p className="page-subtitle">Configurer les paramètres et préférences du bot</p>
      </div>
      <div className="card">
        <p className="text-voidbot-secondary">Page des paramètres à venir...</p>
      </div>
    </div>
  );
}