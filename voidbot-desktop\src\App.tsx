import { useAppStore } from './stores/appStore';
import { useEffect, useState } from 'react';
import { SimpleSidebar } from './components/SimpleSidebar';
import { SimpleDashboard, SimpleStealthMode, SimpleNotifications } from './components/SimplePages';
import { LoginScreen } from './components/LoginScreen';
import ActivityViewer from './components/ActivityViewer';
import LoginManager from './components/LoginManager';
import DynamicVariables from './components/DynamicVariables';
import RichPresence from './components/RichPresence';
import { TrollControl } from './components/pages/TrollControl';
import { SystemManager } from './components/SystemManager';
import { AutoCommands } from './components/AutoCommands';
import { ThemeBuilder } from './components/ThemeBuilder';
import { SettingsPage } from './components/SettingsPage';
import { UpdateManager } from './components/UpdateManager';
import { UpdateNotification } from './components/UpdateNotification';
import { useUpdateStore } from './stores/updateStore';
import "./styles/globals.css";

function App() {
  const { currentPage, isConnected, botStatus, autoConnectFromSession } = useAppStore();
  const { initialize: initializeUpdater } = useUpdateStore();
  const [isCheckingSession, setIsCheckingSession] = useState(true);
  const [sessionChecked, setSessionChecked] = useState(false);
  const [showUpdateManager, setShowUpdateManager] = useState(false);

  // Vérifier la session sauvegardée au démarrage
  useEffect(() => {
    const checkSession = async () => {
      if (!sessionChecked) {
        try {
          // Délai de sécurité pour s'assurer que Tauri est prêt
          await new Promise(resolve => setTimeout(resolve, 500));
          await autoConnectFromSession();
        } catch (error) {
          console.error('Erreur lors de la vérification de session:', error);
        } finally {
          setIsCheckingSession(false);
          setSessionChecked(true);
        }
      }
    };

    checkSession();
  }, [autoConnectFromSession, sessionChecked]);

  // Initialiser l'auto-updater au démarrage
  useEffect(() => {
    const initUpdater = async () => {
      try {
        await initializeUpdater();
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'auto-updater:', error);
      }
    };

    initUpdater();
  }, [initializeUpdater]);

  // Afficher un écran de chargement pendant la vérification de session
  if (isCheckingSession) {
    return (
      <div className="h-screen bg-gradient-to-br from-voidbot-dark via-voidbot-darker to-black flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-8">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-voidbot-primary via-voidbot-secondary to-voidbot-accent bg-clip-text text-transparent">
              VoidBot
            </h1>
            <div className="absolute -inset-1 bg-gradient-to-r from-voidbot-primary/20 to-voidbot-secondary/20 blur-xl rounded-lg -z-10"></div>
          </div>
          <div className="flex items-center justify-center space-x-2 text-voidbot-secondary">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-voidbot-primary"></div>
            <span>Vérification de la session...</span>
          </div>
        </div>
      </div>
    );
  }

  // Afficher l'écran de connexion si pas connecté
  if (!isConnected && botStatus !== 'online') {
    return <LoginScreen />;
  }

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <SimpleDashboard />;
      case 'stealth':
        return <SimpleStealthMode />;
      case 'notifications':
        return <SimpleNotifications />;
      case 'activity':
        return <ActivityViewer />;
      case 'login':
        return <LoginManager />;
      case 'variables':
        return <DynamicVariables />;
      case 'rich-presence':
        return <RichPresence />;
      case 'troll':
        return <TrollControl />;
      case 'auto':
        return <AutoCommands />;
      case 'system':
        return <SystemManager />;
      case 'themes':
        return <ThemeBuilder />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <SimpleDashboard />;
    }
  };

  return (
    <div className="flex h-screen bg-voidbot-dark text-voidbot-primary">
      <SimpleSidebar />
      <main className="flex-1 overflow-y-auto bg-gradient-to-br from-voidbot-dark to-voidbot-darker">
        {renderPage()}
      </main>
      
      {/* Auto-Updater Components */}
      <UpdateNotification onOpenManager={() => setShowUpdateManager(true)} />
      <UpdateManager 
        isOpen={showUpdateManager} 
        onClose={() => setShowUpdateManager(false)} 
      />
    </div>
  );
}

export default App;