use serenity::{all::*, Error as SerenityError};
use voidbot_shared::{RateLimiter, RequestPriority, RateLimitedClient};
use std::sync::Arc;
use anyhow::Result;

/// Client HTTP avec rate limiting intégré pour Discord
pub struct RateLimitedDiscordClient {
    /// Client HTTP Serenity
    http: Arc<Http>,
    /// Rate limiter
    rate_limiter: Arc<RateLimiter>,
    /// Client reqwest pour requêtes custom
    reqwest_client: reqwest::Client,
}

impl RateLimitedDiscordClient {
    pub fn new(http: Arc<Http>, rate_limiter: Arc<RateLimiter>) -> Self {
        let reqwest_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("Failed to create reqwest client");

        Self {
            http,
            rate_limiter,
            reqwest_client,
        }
    }

    /// Envoyer un message avec rate limiting
    pub async fn send_message(
        &self,
        channel_id: ChannelId,
        content: &str,
        priority: RequestPriority,
    ) -> Result<Message> {
        let endpoint = format!("/channels/{}/messages", channel_id);
        
        // Vérifier le rate limit avant
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Envoyer le message
        match channel_id.say(&self.http, content).await {
            Ok(msg) => Ok(msg),
            Err(e) => {
                // Gérer 429 si nécessaire
                if let SerenityError::Http(ref http_err) = e {
                    if let HttpError::UnsuccessfulRequest(ref error_response) = http_err {
                        if error_response.status_code == 429 {
                            // Extraire retry_after
                            let retry_after = error_response
                                .error
                                .message
                                .parse::<u64>()
                                .unwrap_or(60);
                            
                            self.rate_limiter
                                .handle_rate_limit_error(&endpoint, retry_after)
                                .await?;
                        }
                    }
                }
                Err(e.into())
            }
        }
    }

    /// Créer une interaction response avec rate limiting
    pub async fn create_interaction_response(
        &self,
        interaction: &CommandInteraction,
        response: CreateInteractionResponse,
        priority: RequestPriority,
    ) -> Result<()> {
        let endpoint = format!("/interactions/{}/{}/callback", interaction.id, interaction.token);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Créer la réponse
        match interaction.create_response(&self.http, response).await {
            Ok(_) => Ok(()),
            Err(e) => {
                // Gérer 429
                if let SerenityError::Http(ref http_err) = e {
                    if let HttpError::UnsuccessfulRequest(ref error_response) = http_err {
                        if error_response.status_code == 429 {
                            let retry_after = 60; // Discord ne renvoie pas toujours retry-after pour interactions
                            
                            self.rate_limiter
                                .handle_rate_limit_error(&endpoint, retry_after)
                                .await?;
                        }
                    }
                }
                Err(e.into())
            }
        }
    }

    /// Éditer un message avec rate limiting
    pub async fn edit_message(
        &self,
        channel_id: ChannelId,
        message_id: MessageId,
        new_content: &str,
        priority: RequestPriority,
    ) -> Result<Message> {
        let endpoint = format!("/channels/{}/messages/{}", channel_id, message_id);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Éditer le message
        match channel_id.edit_message(&self.http, message_id, EditMessage::new().content(new_content)).await {
            Ok(msg) => Ok(msg),
            Err(e) => {
                self.handle_serenity_error(&endpoint, e).await
            }
        }
    }

    /// Supprimer un message avec rate limiting
    pub async fn delete_message(
        &self,
        channel_id: ChannelId,
        message_id: MessageId,
        priority: RequestPriority,
    ) -> Result<()> {
        let endpoint = format!("/channels/{}/messages/{}", channel_id, message_id);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Supprimer le message
        match channel_id.delete_message(&self.http, message_id).await {
            Ok(_) => Ok(()),
            Err(e) => {
                self.handle_serenity_error(&endpoint, e).await
            }
        }
    }

    /// Ajouter une réaction avec rate limiting
    pub async fn add_reaction(
        &self,
        channel_id: ChannelId,
        message_id: MessageId,
        reaction_type: ReactionType,
        priority: RequestPriority,
    ) -> Result<()> {
        let endpoint = format!("/channels/{}/messages/{}/reactions", channel_id, message_id);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Ajouter la réaction
        match self.http.create_reaction(channel_id, message_id, &reaction_type).await {
            Ok(_) => Ok(()),
            Err(e) => {
                self.handle_serenity_error(&endpoint, e).await
            }
        }
    }

    /// Récupérer les messages d'un canal avec rate limiting
    pub async fn get_messages(
        &self,
        channel_id: ChannelId,
        limit: u8,
        priority: RequestPriority,
    ) -> Result<Vec<Message>> {
        let endpoint = format!("/channels/{}/messages", channel_id);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Récupérer les messages
        match channel_id.messages(&self.http, GetMessages::new().limit(limit)).await {
            Ok(messages) => Ok(messages),
            Err(e) => {
                self.handle_serenity_error(&endpoint, e).await
            }
        }
    }

    /// Bulk delete avec rate limiting spécial
    pub async fn bulk_delete_messages(
        &self,
        channel_id: ChannelId,
        message_ids: Vec<MessageId>,
        priority: RequestPriority,
    ) -> Result<()> {
        let endpoint = format!("/channels/{}/messages/bulk-delete", channel_id);
        
        // Bulk delete a des limites spéciales : max 100 messages
        for chunk in message_ids.chunks(100) {
            // Vérifier le rate limit
            self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
            
            // Supprimer le chunk
            match channel_id.delete_messages(&self.http, chunk).await {
                Ok(_) => {},
                Err(e) => {
                    return self.handle_serenity_error(&endpoint, e).await;
                }
            }
            
            // Attendre un peu entre les chunks
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
        }
        
        Ok(())
    }

    /// Récupérer les informations d'un utilisateur avec rate limiting
    pub async fn get_user(
        &self,
        user_id: UserId,
        priority: RequestPriority,
    ) -> Result<User> {
        let endpoint = format!("/users/{}", user_id);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Récupérer l'utilisateur
        match user_id.to_user(&self.http).await {
            Ok(user) => Ok(user),
            Err(e) => {
                self.handle_serenity_error(&endpoint, e).await
            }
        }
    }

    /// Récupérer les informations d'une guild avec rate limiting
    pub async fn get_guild(
        &self,
        guild_id: GuildId,
        priority: RequestPriority,
    ) -> Result<PartialGuild> {
        let endpoint = format!("/guilds/{}", guild_id);
        
        // Vérifier le rate limit
        self.rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Récupérer la guild
        match guild_id.to_partial_guild(&self.http).await {
            Ok(guild) => Ok(guild),
            Err(e) => {
                self.handle_serenity_error(&endpoint, e).await
            }
        }
    }

    /// Exécuter une requête custom avec rate limiting
    pub async fn execute_custom_request(
        &self,
        request: reqwest::Request,
        priority: RequestPriority,
    ) -> Result<reqwest::Response> {
        self.reqwest_client
            .execute_with_rate_limit(request, &self.rate_limiter, priority)
            .await
    }

    /// Gérer les erreurs Serenity (extraire 429)
    async fn handle_serenity_error<T>(&self, endpoint: &str, error: SerenityError) -> Result<T> {
        if let SerenityError::Http(ref http_err) = error {
            if let HttpError::UnsuccessfulRequest(ref error_response) = http_err {
                if error_response.status_code == 429 {
                    // Extraire retry_after du message d'erreur ou utiliser défaut
                    let retry_after = 60; // Discord ne renvoie pas toujours les headers corrects
                    
                    self.rate_limiter
                        .handle_rate_limit_error(endpoint, retry_after)
                        .await?;
                }
            }
        }
        Err(error.into())
    }

    /// Obtenir les stats du rate limiter
    pub async fn get_rate_limit_stats(&self) -> voidbot_shared::RateLimitStats {
        self.rate_limiter.get_stats().await
    }

    /// Réinitialiser un bucket spécifique
    pub async fn reset_bucket(&self, endpoint: &str) {
        self.rate_limiter.reset_bucket(endpoint).await
    }

    /// Réinitialiser tous les buckets
    pub async fn reset_all_buckets(&self) {
        self.rate_limiter.reset_all_buckets().await
    }
}

/// Extension pour Context avec rate limiting
pub trait RateLimitedContext {
    fn rate_limited_http(&self) -> Arc<RateLimitedDiscordClient>;
}

impl RateLimitedContext for Context {
    fn rate_limited_http(&self) -> Arc<RateLimitedDiscordClient> {
        // Create a basic rate-limited client without access to BotData
        // This is a simplified version that doesn't have access to the actual rate limiter
        let rate_limiter = Arc::new(voidbot_shared::RateLimiter::new(voidbot_shared::RateLimitConfig::default()));
        
        Arc::new(RateLimitedDiscordClient::new(
            self.http.clone(),
            rate_limiter,
        ))
    }
}