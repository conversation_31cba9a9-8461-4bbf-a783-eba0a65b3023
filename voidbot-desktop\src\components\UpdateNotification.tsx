import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, Clock, Zap } from 'lucide-react';
import { useUpdateStore } from '../stores/updateStore';

interface UpdateNotificationProps {
  onOpenManager: () => void;
}

export const UpdateNotification: React.FC<UpdateNotificationProps> = ({ onOpenManager }) => {
  const { status, downloadAndInstall, setStatus } = useUpdateStore();
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Afficher la notification si une mise à jour est disponible
    if (status.available && status.update_info && !isDismissed) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [status.available, status.update_info, isDismissed]);

  const handleQuickInstall = async () => {
    setIsVisible(false);
    await downloadAndInstall();
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    setIsVisible(false);
  };

  const handleOpenManager = () => {
    setIsVisible(false);
    onOpenManager();
  };

  if (!isVisible || !status.update_info) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -100, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -100, scale: 0.9 }}
        className="fixed top-4 right-4 z-50 w-96"
      >
        <div className="bg-gray-900/95 backdrop-blur-md border border-gray-700 rounded-xl shadow-2xl overflow-hidden">
          {/* Header avec gradient */}
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-4 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-full">
                  <Zap className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <h3 className="text-white font-semibold">Mise à jour disponible</h3>
                  <p className="text-gray-300 text-sm">VoidBot v{status.update_info.version}</p>
                </div>
              </div>
              <button
                onClick={handleDismiss}
                className="p-1 hover:bg-gray-700 rounded-full transition-colors"
              >
                <X className="w-4 h-4 text-gray-400" />
              </button>
            </div>
          </div>

          {/* Body */}
          <div className="p-4">
            {/* Release info */}
            <div className="mb-4">
              <div className="flex items-center gap-2 text-sm text-gray-400 mb-2">
                <Clock className="w-4 h-4" />
                <span>
                  Sortie le {new Date(status.update_info.release_date).toLocaleDateString('fr-FR')}
                </span>
              </div>
              
              {/* Notes preview */}
              {status.update_info.notes && (
                <div className="text-gray-300 text-sm bg-gray-800/50 rounded-lg p-3">
                  <p className="line-clamp-3">
                    {status.update_info.notes.split('\n')[0] || 'Nouvelle version disponible avec améliorations et corrections.'}
                  </p>
                  {status.update_info.notes.length > 100 && (
                    <button
                      onClick={handleOpenManager}
                      className="text-indigo-400 hover:text-indigo-300 text-xs mt-1"
                    >
                      Voir les notes complètes
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <button
                onClick={handleQuickInstall}
                className="flex-1 flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Installer maintenant</span>
              </button>
              
              <button
                onClick={handleOpenManager}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg font-medium transition-colors"
              >
                Détails
              </button>
            </div>

            {/* Auto-update hint */}
            <div className="mt-3 text-xs text-gray-500 text-center">
              💡 Activez l'installation automatique dans les paramètres
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};