use serde::{Deserialize, Serialize};
use anyhow::{anyhow, Result};
use base64::{Engine as _, engine::general_purpose};
use std::fmt;

/// Token Discord chiffré pour stockage sécurisé
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecureToken {
    /// Token chiffré en base64
    encrypted_data: String,
    /// Nonce pour AES-GCM
    nonce: String,
    /// Timestamp de création pour expiration
    created_at: i64,
    /// Hash du token original pour validation rapide
    token_hash: String,
}

/// Service de chiffrement pour les données sensibles
#[derive(Debug)]
pub struct SecurityService {
    /// Clé maître dérivée du système
    master_key: [u8; 32],
}

impl SecurityService {
    /// Crée un nouveau service de sécurité avec une clé dérivée
    pub fn new() -> Result<Self> {
        let master_key = Self::derive_master_key()?;
        Ok(Self { master_key })
    }

    /// Dérive une clé maître depuis les informations système
    fn derive_master_key() -> Result<[u8; 32]> {
        use sha2::{Sha256, Digest};
        
        // Utilise des informations système pour dériver une clé unique
        let machine_id = Self::get_machine_identifier()?;
        let app_salt = b"VoidBot-2024-Security-Salt-v1";
        
        let mut hasher = Sha256::new();
        hasher.update(machine_id.as_bytes());
        hasher.update(app_salt);
        
        let hash = hasher.finalize();
        let mut key = [0u8; 32];
        key.copy_from_slice(&hash[..32]);
        
        Ok(key)
    }

    /// Obtient un identifiant unique de la machine
    fn get_machine_identifier() -> Result<String> {
        // Combine plusieurs sources pour un ID unique mais stable
        let hostname = std::env::var("COMPUTERNAME")
            .or_else(|_| std::env::var("HOSTNAME"))
            .unwrap_or_else(|_| "unknown".to_string());
            
        let username = std::env::var("USERNAME")
            .or_else(|_| std::env::var("USER"))
            .unwrap_or_else(|_| "unknown".to_string());
            
        // Note: En production, on pourrait utiliser des APIs système plus robustes
        // comme le UUID du BIOS ou le serial number du disque
        Ok(format!("{}_{}_voidbot", hostname, username))
    }

    /// Chiffre un token Discord
    pub fn encrypt_token(&self, token: &str) -> Result<SecureToken> {
        use aes_gcm::{Aes256Gcm, Key, Nonce, KeyInit, AeadInPlace};
        use rand::RngCore;
        
        // Validation du format du token Discord
        if !Self::is_valid_discord_token(token) {
            return Err(anyhow!("Format de token Discord invalide"));
        }
        
        // Génère un nonce aléatoire
        let mut nonce_bytes = [0u8; 12];
        rand::thread_rng().fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);
        
        // Prépare les données à chiffrer
        let mut buffer = token.as_bytes().to_vec();
        
        // Chiffre avec AES-256-GCM
        let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
        let cipher = Aes256Gcm::new(key);
        
        let tag = cipher.encrypt_in_place_detached(nonce, b"voidbot_token", &mut buffer)
            .map_err(|e| anyhow!("Erreur de chiffrement: {}", e))?;
        
        // Combine données chiffrées + tag
        buffer.extend_from_slice(&tag);
        
        // Encode en base64 pour stockage
        let encrypted_data = general_purpose::STANDARD.encode(&buffer);
        let nonce_b64 = general_purpose::STANDARD.encode(&nonce_bytes);
        
        // Hash du token pour validation rapide
        let token_hash = Self::hash_token(token);
        
        Ok(SecureToken {
            encrypted_data,
            nonce: nonce_b64,
            created_at: chrono::Utc::now().timestamp(),
            token_hash,
        })
    }

    /// Déchiffre un token Discord
    pub fn decrypt_token(&self, secure_token: &SecureToken) -> Result<String> {
        use aes_gcm::{Aes256Gcm, Key, Nonce, KeyInit, AeadInPlace};
        
        // Vérifie l'âge du token (expiration de sécurité)
        let now = chrono::Utc::now().timestamp();
        if now - secure_token.created_at > 86400 * 30 { // 30 jours
            return Err(anyhow!("Token expiré pour des raisons de sécurité"));
        }
        
        // Décode depuis base64
        let nonce_bytes = general_purpose::STANDARD.decode(&secure_token.nonce)
            .map_err(|e| anyhow!("Nonce invalide: {}", e))?;
        let mut encrypted_data = general_purpose::STANDARD.decode(&secure_token.encrypted_data)
            .map_err(|e| anyhow!("Données chiffrées invalides: {}", e))?;
        
        if nonce_bytes.len() != 12 {
            return Err(anyhow!("Taille de nonce invalide"));
        }
        
        if encrypted_data.len() < 16 {
            return Err(anyhow!("Données chiffrées trop courtes"));
        }
        
        // Sépare les données du tag
        let tag_start = encrypted_data.len() - 16;
        let tag: [u8; 16] = encrypted_data[tag_start..].try_into()
            .map_err(|_| anyhow!("Tag de taille invalide"))?;
        encrypted_data.truncate(tag_start);
        
        // Déchiffre
        let nonce = Nonce::from_slice(&nonce_bytes);
        let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
        let cipher = Aes256Gcm::new(key);
        
        cipher.decrypt_in_place_detached(nonce, b"voidbot_token", &mut encrypted_data, &tag.into())
            .map_err(|e| anyhow!("Erreur de déchiffrement: {}", e))?;
        
        let token = String::from_utf8(encrypted_data)
            .map_err(|e| anyhow!("Token déchiffré invalide: {}", e))?;
        
        // Vérifie l'intégrité avec le hash
        if Self::hash_token(&token) != secure_token.token_hash {
            return Err(anyhow!("Intégrité du token compromise"));
        }
        
        Ok(token)
    }

    /// Valide le format d'un token Discord
    fn is_valid_discord_token(token: &str) -> bool {
        // Format de base : longueur et caractères autorisés
        if token.len() < 24 || token.len() > 100 {
            return false;
        }
        
        // Caractères autorisés dans un token Discord
        token.chars().all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '_' || c == '-')
    }

    /// Hash sécurisé d'un token pour validation d'intégrité
    fn hash_token(token: &str) -> String {
        use sha2::{Sha256, Digest};
        
        let mut hasher = Sha256::new();
        hasher.update(token.as_bytes());
        hasher.update(b"voidbot_integrity_salt");
        
        let result = hasher.finalize();
        general_purpose::STANDARD.encode(&result[..16]) // Garde 16 premiers bytes
    }

    /// Nettoie les données sensibles de la mémoire
    pub fn secure_zero(data: &mut [u8]) {
        // Utilise volatile pour éviter que le compilateur optimise
        for byte in data {
            unsafe {
                std::ptr::write_volatile(byte, 0);
            }
        }
    }
}

impl Drop for SecurityService {
    fn drop(&mut self) {
        // Nettoie la clé maître à la destruction
        Self::secure_zero(&mut self.master_key);
    }
}

/// Token sécurisé qui se nettoie automatiquement
#[derive(Clone)]
pub struct SecureString {
    data: Vec<u8>,
}

impl SecureString {
    pub fn new(s: String) -> Self {
        Self {
            data: s.into_bytes(),
        }
    }
    
    pub fn as_str(&self) -> &str {
        // Safe car on contrôle la construction
        unsafe { std::str::from_utf8_unchecked(&self.data) }
    }
    
    pub fn len(&self) -> usize {
        self.data.len()
    }
    
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }
}

impl Drop for SecureString {
    fn drop(&mut self) {
        SecurityService::secure_zero(&mut self.data);
    }
}

impl fmt::Debug for SecureString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SecureString([PROTECTED])")
    }
}

impl fmt::Display for SecureString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[PROTECTED]")
    }
}

// Ne pas implémenter Clone automatiquement pour éviter les fuites
// Note: secstr::SecStr est un type alias, pas un trait, donc on le commente
// impl secstr::SecStr for SecureString {
//     fn unsecure(&self) -> &str {
//         self.as_str()
//     }
// }

/// Service de stockage sécurisé pour la configuration
#[derive(Debug)]
pub struct SecureStorage {
    security: SecurityService,
    storage_path: std::path::PathBuf,
}

impl SecureStorage {
    pub fn new() -> Result<Self> {
        let security = SecurityService::new()?;
        
        // Détermine le chemin de stockage sécurisé
        let storage_path = Self::get_secure_storage_path()?;
        
        // Crée le dossier si nécessaire avec permissions restrictives
        if let Some(parent) = storage_path.parent() {
            std::fs::create_dir_all(parent)?;
            
            // Définit les permissions restrictives (Unix seulement)
            #[cfg(unix)]
            {
                use std::os::unix::fs::PermissionsExt;
                let mut perms = std::fs::metadata(parent)?.permissions();
                perms.set_mode(0o700); // rwx------ (propriétaire seulement)
                std::fs::set_permissions(parent, perms)?;
            }
        }
        
        Ok(Self {
            security,
            storage_path,
        })
    }

    /// Sauvegarde un token de manière sécurisée
    pub async fn save_token(&self, token: &str) -> Result<()> {
        let secure_token = self.security.encrypt_token(token)?;
        
        let json_data = serde_json::to_string_pretty(&secure_token)?;
        
        // Écrit de manière atomique (temp file + rename)
        let temp_path = self.storage_path.with_extension("tmp");
        tokio::fs::write(&temp_path, json_data).await?;
        tokio::fs::rename(&temp_path, &self.storage_path).await?;
        
        // Définit les permissions restrictives sur le fichier
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(&self.storage_path).await?.permissions();
            perms.set_mode(0o600); // rw------- (propriétaire seulement)
            tokio::fs::set_permissions(&self.storage_path, perms).await?;
        }
        
        Ok(())
    }

    /// Charge un token de manière sécurisée
    pub async fn load_token(&self) -> Result<Option<SecureString>> {
        if !tokio::fs::try_exists(&self.storage_path).await? {
            return Ok(None);
        }
        
        let json_data = tokio::fs::read_to_string(&self.storage_path).await?;
        let secure_token: SecureToken = serde_json::from_str(&json_data)?;
        
        let token = self.security.decrypt_token(&secure_token)?;
        Ok(Some(SecureString::new(token)))
    }

    /// Supprime le token stocké
    pub async fn delete_token(&self) -> Result<()> {
        if tokio::fs::try_exists(&self.storage_path).await? {
            tokio::fs::remove_file(&self.storage_path).await?;
        }
        Ok(())
    }

    /// Détermine le chemin de stockage sécurisé
    fn get_secure_storage_path() -> Result<std::path::PathBuf> {
        let mut path = dirs::config_dir()
            .ok_or_else(|| anyhow!("Impossible de déterminer le dossier de configuration"))?;
        
        path.push("VoidBot");
        path.push("secure");
        path.push("credentials.dat");
        
        Ok(path)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_encryption_decryption() {
        let security = SecurityService::new().unwrap();
        let original_token = "OTkxMjM0NTY3ODkwMTIzNDU2.GTest.1234567890123456789012345678901234567890";
        
        let encrypted = security.encrypt_token(original_token).unwrap();
        let decrypted = security.decrypt_token(&encrypted).unwrap();
        
        assert_eq!(original_token, decrypted);
    }

    #[test]
    fn test_invalid_token_format() {
        let security = SecurityService::new().unwrap();
        let invalid_token = "invalid_token";
        
        assert!(security.encrypt_token(invalid_token).is_err());
    }

    #[test]
    fn test_secure_string_cleanup() {
        let secure_str = SecureString::new("sensitive_data".to_string());
        assert_eq!(secure_str.as_str(), "sensitive_data");
        // Le Drop automatique devrait nettoyer la mémoire
    }
}