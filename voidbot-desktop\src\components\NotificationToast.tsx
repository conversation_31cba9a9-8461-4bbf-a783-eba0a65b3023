import React, { useState, useEffect } from 'react';

interface NotificationToastProps {
  message: string;
  type: 'success' | 'error' | 'info';
  duration?: number;
  onClose: () => void;
}

export const NotificationToast: React.FC<NotificationToastProps> = ({ 
  message, 
  type, 
  duration = 3000, 
  onClose 
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 200); // Attendre la fin de l'animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-500',
          text: 'text-white',
          icon: '✅'
        };
      case 'error':
        return {
          bg: 'bg-red-500',
          text: 'text-white',
          icon: '❌'
        };
      case 'info':
        return {
          bg: 'bg-blue-500',
          text: 'text-white',
          icon: 'ℹ️'
        };
      default:
        return {
          bg: 'bg-gray-500',
          text: 'text-white',
          icon: '📢'
        };
    }
  };

  const colors = getColors();

  return (
    <div 
      className={`fixed bottom-4 right-4 max-w-sm p-4 rounded-lg shadow-lg transition-all duration-200 z-50 ${
        colors.bg
      } ${colors.text} ${
        isVisible ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-2'
      }`}
    >
      <div className="flex items-center gap-3">
        <span className="text-lg">{colors.icon}</span>
        <span className="font-medium">{message}</span>
        <button 
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 200);
          }}
          className="ml-auto text-white hover:text-gray-200 transition-colors"
        >
          ×
        </button>
      </div>
    </div>
  );
};

// Hook pour gérer les notifications
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    message: string;
    type: 'success' | 'error' | 'info';
    duration?: number;
  }>>([]);

  const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'success', duration?: number) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, message, type, duration }]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Écouter les événements personnalisés
  useEffect(() => {
    const handleNotificationEvent = (event: CustomEvent) => {
      const { message, type, duration } = event.detail;
      showNotification(message, type, duration);
    };

    window.addEventListener('showNotification', handleNotificationEvent as EventListener);
    return () => window.removeEventListener('showNotification', handleNotificationEvent as EventListener);
  }, []);

  return {
    notifications,
    showNotification,
    removeNotification
  };
};
