#!/bin/bash

# ================================================================
# Script de préparation et déploiement Railway pour VoidBot
# Vérifie le site et prépare pour le déploiement
# ================================================================

set -e

# Couleurs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
WEBSITE_DIR="$PROJECT_ROOT/website"

echo ""
echo "🚂 VoidBot - Préparation Déploiement Railway"
echo "============================================"
echo ""

# Vérifier la structure
log_info "Vérification de la structure du projet..."

if [ ! -d "$WEBSITE_DIR" ]; then
    log_error "Dossier website/ non trouvé"
    exit 1
fi

cd "$WEBSITE_DIR"
log_success "Dossier website trouvé: $WEBSITE_DIR"

# Vérifier Node.js
log_info "Vérification de l'environnement..."

if ! command -v node &> /dev/null; then
    log_error "Node.js non trouvé"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_error "npm non trouvé"
    exit 1
fi

NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
log_success "Node.js $NODE_VERSION et npm $NPM_VERSION détectés"

# Vérifier les fichiers de configuration
log_info "Vérification des fichiers de configuration..."

if [ ! -f "package.json" ]; then
    log_error "package.json manquant"
    exit 1
fi

if [ ! -f "railway.toml" ]; then
    log_error "railway.toml manquant"
    exit 1
fi

if [ ! -f "astro.config.mjs" ]; then
    log_error "astro.config.mjs manquant"
    exit 1
fi

log_success "Fichiers de configuration OK"

# Installer les dépendances
log_info "Installation des dépendances..."

if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        log_error "Erreur installation npm"
        exit 1
    fi
else
    log_success "Dépendances déjà installées"
fi

# Test du build
log_info "Test du build Astro..."
npm run build

if [ $? -ne 0 ]; then
    log_error "Erreur lors du build"
    exit 1
fi

log_success "Build réussi, dossier dist/ généré"

# Vérifier la taille du build
if [ -d "dist" ]; then
    BUILD_SIZE=$(du -sh dist | cut -f1)
    log_info "Taille du build: $BUILD_SIZE"
else
    log_error "Dossier dist/ non généré"
    exit 1
fi

# Test du serveur preview
log_info "Test du serveur preview..."

# Démarrer le serveur en arrière-plan
npm run preview &
PREVIEW_PID=$!

# Attendre que le serveur démarre
sleep 5

# Tester la connectivité
if curl -s http://localhost:4321 >/dev/null 2>&1; then
    log_success "Serveur preview fonctionne"
    
    # Tester quelques pages importantes
    log_info "Test des pages principales..."
    
    PAGES=("/" "/features" "/download" "/docs" "/support")
    for page in "${PAGES[@]}"; do
        if curl -s "http://localhost:4321$page" >/dev/null 2>&1; then
            echo "   ✅ $page"
        else
            echo "   ❌ $page"
        fi
    done
    
else
    log_error "Serveur preview ne répond pas"
    kill $PREVIEW_PID 2>/dev/null || true
    exit 1
fi

# Arrêter le serveur
kill $PREVIEW_PID 2>/dev/null || true
sleep 2

log_success "Test du serveur terminé"

# Vérifier les assets
log_info "Vérification des assets..."

REQUIRED_ASSETS=(
    "public/favicon.ico"
    "public/og-image.png"
    "public/downloads/.gitkeep"
    "src/layouts/Layout.astro"
    "src/pages/index.astro"
)

for asset in "${REQUIRED_ASSETS[@]}"; do
    if [ -f "$asset" ]; then
        echo "   ✅ $asset"
    else
        echo "   ⚠️  $asset manquant (non critique)"
    fi
done

# Vérifier la configuration Railway
log_info "Vérification configuration Railway..."

# Extraire les commandes de railway.toml
BUILD_CMD=$(grep "buildCommand" railway.toml | cut -d'"' -f2)
START_CMD=$(grep "startCommand" railway.toml | cut -d'"' -f2)

log_info "Commande build: $BUILD_CMD"
log_info "Commande start: $START_CMD"

# Vérifier que les commandes existent dans package.json
if grep -q "\"build\":" package.json && grep -q "\"preview\":" package.json; then
    log_success "Scripts package.json compatibles Railway"
else
    log_warning "Scripts package.json non standard"
fi

# Résumé final
echo ""
log_success "🎉 PRÉPARATION RAILWAY TERMINÉE !"
echo "=================================="
echo ""
echo "📊 Résumé:"
echo "   Node.js: $NODE_VERSION"
echo "   npm: $NPM_VERSION"
echo "   Build size: $BUILD_SIZE"
echo "   Configuration: ✅ Railway ready"
echo ""
echo "🚀 Prochaines étapes pour déploiement:"
echo "   1. Commit et push sur GitHub:"
echo "      git add ."
echo "      git commit -m \"Prepare Railway deployment\""
echo "      git push origin main"
echo ""
echo "   2. Créer projet Railway:"
echo "      - Aller sur https://railway.app/dashboard"
echo "      - \"New Project\" → \"Deploy from GitHub repo\""
echo "      - Sélectionner le repository VoidBot"
echo "      - Root directory: /website"
echo ""
echo "   3. Configuration automatique:"
echo "      - Railway détecte Astro automatiquement"
echo "      - Build: $BUILD_CMD"
echo "      - Start: $START_CMD"
echo "      - Port: 4321 (auto-détecté)"
echo ""
echo "   4. Accès au site:"
echo "      - URL Railway: https://voidbot-[hash].up.railway.app"
echo "      - Déploiement automatique depuis GitHub"
echo ""
echo "📖 Documentation complète: RAILWAY_DEPLOYMENT.md"
echo ""
log_success "Site prêt pour Railway ! 🚂"