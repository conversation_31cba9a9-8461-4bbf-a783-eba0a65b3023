use serenity::all::{Context, Message, ChannelId, GuildId, UserId, MessageId};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{Duration, Instant};
use anyhow::Result;
use reqwest::Client;
use serde_json::{json, Value};
use voidbot_shared::auto_commands::*;

pub struct AutoCommandsManager {
    state: Arc<RwLock<AutoCommandsState>>,
    http_client: Client,
    last_activity: Arc<RwLock<HashMap<u64, Instant>>>, // Pour cooldowns
}

impl AutoCommandsManager {
    pub fn new() -> Self {
        Self {
            state: Arc::new(RwLock::new(AutoCommandsState::default())),
            http_client: Client::new(),
            last_activity: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    // Handler principal pour les messages
    pub async fn handle_message(&self, ctx: &Context, msg: &Message) -> Result<()> {
        // Ignorer nos propres messages
        if msg.author.id == ctx.cache.current_user().id {
            return Ok(());
        }

        let state = self.state.read().await;

        // Auto-translate
        if state.auto_translate.enabled {
            self.handle_auto_translate(ctx, msg, &state.auto_translate).await?;
        }

        // Auto-slash
        if state.auto_slash.enabled {
            self.handle_auto_slash(ctx, msg, &state.auto_slash).await?;
        }

        // Auto-reply
        if state.auto_reply.enabled {
            self.handle_auto_reply(ctx, msg, &state.auto_reply).await?;
        }

        Ok(())
    }

    async fn handle_auto_translate(
        &self,
        ctx: &Context,
        msg: &Message,
        config: &AutoTranslateConfig,
    ) -> Result<()> {
        // Vérifications préliminaires
        if config.exclude_bots && msg.author.bot {
            return Ok(());
        }

        if msg.content.len() < config.min_length {
            return Ok(());
        }

        // Vérifier si le canal est autorisé
        if !config.channels.is_empty() && !config.channels.contains(&msg.channel_id.get()) {
            return Ok(());
        }

        // Éviter les traductions en boucle (vérifier cache)
        let mut state = self.state.write().await;
        if let Some(last_translation) = state.last_translation_cache.get(&msg.channel_id.get()) {
            if last_translation == &msg.content {
                return Ok(());
            }
        }

        // Traduire le message
        if let Ok(translated) = self.translate_text(&msg.content, &config.source_lang, &config.target_lang, &config.service).await {
            // Éviter de traduire si c'est identique ou très similaire
            if translated != msg.content && !is_similar(&translated, &msg.content) {
                // Envoyer la traduction
                let translation_msg = format!(
                    "🌐 **Traduction** ({} → {}):\n{}",
                    detect_language(&msg.content).unwrap_or_else(|| config.source_lang.clone()),
                    config.target_lang,
                    translated
                );

                msg.channel_id.say(&ctx.http, translation_msg).await?;

                // Mettre à jour le cache
                state.last_translation_cache.insert(msg.channel_id.get(), msg.content.clone());

                // Émettre événement pour le frontend
                self.emit_auto_command_event(AutoCommandEvent::TranslationTriggered {
                    channel_id: msg.channel_id.get(),
                    original_text: msg.content.clone(),
                    translated_text: translated,
                    source_lang: config.source_lang.clone(),
                    target_lang: config.target_lang.clone(),
                }).await;
            }
        }

        Ok(())
    }

    async fn handle_auto_slash(
        &self,
        ctx: &Context,
        msg: &Message,
        config: &AutoSlashConfig,
    ) -> Result<()> {
        // Vérifier les cooldowns
        if let Some(guild_id) = msg.guild_id {
            let cooldown_key = (guild_id.get(), msg.author.id.get());
            let state = self.state.read().await;
            
            if let Some(last_use) = state.slash_cooldowns.get(&cooldown_key) {
                let guild_settings = config.guild_settings.get(&guild_id.get());
                let cooldown_seconds = guild_settings
                    .map(|s| s.cooldown_seconds)
                    .unwrap_or(5);
                
                if *last_use + cooldown_seconds > chrono::Utc::now().timestamp() as u64 {
                    return Ok(());
                }
            }
        }

        // Chercher les triggers correspondants
        for (trigger, slash_config) in &config.triggers {
            if !slash_config.enabled {
                continue;
            }

            let matches = if slash_config.exact_match {
                if slash_config.case_sensitive {
                    msg.content == *trigger
                } else {
                    msg.content.to_lowercase() == trigger.to_lowercase()
                }
            } else {
                if slash_config.case_sensitive {
                    msg.content.contains(trigger)
                } else {
                    msg.content.to_lowercase().contains(&trigger.to_lowercase())
                }
            };

            if matches {
                // Vérifier les permissions du serveur
                if let Some(guild_id) = msg.guild_id {
                    if let Some(guild_settings) = config.guild_settings.get(&guild_id.get()) {
                        if !guild_settings.enabled {
                            continue;
                        }

                        if !guild_settings.allowed_channels.is_empty() 
                            && !guild_settings.allowed_channels.contains(&msg.channel_id.get()) {
                            continue;
                        }

                        // TODO: Vérifier les rôles autorisés
                    }
                }

                // Attendre le délai configuré
                if slash_config.delay_ms > 0 {
                    tokio::time::sleep(Duration::from_millis(slash_config.delay_ms)).await;
                }

                // Exécuter la commande slash
                self.execute_slash_command(ctx, msg, &slash_config.command, &slash_config.options).await?;

                // Mettre à jour les cooldowns
                if let Some(guild_id) = msg.guild_id {
                    let cooldown_key = (guild_id.get(), msg.author.id.get());
                    let mut state = self.state.write().await;
                    state.slash_cooldowns.insert(cooldown_key, chrono::Utc::now().timestamp() as u64);
                }

                // Émettre événement
                self.emit_auto_command_event(AutoCommandEvent::SlashTriggered {
                    guild_id: msg.guild_id.map(|g| g.get()),
                    channel_id: msg.channel_id.get(),
                    trigger: trigger.clone(),
                    command: slash_config.command.clone(),
                    user_id: msg.author.id.get(),
                }).await;

                break; // Une seule commande par message
            }
        }

        Ok(())
    }

    async fn handle_auto_reply(
        &self,
        ctx: &Context,
        msg: &Message,
        config: &AutoReplyConfig,
    ) -> Result<()> {
        // Vérifier cooldown global et par utilisateur
        let now = chrono::Utc::now().timestamp() as u64;
        let state = self.state.read().await;

        if let Some(last_reply) = state.reply_cooldowns.get(&msg.author.id.get()) {
            if now - last_reply < config.per_user_cooldown_seconds {
                return Ok(());
            }
        }

        // Chercher les triggers correspondants
        for (trigger, reply_config) in &config.triggers {
            if !reply_config.enabled {
                continue;
            }

            let matches = if reply_config.case_sensitive {
                msg.content.contains(trigger)
            } else {
                msg.content.to_lowercase().contains(&trigger.to_lowercase())
            };

            if matches {
                // Vérifier la probabilité
                if reply_config.probability < 1.0 {
                    use rand::Rng;
                    let mut rng = rand::thread_rng();
                    if rng.gen::<f32>() > reply_config.probability {
                        continue;
                    }
                }

                // Choisir une réponse
                let response = if reply_config.random_responses.is_empty() {
                    reply_config.response.clone()
                } else {
                    use rand::seq::SliceRandom;
                    let mut rng = rand::thread_rng();
                    reply_config.random_responses
                        .choose(&mut rng)
                        .unwrap_or(&reply_config.response)
                        .clone()
                };

                // Envoyer la réponse
                if reply_config.dm_only {
                    msg.author.direct_message(&ctx.http, serenity::builder::CreateMessage::new().content(response.clone())).await?;
                } else {
                    msg.channel_id.say(&ctx.http, &response).await?;
                }

                // Supprimer le message déclencheur si demandé
                if reply_config.delete_trigger {
                    let _ = msg.delete(&ctx.http).await; // Ignorer les erreurs (permissions)
                }

                // Mettre à jour les cooldowns
                let mut state = self.state.write().await;
                state.reply_cooldowns.insert(msg.author.id.get(), now);

                // Émettre événement
                self.emit_auto_command_event(AutoCommandEvent::AutoReplyTriggered {
                    channel_id: msg.channel_id.get(),
                    trigger: trigger.clone(),
                    response,
                    user_id: msg.author.id.get(),
                }).await;

                break; // Une seule réponse par message
            }
        }

        Ok(())
    }

    async fn translate_text(
        &self,
        text: &str,
        source_lang: &str,
        target_lang: &str,
        service: &TranslationService,
    ) -> Result<String> {
        match service {
            TranslationService::Google => self.translate_with_google(text, source_lang, target_lang).await,
            TranslationService::DeepL => self.translate_with_deepl(text, source_lang, target_lang).await,
            TranslationService::Bing => self.translate_with_bing(text, source_lang, target_lang).await,
        }
    }

    async fn translate_with_google(&self, text: &str, source: &str, target: &str) -> Result<String> {
        // Utiliser l'API Google Translate gratuite (attention aux limites)
        let url = "https://translate.googleapis.com/translate_a/single";
        let params = [
            ("client", "gtx"),
            ("sl", source),
            ("tl", target),
            ("dt", "t"),
            ("q", text),
        ];

        let response = self.http_client
            .get(url)
            .query(&params)
            .send()
            .await?;

        let json: Value = response.json().await?;
        
        if let Some(translations) = json.get(0).and_then(|v| v.as_array()) {
            let mut result = String::new();
            for translation in translations {
                if let Some(text) = translation.get(0).and_then(|v| v.as_str()) {
                    result.push_str(text);
                }
            }
            Ok(result)
        } else {
            Err(anyhow::anyhow!("Réponse de traduction invalide"))
        }
    }

    async fn translate_with_deepl(&self, text: &str, source: &str, target: &str) -> Result<String> {
        // TODO: Implémenter DeepL API (nécessite clé API)
        Err(anyhow::anyhow!("DeepL non encore implémenté"))
    }

    async fn translate_with_bing(&self, text: &str, source: &str, target: &str) -> Result<String> {
        // TODO: Implémenter Bing Translator API
        Err(anyhow::anyhow!("Bing Translator non encore implémenté"))
    }

    async fn execute_slash_command(
        &self,
        ctx: &Context,
        msg: &Message,
        command: &str,
        options: &HashMap<String, String>,
    ) -> Result<()> {
        // Cette fonction devrait déclencher l'exécution d'une commande slash
        // Pour l'instant, on simule en envoyant un message
        let command_text = if options.is_empty() {
            format!("🤖 **Auto-slash exécuté**: `{}`", command)
        } else {
            let options_str = options
                .iter()
                .map(|(k, v)| format!("{}:{}", k, v))
                .collect::<Vec<_>>()
                .join(" ");
            format!("🤖 **Auto-slash exécuté**: `{}` avec options: `{}`", command, options_str)
        };

        msg.channel_id.say(&ctx.http, command_text).await?;
        Ok(())
    }

    async fn emit_auto_command_event(&self, event: AutoCommandEvent) {
        // Émettre l'événement vers le frontend Tauri
        // TODO: Intégrer avec le système d'événements Tauri
        println!("🔔 Auto-command event: {:?}", event);
    }

    // API pour le frontend
    pub async fn get_config(&self) -> AutoCommandsState {
        self.state.read().await.clone()
    }

    pub async fn update_auto_translate_config(&self, config: AutoTranslateConfig) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_translate = config;
        
        self.emit_auto_command_event(AutoCommandEvent::ConfigUpdated {
            component: "translate".to_string(),
        }).await;
        
        Ok(())
    }

    pub async fn update_auto_slash_config(&self, config: AutoSlashConfig) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_slash = config;
        
        self.emit_auto_command_event(AutoCommandEvent::ConfigUpdated {
            component: "slash".to_string(),
        }).await;
        
        Ok(())
    }

    pub async fn update_auto_reply_config(&self, config: AutoReplyConfig) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_reply = config;
        
        self.emit_auto_command_event(AutoCommandEvent::ConfigUpdated {
            component: "reply".to_string(),
        }).await;
        
        Ok(())
    }

    pub async fn add_slash_trigger(&self, keyword: String, trigger: SlashTrigger) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_slash.triggers.insert(keyword, trigger);
        Ok(())
    }

    pub async fn remove_slash_trigger(&self, keyword: &str) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_slash.triggers.remove(keyword);
        Ok(())
    }

    pub async fn add_auto_reply(&self, keyword: String, reply: AutoReply) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_reply.triggers.insert(keyword, reply);
        Ok(())
    }

    pub async fn remove_auto_reply(&self, keyword: &str) -> Result<()> {
        let mut state = self.state.write().await;
        state.auto_reply.triggers.remove(keyword);
        Ok(())
    }

    pub async fn clear_cooldowns(&self) -> Result<()> {
        let mut state = self.state.write().await;
        state.slash_cooldowns.clear();
        state.reply_cooldowns.clear();
        Ok(())
    }
}

// Fonctions utilitaires
fn is_similar(text1: &str, text2: &str) -> bool {
    // Simple comparaison de similarité (peut être améliorée)
    let similarity = calculate_similarity(text1, text2);
    similarity > 0.8 // 80% de similarité = considéré comme identique
}

fn calculate_similarity(s1: &str, s2: &str) -> f32 {
    // Algorithme simple de distance de Levenshtein normalisée
    let len1 = s1.chars().count();
    let len2 = s2.chars().count();
    
    if len1 == 0 && len2 == 0 {
        return 1.0;
    }
    
    let max_len = len1.max(len2);
    let distance = levenshtein_distance(s1, s2);
    
    1.0 - (distance as f32 / max_len as f32)
}

fn levenshtein_distance(s1: &str, s2: &str) -> usize {
    let chars1: Vec<char> = s1.chars().collect();
    let chars2: Vec<char> = s2.chars().collect();
    let len1 = chars1.len();
    let len2 = chars2.len();

    let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];

    for i in 0..=len1 {
        matrix[i][0] = i;
    }
    for j in 0..=len2 {
        matrix[0][j] = j;
    }

    for i in 1..=len1 {
        for j in 1..=len2 {
            let cost = if chars1[i - 1] == chars2[j - 1] { 0 } else { 1 };
            matrix[i][j] = (matrix[i - 1][j] + 1)
                .min(matrix[i][j - 1] + 1)
                .min(matrix[i - 1][j - 1] + cost);
        }
    }

    matrix[len1][len2]
}

fn detect_language(text: &str) -> Option<String> {
    // Détection simple de langue basée sur des mots-clés
    // TODO: Utiliser une vraie bibliothèque de détection de langue
    let text_lower = text.to_lowercase();
    
    if text_lower.contains("the ") || text_lower.contains(" and ") || text_lower.contains(" is ") {
        Some("en".to_string())
    } else if text_lower.contains(" le ") || text_lower.contains(" la ") || text_lower.contains(" et ") {
        Some("fr".to_string())
    } else if text_lower.contains(" el ") || text_lower.contains(" la ") || text_lower.contains(" y ") {
        Some("es".to_string())
    } else {
        None
    }
}