import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

interface LoginManagerConfig {
  enabled: boolean;
  max_accounts: number;
  auto_switch_on_error: boolean;
  save_credentials: boolean;
  session_timeout_hours: number;
  validate_tokens_on_startup: boolean;
  backup_tokens_encrypted: boolean;
}

interface DiscordAccount {
  id: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
  phone?: string;
  verified: boolean;
  mfa_enabled: boolean;
  premium_type: 'None' | 'NitroClassic' | 'NitroBoost' | 'NitroBasic';
  flags: number;
  token_hash: string;
  added_at: string;
  last_used: string;
  last_validated?: string;
  status: 'Active' | 'Inactive' | 'Invalid' | 'Banned' | 'Disabled' | 'RateLimited' | 'Unknown';
  notes?: string;
  features: string[];
  guild_count?: number;
  friend_count?: number;
}

interface AccountStats {
  total_accounts: number;
  active_accounts: number;
  invalid_accounts: number;
  last_switch?: string;
  switch_count: number;
  total_login_time: number;
}

interface LoginSession {
  account_id: string;
  started_at: string;
  last_activity: string;
  expires_at: string;
  ip_address?: string;
  user_agent?: string;
  features_enabled: string[];
}

const PremiumBadge = ({ premiumType }: { premiumType: string }) => {
  if (premiumType === 'None') return null;

  const getPremiumConfig = (type: string) => {
    switch (type) {
      case 'NitroClassic': return { label: 'Nitro Classic', color: 'from-purple-500 to-pink-500', icon: '👑' };
      case 'NitroBoost': return { label: 'Nitro', color: 'from-purple-600 to-blue-600', icon: '💎' };
      case 'NitroBasic': return { label: 'Nitro Basic', color: 'from-blue-500 to-purple-500', icon: '⭐' };
      default: return { label: type, color: 'from-gray-500 to-gray-600', icon: '🎫' };
    }
  };

  const config = getPremiumConfig(premiumType);

  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${config.color} shadow-lg`}
    >
      <span>{config.icon}</span>
      <span>{config.label}</span>
    </motion.div>
  );
};

const StatusIndicator = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Active': return { color: 'bg-green-500', glow: 'shadow-green-500/50', icon: '✅', text: 'Actif' };
      case 'Inactive': return { color: 'bg-gray-500', glow: 'shadow-gray-500/50', icon: '⏸️', text: 'Inactif' };
      case 'Invalid': return { color: 'bg-red-500', glow: 'shadow-red-500/50', icon: '❌', text: 'Token invalide' };
      case 'Banned': return { color: 'bg-red-600', glow: 'shadow-red-600/50', icon: '🚫', text: 'Banni' };
      case 'Disabled': return { color: 'bg-orange-500', glow: 'shadow-orange-500/50', icon: '⚠️', text: 'Désactivé' };
      case 'RateLimited': return { color: 'bg-yellow-500', glow: 'shadow-yellow-500/50', icon: '⏳', text: 'Rate limité' };
      default: return { color: 'bg-gray-400', glow: 'shadow-gray-400/50', icon: '❓', text: 'Inconnu' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <motion.div 
      className="flex items-center space-x-2"
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <div className={`w-3 h-3 rounded-full ${config.color} ${config.glow} shadow-lg animate-pulse`} />
      <span className="text-sm text-gray-300">{config.text}</span>
    </motion.div>
  );
};

const AccountCard = ({ 
  account, 
  isActive, 
  onSwitch, 
  onEdit, 
  onDelete 
}: {
  account: DiscordAccount;
  isActive: boolean;
  onSwitch: (accountId: string) => void;
  onEdit: (account: DiscordAccount) => void;
  onDelete: (accountId: string) => void;
}) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className={`bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-4 border transition-all duration-300 ${
        isActive 
          ? 'border-indigo-500/50 shadow-lg shadow-indigo-500/25' 
          : 'border-gray-600/50 hover:border-indigo-500/30'
      }`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Avatar */}
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
              {account.username[0].toUpperCase()}
            </div>
            {isActive && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-800 animate-pulse" />
            )}
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <h3 className="text-white font-semibold text-lg">
                {account.username}
                <span className="text-gray-400 text-sm ml-1">#{account.discriminator}</span>
              </h3>
              
              {isActive && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-600 text-white">
                  🎯 Actuel
                </span>
              )}
              
              <PremiumBadge premiumType={account.premium_type} />
              
              {account.verified && (
                <span className="text-green-400" title="Compte vérifié">
                  ✅
                </span>
              )}
              
              {account.mfa_enabled && (
                <span className="text-blue-400" title="2FA activé">
                  🔐
                </span>
              )}
            </div>
            
            <div className="text-sm text-gray-400 mt-1">
              ID: <code className="bg-gray-700 px-1 rounded text-xs">{account.id}</code>
            </div>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500 mt-2">
              <span>Ajouté: {new Date(account.added_at).toLocaleDateString('fr-FR')}</span>
              <span>•</span>
              <span>Dernière utilisation: {new Date(account.last_used).toLocaleDateString('fr-FR')}</span>
              {account.guild_count && (
                <>
                  <span>•</span>
                  <span>🏠 {account.guild_count} serveurs</span>
                </>
              )}
              {account.friend_count && (
                <>
                  <span>•</span>
                  <span>👥 {account.friend_count} amis</span>
                </>
              )}
            </div>

            <div className="mt-2">
              <StatusIndicator status={account.status} />
            </div>

            {account.notes && (
              <div className="mt-2 p-2 bg-gray-700/50 rounded-lg">
                <div className="text-xs text-gray-400">Notes:</div>
                <div className="text-sm text-gray-300">{account.notes}</div>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {!isActive && account.status === 'Active' && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onSwitch(account.id)}
              className="p-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              title="Activer ce compte"
            >
              🔄
            </motion.button>
          )}
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowDetails(!showDetails)}
            className="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
            title="Voir détails"
          >
            📊
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onEdit(account)}
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            title="Modifier"
          >
            ✏️
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onDelete(account.id)}
            className="p-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            title="Supprimer"
            disabled={isActive}
          >
            🗑️
          </motion.button>
        </div>
      </div>

      {/* Expanded Details */}
      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 pt-4 border-t border-gray-600/50"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-gray-400">Email:</div>
                <div className="text-white">{account.email || 'Non renseigné'}</div>
              </div>
              <div>
                <div className="text-gray-400">Téléphone:</div>
                <div className="text-white">{account.phone || 'Non renseigné'}</div>
              </div>
              <div>
                <div className="text-gray-400">Flags:</div>
                <div className="text-white">{account.flags}</div>
              </div>
              <div>
                <div className="text-gray-400">Dernière validation:</div>
                <div className="text-white">
                  {account.last_validated 
                    ? new Date(account.last_validated).toLocaleString('fr-FR')
                    : 'Jamais'
                  }
                </div>
              </div>
            </div>
            
            {account.features.length > 0 && (
              <div className="mt-3">
                <div className="text-gray-400 text-sm mb-2">Fonctionnalités Discord:</div>
                <div className="flex flex-wrap gap-1">
                  {account.features.map((feature, index) => (
                    <span 
                      key={index}
                      className="px-2 py-1 bg-blue-600/20 text-blue-300 rounded text-xs"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const StatsCard = ({ title, value, subtitle, icon, color = 'indigo' }: {
  title: string;
  value: string | number;
  subtitle: string;
  icon: string;
  color?: string;
}) => {
  const colorClasses = {
    indigo: 'from-indigo-500/20 to-purple-600/20 border-indigo-500/30',
    green: 'from-green-500/20 to-emerald-600/20 border-green-500/30',
    blue: 'from-blue-500/20 to-cyan-600/20 border-blue-500/30',
    red: 'from-red-500/20 to-pink-600/20 border-red-500/30',
  };

  return (
    <motion.div
      whileHover={{ scale: 1.05, y: -2 }}
      className={`bg-gradient-to-br ${colorClasses[color as keyof typeof colorClasses]} backdrop-blur-sm rounded-xl p-4 border`}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold text-white">{value}</div>
          <div className="text-sm text-gray-300">{title}</div>
          <div className="text-xs text-gray-400 mt-1">{subtitle}</div>
        </div>
        <div className="text-3xl opacity-80">{icon}</div>
      </div>
    </motion.div>
  );
};

export default function LoginManager() {
  const [config, setConfig] = useState<LoginManagerConfig>({
    enabled: true,
    max_accounts: 10,
    auto_switch_on_error: false,
    save_credentials: true,
    session_timeout_hours: 24,
    validate_tokens_on_startup: true,
    backup_tokens_encrypted: true,
  });

  const [accounts, setAccounts] = useState<DiscordAccount[]>([]);
  const [activeAccountId, setActiveAccountId] = useState<string | null>(null);
  const [stats, setStats] = useState<AccountStats>({
    total_accounts: 0,
    active_accounts: 0,
    invalid_accounts: 0,
    switch_count: 0,
    total_login_time: 0,
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'accounts' | 'config' | 'sessions'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Add Account Modal
  const [showAddModal, setShowAddModal] = useState(false);
  const [newAccountData, setNewAccountData] = useState({
    token: '',
    notes: '',
  });

  // Edit Account Modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState<DiscordAccount | null>(null);

  const [sessions, setSessions] = useState<LoginSession[]>([]);

  useEffect(() => {
    loadConfig();
    loadAccounts();
    loadStats();
    loadSessions();

    // Auto-refresh des données toutes les 10 secondes
    const interval = setInterval(() => {
      if (config.enabled) {
        loadAccounts();
        loadStats();
        loadSessions();
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [config.enabled]);

  const loadConfig = async () => {
    try {
      const loginConfig = await invoke<LoginManagerConfig>('get_login_manager_config');
      setConfig(loginConfig);
    } catch (error) {
      console.error('Erreur lors du chargement de la config Login Manager:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      const accountsList = await invoke<DiscordAccount[]>('get_all_accounts');
      setAccounts(accountsList);
      
      const activeId = await invoke<string | null>('get_active_account_id');
      setActiveAccountId(activeId);
    } catch (error) {
      console.error('Erreur lors du chargement des comptes:', error);
    }
  };

  const loadStats = async () => {
    try {
      const accountStats = await invoke<AccountStats>('get_account_stats');
      setStats(accountStats);
    } catch (error) {
      console.error('Erreur lors du chargement des stats:', error);
    }
  };

  const loadSessions = async () => {
    try {
      const sessionList = await invoke<LoginSession[]>('get_active_sessions');
      setSessions(sessionList);
    } catch (error) {
      console.error('Erreur lors du chargement des sessions:', error);
    }
  };

  const updateConfig = async (newConfig: LoginManagerConfig) => {
    try {
      await invoke('update_login_manager_config', { config: newConfig });
      setConfig(newConfig);
      toast.success('✅ Configuration mise à jour', {
        style: { background: '#10B981', color: 'white' },
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('❌ Erreur lors de la mise à jour');
    }
  };

  const addAccount = async () => {
    if (!newAccountData.token) {
      toast.error('Token requis');
      return;
    }

    try {
      await invoke('add_account', {
        token: newAccountData.token,
        notes: newAccountData.notes || null,
      });
      
      toast.success('👤 Compte ajouté avec succès', {
        style: { background: '#10B981', color: 'white' },
      });
      
      setNewAccountData({ token: '', notes: '' });
      setShowAddModal(false);
      await loadAccounts();
      await loadStats();
    } catch (error) {
      console.error('Erreur lors de l\'ajout:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const switchAccount = async (accountId: string) => {
    try {
      await invoke('switch_account', { accountId });
      setActiveAccountId(accountId);
      toast.success('🔄 Compte activé', {
        style: { background: '#3B82F6', color: 'white' },
      });
      await loadAccounts();
      await loadStats();
    } catch (error) {
      console.error('Erreur lors du switch:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const deleteAccount = async (accountId: string) => {
    try {
      await invoke('remove_account', { accountId });
      toast.success('🗑️ Compte supprimé');
      await loadAccounts();
      await loadStats();
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const validateAccount = async (accountId: string) => {
    try {
      await invoke('validate_account', { accountId });
      toast.success('✅ Compte validé');
      await loadAccounts();
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const editAccount = (account: DiscordAccount) => {
    setEditingAccount(account);
    setShowEditModal(true);
  };

  const updateAccountNotes = async (accountId: string, notes: string) => {
    try {
      await invoke('update_account_notes', { accountId, notes });
      toast.success('📝 Notes mises à jour');
      setShowEditModal(false);
      setEditingAccount(null);
      await loadAccounts();
    } catch (error) {
      console.error('Erreur lors de la mise à jour des notes:', error);
      toast.error(`❌ ${error}`);
    }
  };

  // Filtrer les comptes
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.id.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || account.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const tabs = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: '📊' },
    { id: 'accounts', label: 'Comptes', icon: '👥' },
    { id: 'config', label: 'Configuration', icon: '⚙️' },
    { id: 'sessions', label: 'Sessions', icon: '🔐' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
              👥 Login Manager
            </h1>
            <p className="text-gray-400 mt-2">
              Gestion seamless de vos comptes Discord multiples
              {!config.enabled && (
                <span className="text-red-400 ml-2 animate-pulse">● DÉSACTIVÉ</span>
              )}
              {config.enabled && (
                <span className="text-green-400 ml-2 animate-pulse">● ACTIF</span>
              )}
            </p>
          </div>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowAddModal(true)}
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-medium transition-all duration-300 shadow-lg shadow-indigo-500/25"
          >
            ➕ Ajouter un compte
          </motion.button>
        </motion.div>

        {/* Warning si désactivé */}
        <AnimatePresence>
          {!config.enabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 border border-yellow-700/50 rounded-xl p-4 backdrop-blur-sm"
            >
              <div className="flex items-center">
                <div className="text-yellow-400 text-2xl mr-3 animate-bounce">⚠️</div>
                <div>
                  <h3 className="text-yellow-400 font-medium text-lg">Login Manager désactivé</h3>
                  <p className="text-yellow-300 text-sm mt-1">
                    Activez le gestionnaire de comptes pour utiliser la fonctionnalité multi-comptes
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          <StatsCard
            title="Comptes total"
            value={stats.total_accounts}
            subtitle="Tous les comptes"
            icon="👥"
            color="indigo"
          />
          <StatsCard
            title="Comptes actifs"
            value={stats.active_accounts}
            subtitle="Prêts à utiliser"
            icon="✅"
            color="green"
          />
          <StatsCard
            title="Comptes invalides"
            value={stats.invalid_accounts}
            subtitle="Tokens expirés"
            icon="❌"
            color="red"
          />
          <StatsCard
            title="Switches"
            value={stats.switch_count}
            subtitle="Changements de compte"
            icon="🔄"
            color="blue"
          />
        </motion.div>

        {/* Tabs Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="border-b border-gray-700/50"
        >
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                whileHover={{ y: -2 }}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-400 bg-indigo-500/10 rounded-t-lg'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                }`}
              >
                <span className="mr-2 text-lg">{tab.icon}</span>
                {tab.label}
              </motion.button>
            ))}
          </nav>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Quick Actions */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">🚀 Actions rapides</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setActiveTab('accounts')}
                      className="p-4 bg-indigo-600/20 border border-indigo-500/30 rounded-lg text-left hover:bg-indigo-600/30 transition-colors"
                    >
                      <div className="text-2xl mb-2">👤</div>
                      <div className="text-white font-medium">Gérer comptes</div>
                      <div className="text-gray-400 text-sm">Ajouter/modifier/supprimer</div>
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        accounts.forEach(account => {
                          if (account.status !== 'Invalid') {
                            validateAccount(account.id);
                          }
                        });
                      }}
                      className="p-4 bg-green-600/20 border border-green-500/30 rounded-lg text-left hover:bg-green-600/30 transition-colors"
                    >
                      <div className="text-2xl mb-2">✅</div>
                      <div className="text-white font-medium">Valider tous</div>
                      <div className="text-gray-400 text-sm">Vérifier tous les tokens</div>
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setActiveTab('config')}
                      className="p-4 bg-purple-600/20 border border-purple-500/30 rounded-lg text-left hover:bg-purple-600/30 transition-colors"
                    >
                      <div className="text-2xl mb-2">⚙️</div>
                      <div className="text-white font-medium">Configuration</div>
                      <div className="text-gray-400 text-sm">Paramètres avancés</div>
                    </motion.button>
                  </div>
                </div>

                {/* Recent Accounts */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">📈 Comptes récents</h3>
                  {accounts.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">👻</div>
                      <div className="text-gray-400 text-lg">Aucun compte configuré</div>
                      <div className="text-gray-500 text-sm">Ajoutez vos comptes Discord pour commencer</div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {accounts.slice(0, 6).map((account) => (
                        <motion.div
                          key={account.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-lg border ${
                            activeAccountId === account.id
                              ? 'bg-indigo-600/20 border-indigo-500/30'
                              : 'bg-gray-700/50 border-gray-600/30'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              {account.username[0].toUpperCase()}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium truncate">
                                {account.username}
                                {activeAccountId === account.id && <span className="text-indigo-400 ml-1">●</span>}
                              </div>
                              <StatusIndicator status={account.status} />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Accounts Tab */}
            {activeTab === 'accounts' && (
              <div className="space-y-6">
                {/* Search and Filter */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="🔍 Rechercher par nom ou ID..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      />
                    </div>
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                    >
                      <option value="all">Tous les statuts</option>
                      <option value="Active">Actif</option>
                      <option value="Inactive">Inactif</option>
                      <option value="Invalid">Invalide</option>
                      <option value="Banned">Banni</option>
                      <option value="Disabled">Désactivé</option>
                      <option value="RateLimited">Rate limité</option>
                    </select>
                  </div>
                </div>

                {/* Accounts List */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-semibold text-white">
                      👥 Comptes Discord ({filteredAccounts.length})
                    </h3>
                  </div>
                  
                  {filteredAccounts.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">🔍</div>
                      <div className="text-gray-400 text-xl">
                        {searchTerm || statusFilter !== 'all' 
                          ? 'Aucun compte trouvé' 
                          : 'Aucun compte configuré'
                        }
                      </div>
                      <div className="text-gray-500 text-sm mt-2">
                        {searchTerm || statusFilter !== 'all' 
                          ? 'Essayez de modifier vos filtres' 
                          : 'Ajoutez vos comptes Discord pour commencer'
                        }
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <AnimatePresence>
                        {filteredAccounts.map((account) => (
                          <AccountCard
                            key={account.id}
                            account={account}
                            isActive={activeAccountId === account.id}
                            onSwitch={switchAccount}
                            onEdit={editAccount}
                            onDelete={deleteAccount}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Config Tab */}
            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-6">⚙️ Configuration Login Manager</h3>
                  
                  <div className="space-y-8">
                    {/* Général */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">📊 Paramètres généraux</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Nombre maximum de comptes
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="50"
                            value={config.max_accounts}
                            onChange={(e) => setConfig(prev => ({ ...prev, max_accounts: parseInt(e.target.value) || 1 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Timeout de session (heures)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="168"
                            value={config.session_timeout_hours}
                            onChange={(e) => setConfig(prev => ({ ...prev, session_timeout_hours: parseInt(e.target.value) || 1 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Options avancées */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">🎯 Options avancées</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                          { key: 'enabled', label: 'Activer le gestionnaire de comptes', icon: '🔓', desc: 'Permettre la gestion multi-comptes' },
                          { key: 'auto_switch_on_error', label: 'Switch automatique en cas d\'erreur', icon: '🔄', desc: 'Changer de compte si le courant échoue' },
                          { key: 'save_credentials', label: 'Sauvegarder les identifiants', icon: '💾', desc: 'Conserver les tokens de manière sécurisée' },
                          { key: 'validate_tokens_on_startup', label: 'Valider les tokens au démarrage', icon: '✅', desc: 'Vérifier la validité des comptes' },
                          { key: 'backup_tokens_encrypted', label: 'Backup chiffré des tokens', icon: '🔐', desc: 'Sauvegarde sécurisée des données' },
                        ].map((setting) => (
                          <motion.label 
                            key={setting.key} 
                            className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                            whileHover={{ scale: 1.02 }}
                          >
                            <input
                              type="checkbox"
                              checked={config[setting.key as keyof LoginManagerConfig] as boolean}
                              onChange={(e) => setConfig(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                              className="w-4 h-4 text-indigo-600 bg-gray-700 border-gray-600 rounded focus:ring-indigo-500 mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-lg">{setting.icon}</span>
                                <span className="text-white font-medium">{setting.label}</span>
                              </div>
                              <div className="text-gray-400 text-sm mt-1">{setting.desc}</div>
                            </div>
                          </motion.label>
                        ))}
                      </div>
                    </div>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateConfig(config)}
                      className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg shadow-indigo-500/25"
                    >
                      ✅ Sauvegarder la configuration
                    </motion.button>
                  </div>
                </div>
              </div>
            )}

            {/* Sessions Tab */}
            {activeTab === 'sessions' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">🔐 Sessions actives</h3>
                  
                  {sessions.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">🔐</div>
                      <div className="text-gray-400 text-xl">Aucune session active</div>
                      <div className="text-gray-500 text-sm mt-2">
                        Les sessions apparaîtront ici lors de l'utilisation des comptes
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <AnimatePresence>
                        {sessions.map((session, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="bg-gray-700/50 rounded-lg p-4 border border-gray-600/30"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                <span className="text-lg">🔐</span>
                                <span className="text-white font-medium">
                                  {accounts.find(acc => acc.id === session.account_id)?.username || session.account_id}
                                </span>
                              </div>
                              <span className="text-gray-400 text-sm">
                                Expire: {new Date(session.expires_at).toLocaleString('fr-FR')}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <div className="text-gray-400">Démarrée:</div>
                                <div className="text-white">{new Date(session.started_at).toLocaleString('fr-FR')}</div>
                              </div>
                              <div>
                                <div className="text-gray-400">Dernière activité:</div>
                                <div className="text-white">{new Date(session.last_activity).toLocaleString('fr-FR')}</div>
                              </div>
                            </div>
                            {session.ip_address && (
                              <div className="text-sm mt-2">
                                <span className="text-gray-400">IP: </span>
                                <span className="text-white">{session.ip_address}</span>
                              </div>
                            )}
                            {session.features_enabled.length > 0 && (
                              <div className="mt-2">
                                <div className="text-gray-400 text-sm mb-1">Fonctionnalités actives:</div>
                                <div className="flex flex-wrap gap-1">
                                  {session.features_enabled.map((feature, idx) => (
                                    <span 
                                      key={idx}
                                      className="px-2 py-1 bg-green-600/20 text-green-300 rounded text-xs"
                                    >
                                      {feature}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </motion.div>
                        ))}
                      </AnimatePresence>
                    </div>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Add Account Modal */}
      <AnimatePresence>
        {showAddModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={() => setShowAddModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-600 max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-semibold text-white mb-4">➕ Ajouter un compte Discord</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Discord *
                  </label>
                  <input
                    type="password"
                    placeholder="Votre token Discord"
                    value={newAccountData.token}
                    onChange={(e) => setNewAccountData(prev => ({ ...prev, token: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Notes (optionnel)
                  </label>
                  <textarea
                    placeholder="Notes personnelles sur ce compte"
                    value={newAccountData.notes}
                    onChange={(e) => setNewAccountData(prev => ({ ...prev, notes: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 h-20 resize-none"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Annuler
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={addAccount}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  ✅ Ajouter
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Account Modal */}
      <AnimatePresence>
        {showEditModal && editingAccount && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={() => setShowEditModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-600 max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-semibold text-white mb-4">✏️ Modifier le compte</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Compte
                  </label>
                  <div className="text-white font-medium">
                    {editingAccount.username}#{editingAccount.discriminator}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Notes
                  </label>
                  <textarea
                    placeholder="Notes personnelles sur ce compte"
                    defaultValue={editingAccount.notes || ''}
                    onChange={(e) => setEditingAccount(prev => prev ? ({ ...prev, notes: e.target.value }) : null)}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 h-20 resize-none"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Annuler
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => updateAccountNotes(editingAccount.id, editingAccount.notes || '')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  ✅ Sauvegarder
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}