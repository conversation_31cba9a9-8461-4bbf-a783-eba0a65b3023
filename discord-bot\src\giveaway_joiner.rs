use serenity::all::*;
use voidbot_shared::{GiveawayManager, GiveawayInfo, GiveawayStatus, GiveawayEvent, JoinResult};
use std::sync::Arc;
use tokio::sync::Mutex;
use regex::Regex;
use rand::Rng;
use chrono::Utc;

/// Gestionnaire principal du Giveaway Joiner
pub struct GiveawayJoiner {
    manager: Arc<Mutex<GiveawayManager>>,
    regex_patterns: GiveawayPatterns,
}

/// Patterns regex pour détecter les giveaways
struct GiveawayPatterns {
    /// Pattern principal pour détecter un giveaway
    giveaway_pattern: Regex,
    /// Pattern pour extraire le prix
    prize_pattern: Regex,
    /// Pattern pour extraire le nombre de gagnants
    winner_pattern: Regex,
    /// Pattern pour extraire la durée
    duration_pattern: Regex,
    /// Pattern pour détecter l'émoji de réaction
    emoji_pattern: Regex,
}

impl GiveawayPatterns {
    pub fn new() -> Result<Self, regex::Error> {
        Ok(Self {
            giveaway_pattern: Regex::new(r"(?i)(giveaway|concours|🎉|🎁)")?,
            prize_pattern: Regex::new(r"(?i)(prize|prix|win|gagne|gain):\s*(.+?)(?:\n|$)")?,
            winner_pattern: Regex::new(r"(?i)(\d+)\s*(winner|gagnant|participant)")?,
            duration_pattern: Regex::new(r"(?i)(\d+)\s*(hour|heure|day|jour|minute|min|week|semaine)")?,
            emoji_pattern: Regex::new(r"(🎉|🎁|🎊|✨|💎|🔥|⚡|🌟)")?,
        })
    }
}

impl GiveawayJoiner {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(Self {
            manager: Arc::new(Mutex::new(GiveawayManager::new())),
            regex_patterns: GiveawayPatterns::new()?,
        })
    }

    /// Obtenir le gestionnaire de giveaways
    pub fn get_manager(&self) -> Arc<Mutex<GiveawayManager>> {
        self.manager.clone()
    }

    /// Analyser un message pour détecter un giveaway
    pub async fn analyze_message(&self, ctx: &Context, msg: &Message) -> Option<GiveawayInfo> {
        let manager = self.manager.lock().await;
        
        // Vérifier si les giveaways sont activés
        if !manager.config.enabled {
            return None;
        }

        // Vérifier si le serveur est surveillé
        if let Some(guild_id) = msg.guild_id {
            if !manager.config.monitored_guilds.is_empty() 
                && !manager.config.monitored_guilds.contains(&guild_id.get()) {
                return None;
            }

            // Vérifier si le serveur est blacklisté
            if manager.config.blacklisted_guilds.contains(&guild_id.get()) {
                return None;
            }
        }

        let content = &msg.content;
        let embeds = &msg.embeds;

        // Analyser le contenu du message
        if let Some(giveaway_info) = self.extract_giveaway_info(ctx, msg, content).await {
            return Some(giveaway_info);
        }

        // Analyser les embeds
        for embed in embeds {
            if let Some(giveaway_info) = self.extract_giveaway_from_embed(ctx, msg, embed).await {
                return Some(giveaway_info);
            }
        }

        None
    }

    /// Extraire les informations de giveaway du contenu texte
    async fn extract_giveaway_info(&self, ctx: &Context, msg: &Message, content: &str) -> Option<GiveawayInfo> {
        // Vérifier si c'est un giveaway
        if !self.regex_patterns.giveaway_pattern.is_match(content) {
            return None;
        }

        let guild_id = msg.guild_id?;
        let guild_name = {
            if let Some(guild_ref) = guild_id.to_guild_cached(&ctx.cache) {
                guild_ref.name.clone()
            } else {
                "Unknown Guild".to_string()
            }
        };
        let channel = msg.channel_id.to_channel(&ctx.http).await.ok()?;
        let channel_name = match channel {
            Channel::Guild(gc) => gc.name.clone(),
            _ => "Unknown".to_string(),
        };

        // Extraire le prix
        let prize = self.regex_patterns.prize_pattern
            .captures(content)
            .and_then(|cap| cap.get(2))
            .map(|m| m.as_str().trim().to_string())
            .unwrap_or_else(|| "Giveaway Prize".to_string());

        // Extraire le nombre de gagnants
        let winner_count = self.regex_patterns.winner_pattern
            .captures(content)
            .and_then(|cap| cap.get(1))
            .and_then(|m| m.as_str().parse().ok())
            .unwrap_or(1);

        // Détecter l'émoji de réaction
        let reaction_emoji = self.regex_patterns.emoji_pattern
            .find(content)
            .map(|m| m.as_str().to_string())
            .unwrap_or_else(|| "🎉".to_string());

        Some(GiveawayInfo {
            id: format!("{}_{}", msg.id.get(), guild_id.get()),
            message_id: msg.id.get(),
            channel_id: msg.channel_id.get(),
            guild_id: guild_id.get(),
            guild_name: guild_name.clone(),
            channel_name,
            prize,
            description: content.to_string(),
            reaction_emoji,
            winner_count,
            end_time: None, // TODO: Parser la durée
            host_user_id: msg.author.id.get(),
            host_username: msg.author.name.clone(),
            status: GiveawayStatus::Active,
            detected_at: Utc::now(),
            joined: false,
            joined_at: None,
            skip_reason: None,
        })
    }

    /// Extraire les informations de giveaway d'un embed
    async fn extract_giveaway_from_embed(&self, ctx: &Context, msg: &Message, embed: &Embed) -> Option<GiveawayInfo> {
        let title = embed.title.as_ref()?;
        let empty_string = String::new();
        let description = embed.description.as_ref().unwrap_or(&empty_string);
        
        // Vérifier si l'embed contient un giveaway
        let full_text = format!("{} {}", title, description);
        if !self.regex_patterns.giveaway_pattern.is_match(&full_text) {
            return None;
        }

        let guild_id = msg.guild_id?;
        let guild_name = {
            if let Some(guild_ref) = guild_id.to_guild_cached(&ctx.cache) {
                guild_ref.name.clone()
            } else {
                "Unknown Guild".to_string()
            }
        };
        let channel = msg.channel_id.to_channel(&ctx.http).await.ok()?;
        let channel_name = match channel {
            Channel::Guild(gc) => gc.name.clone(),
            _ => "Unknown".to_string(),
        };

        let prize = title.clone();
        let winner_count = self.regex_patterns.winner_pattern
            .captures(&full_text)
            .and_then(|cap| cap.get(1))
            .and_then(|m| m.as_str().parse().ok())
            .unwrap_or(1);

        Some(GiveawayInfo {
            id: format!("{}_{}", msg.id.get(), guild_id.get()),
            message_id: msg.id.get(),
            channel_id: msg.channel_id.get(),
            guild_id: guild_id.get(),
            guild_name: guild_name.clone(),
            channel_name,
            prize,
            description: description.clone(),
            reaction_emoji: "🎉".to_string(),
            winner_count,
            end_time: None,
            host_user_id: msg.author.id.get(),
            host_username: msg.author.name.clone(),
            status: GiveawayStatus::Active,
            detected_at: Utc::now(),
            joined: false,
            joined_at: None,
            skip_reason: None,
        })
    }

    /// Vérifier si un giveaway doit être rejoint
    pub async fn should_join_giveaway(&self, giveaway: &GiveawayInfo) -> Result<bool, String> {
        let manager = self.manager.lock().await;

        // Vérifier les mots-clés requis
        if !manager.config.required_keywords.is_empty() {
            let has_required = manager.config.required_keywords.iter().any(|keyword| {
                giveaway.description.to_lowercase().contains(&keyword.to_lowercase()) ||
                giveaway.prize.to_lowercase().contains(&keyword.to_lowercase())
            });
            
            if !has_required {
                return Err("Aucun mot-clé requis trouvé".to_string());
            }
        }

        // Vérifier les mots-clés interdits
        for forbidden in &manager.config.forbidden_keywords {
            if giveaway.description.to_lowercase().contains(&forbidden.to_lowercase()) ||
               giveaway.prize.to_lowercase().contains(&forbidden.to_lowercase()) {
                return Err(format!("Mot-clé interdit trouvé: {}", forbidden));
            }
        }

        Ok(true)
    }

    /// Rejoindre un giveaway
    pub async fn join_giveaway(&self, ctx: &Context, giveaway: &GiveawayInfo) -> JoinResult {
        let start_time = std::time::Instant::now();
        
        // Calculer le délai aléatoire
        let delay_ms = {
            let manager = self.manager.lock().await;
            let min_delay = manager.config.min_delay_seconds * 1000;
            let max_delay = manager.config.max_delay_seconds * 1000;
            rand::thread_rng().gen_range(min_delay..=max_delay) as u64
        };

        // Attendre le délai
        tokio::time::sleep(tokio::time::Duration::from_millis(delay_ms)).await;

        let result = self.try_join_giveaway(ctx, giveaway).await;
        
        JoinResult {
            success: result.is_ok(),
            message: result.unwrap_or_else(|e| e),
            giveaway_id: giveaway.id.clone(),
            attempted_at: Utc::now(),
            delay_used_ms: delay_ms,
        }
    }

    /// Tenter de rejoindre un giveaway (logique interne)
    async fn try_join_giveaway(&self, ctx: &Context, giveaway: &GiveawayInfo) -> Result<String, String> {
        let message_id = MessageId::new(giveaway.message_id);
        let channel_id = ChannelId::new(giveaway.channel_id);

        // Récupérer le message
        let message = channel_id.message(&ctx.http, message_id).await
            .map_err(|e| format!("Impossible de récupérer le message: {}", e))?;

        // Ajouter la réaction
        let emoji = if giveaway.reaction_emoji.chars().count() == 1 {
            ReactionType::Unicode(giveaway.reaction_emoji.clone())
        } else {
            ReactionType::Unicode("🎉".to_string())
        };

        message.react(&ctx.http, emoji).await
            .map_err(|e| format!("Impossible d'ajouter la réaction: {}", e))?;

        Ok("Participation réussie au giveaway".to_string())
    }

    /// Traiter un nouveau giveaway détecté
    pub async fn handle_detected_giveaway(&self, ctx: &Context, giveaway: GiveawayInfo) -> Option<GiveawayEvent> {
        // Vérifier s'il faut rejoindre automatiquement
        let should_join = self.should_join_giveaway(&giveaway).await;
        
        // Ajouter à la liste des giveaways détectés
        {
            let mut manager = self.manager.lock().await;
            manager.add_detected_giveaway(giveaway.clone());
        }

        // Si auto-join est activé et que le giveaway est valide
        let auto_join = {
            let manager = self.manager.lock().await;
            manager.config.auto_join
        };

        if auto_join && should_join.is_ok() {
            // Rejoindre en arrière-plan
            let ctx_clone = ctx.clone();
            let giveaway_clone = giveaway.clone();
            let joiner_clone = self.clone();
            
            tokio::spawn(async move {
                let result = joiner_clone.join_giveaway(&ctx_clone, &giveaway_clone).await;
                
                if result.success {
                    let mut manager = joiner_clone.manager.lock().await;
                    manager.mark_as_joined(&result.giveaway_id);
                }
            });

            Some(GiveawayEvent::GiveawayDetected { giveaway })
        } else if let Err(reason) = should_join {
            // Marquer comme ignoré
            let mut manager = self.manager.lock().await;
            manager.mark_as_skipped(&giveaway.id, reason.clone());
            
            Some(GiveawayEvent::GiveawaySkipped { 
                giveaway_id: giveaway.id, 
                reason 
            })
        } else {
            Some(GiveawayEvent::GiveawayDetected { giveaway })
        }
    }
}

// Implémentation de Clone pour permettre le partage entre threads
impl Clone for GiveawayJoiner {
    fn clone(&self) -> Self {
        Self {
            manager: self.manager.clone(),
            regex_patterns: GiveawayPatterns::new().expect("Failed to create regex patterns"),
        }
    }
}