[package]
name = "voidbot-backend-api"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
# Workspace dependencies
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
sqlx = { workspace = true }
reqwest = { workspace = true }
axum = { workspace = true }
chrono = { workspace = true }
uuid = { workspace = true }

# Local dependencies
voidbot-shared = { path = "../shared" }

# Additional dependencies for API - VERSIONS ÉPINGLÉES
tower = "=0.4.13"
tower-http = { version = "=0.5.2", features = ["cors"] }
env_logger = "=0.11.5"
clap = { version = "=4.5.20", features = ["derive"] }
dotenv = "=0.15.0"