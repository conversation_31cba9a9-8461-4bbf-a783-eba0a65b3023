---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const downloads = [
  {
    os: "Windows",
    icon: "🪟",
    version: "v1.0.0",
    size: "45 MB",
    format: ".msi",
    downloadUrl: "/downloads/voidbot-1.0.0-windows-x64.msi",
    sha256: "052d335d1a855e064bf3b78a4f7acaaf6b0548213a5544cb93b04ea6d3633e77",
    requirements: [
      "Windows 10/11 (x64)",
      "512 MB RAM minimum",
      "50 MB espace disque",
      "Connexion Internet"
    ]
  },
  {
    os: "macOS",
    icon: "🍎",
    version: "v1.0.0", 
    size: "42 MB",
    format: ".dmg",
    downloadUrl: "/downloads/voidbot-1.0.0-macos-universal.dmg",
    sha256: "8ca70c9dab32d04f36c2fbb54dd5bfa4e9e1bae7724c3a31c3884f7e973762d7",
    requirements: [
      "macOS 10.15+ (Intel/Apple Silicon)",
      "512 MB RAM minimum", 
      "50 MB espace disque",
      "Connexion Internet"
    ]
  },
  {
    os: "Linux",
    icon: "🐧",
    version: "v1.0.0",
    size: "38 MB", 
    format: ".deb",
    downloadUrl: "/downloads/voidbot-1.0.0-linux-x64.deb",
    sha256: "e19e4b0eb926f5abaa4eb6b328885767ec4697157db57fe8df7adcf6d5a48445",
    requirements: [
      "Ubuntu 20.04+ / Debian 11+",
      "512 MB RAM minimum",
      "50 MB espace disque", 
      "Connexion Internet"
    ]
  }
];

const features = [
  "Interface desktop native",
  "100+ commandes Discord",
  "Snipers ultra-rapides",
  "Mode furtif avancé",
  "Auto-commands intelligents",
  "Chiffrement AES-256"
];
---

<Layout title="Télécharger VoidBot - Toolkit Discord Gratuit" description="Téléchargez VoidBot gratuitement pour Windows, macOS et Linux. Installation en un clic avec auto-updater intégré.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl sm:text-5xl font-bold mb-6">
          <span class="text-white">Télécharger</span>
          <br>
          <span class="gradient-text">VoidBot</span>
        </h1>
        <p class="text-xl text-gray-300 mb-8">
          Installation gratuite en un clic. Compatible Windows, macOS et Linux.
          <br class="hidden sm:block">
          Auto-updater intégré pour les dernières fonctionnalités.
        </p>
        
        <!-- OS Detection Banner -->
        <div id="os-detection" class="hidden bg-indigo-500/10 border border-indigo-500/20 rounded-lg p-4 mb-8">
          <p class="text-indigo-400 font-medium">
            <span id="detected-os"></span> détecté. 
            <a href="#" id="auto-download" class="underline hover:text-indigo-300">
              Télécharger automatiquement
            </a>
          </p>
        </div>
      </div>
    </section>

    <!-- Download Cards -->
    <section class="py-12">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          {downloads.map((download, index) => (
            <div class="card-hover group text-center">
              <!-- OS Icon -->
              <div class="text-6xl mb-4 group-hover:scale-110 transition-transform duration-200">
                {download.icon}
              </div>
              
              <!-- OS Name -->
              <h3 class="text-2xl font-bold mb-2 text-white group-hover:text-indigo-400 transition-colors duration-200">
                {download.os}
              </h3>
              
              <!-- Version & Size -->
              <div class="flex justify-center gap-4 mb-4 text-sm text-gray-400">
                <span>{download.version}</span>
                <span>•</span>
                <span>{download.size}</span>
              </div>
              
              <!-- Download Button -->
              <a 
                href={download.downloadUrl}
                class="btn btn-primary w-full mb-6 glow group-hover:scale-105"
                data-os={download.os.toLowerCase()}
              >
                <span class="flex items-center justify-center gap-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Télécharger {download.format}
                </span>
              </a>
              
              <!-- Requirements -->
              <div class="text-left">
                <h4 class="font-semibold text-white mb-2">Prérequis :</h4>
                <ul class="space-y-1 text-sm text-gray-400">
                  {download.requirements.map((req) => (
                    <li class="flex items-center">
                      <svg class="w-3 h-3 text-green-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                      </svg>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
              
              <!-- SHA256 -->
              <details class="mt-4">
                <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-400">
                  Vérification SHA256
                </summary>
                <p class="text-xs text-gray-600 mt-2 font-mono break-all">
                  {download.sha256}
                </p>
              </details>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Features Overview -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-8">
          <span class="text-white">Ce que vous obtenez avec</span>
          <br>
          <span class="gradient-text">VoidBot</span>
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {features.map((feature, index) => (
            <div class="flex items-center justify-center md:justify-start gap-3 p-4 bg-gray-900/50 rounded-lg border border-gray-800 hover:border-gray-700 transition-colors duration-200">
              <svg class="w-5 h-5 text-indigo-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="text-gray-300 font-medium">{feature}</span>
            </div>
          ))}
        </div>
        
        <p class="text-lg text-gray-400 mb-8">
          Installation en moins de 60 secondes. Mise à jour automatique. 
          <br class="hidden sm:block">
          Support technique gratuit via Discord.
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/docs" class="btn btn-secondary text-lg px-8 py-4">
            Guide d'Installation
          </a>
          <a href="/support" class="btn btn-accent text-lg px-8 py-4">
            Support Discord
          </a>
        </div>
      </div>
    </section>

    <!-- Installation Steps -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">
          <span class="text-white">Installation</span>
          <span class="gradient-text"> Simple</span>
        </h2>
        
        <div class="space-y-8">
          <div class="flex items-start gap-6">
            <div class="flex-shrink-0 w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
              1
            </div>
            <div>
              <h3 class="text-xl font-semibold text-white mb-2">Téléchargez VoidBot</h3>
              <p class="text-gray-400">Choisissez la version correspondant à votre système d'exploitation ci-dessus.</p>
            </div>
          </div>
          
          <div class="flex items-start gap-6">
            <div class="flex-shrink-0 w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold">
              2
            </div>
            <div>
              <h3 class="text-xl font-semibold text-white mb-2">Installez l'application</h3>
              <p class="text-gray-400">Double-cliquez sur le fichier téléchargé et suivez les instructions d'installation.</p>
            </div>
          </div>
          
          <div class="flex items-start gap-6">
            <div class="flex-shrink-0 w-10 h-10 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold">
              3
            </div>
            <div>
              <h3 class="text-xl font-semibold text-white mb-2">Connectez votre compte Discord</h3>
              <p class="text-gray-400">Utilisez le login web sécurisé pour connecter votre compte Discord en quelques clics.</p>
            </div>
          </div>
          
          <div class="flex items-start gap-6">
            <div class="flex-shrink-0 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
              4
            </div>
            <div>
              <h3 class="text-xl font-semibold text-white mb-2">Profitez de VoidBot !</h3>
              <p class="text-gray-400">Accédez à plus de 100 commandes Discord avancées depuis l'interface moderne.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">
          <span class="text-white">Questions</span>
          <span class="gradient-text"> Fréquentes</span>
        </h2>
        
        <div class="space-y-6">
          <details class="card-hover group">
            <summary class="cursor-pointer text-lg font-semibold text-white mb-3 group-hover:text-indigo-400 transition-colors duration-200">
              VoidBot est-il vraiment gratuit ?
            </summary>
            <p class="text-gray-400 pl-6">
              Oui, VoidBot est 100% gratuit et open-source. Aucun abonnement premium, aucun coût caché. 
              Toutes les fonctionnalités sont disponibles gratuitement.
            </p>
          </details>
          
          <details class="card-hover group">
            <summary class="cursor-pointer text-lg font-semibold text-white mb-3 group-hover:text-indigo-400 transition-colors duration-200">
              Est-ce sécurisé pour mon compte Discord ?
            </summary>
            <p class="text-gray-400 pl-6">
              Absolument. VoidBot utilise le chiffrement AES-256-GCM pour protéger vos tokens Discord. 
              Toutes les données restent sur votre machine, rien n'est envoyé au cloud.
            </p>
          </details>
          
          <details class="card-hover group">
            <summary class="cursor-pointer text-lg font-semibold text-white mb-3 group-hover:text-indigo-400 transition-colors duration-200">
              Puis-je utiliser VoidBot sur plusieurs appareils ?
            </summary>
            <p class="text-gray-400 pl-6">
              Oui, vous pouvez installer VoidBot sur tous vos appareils. La licence est par utilisateur, 
              pas par machine. Synchronisation des settings via export/import.
            </p>
          </details>
          
          <details class="card-hover group">
            <summary class="cursor-pointer text-lg font-semibold text-white mb-3 group-hover:text-indigo-400 transition-colors duration-200">
              Comment obtenir de l'aide ?
            </summary>
            <p class="text-gray-400 pl-6">
              Rejoignez notre serveur Discord pour un support gratuit 24/7. 
              Documentation complète disponible et communauté active pour vous aider.
            </p>
          </details>
        </div>
      </div>
    </section>

    <!-- Additional Downloads -->
    <section class="py-12 bg-gray-900/30">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-white mb-8 text-center">Autres Architectures</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <!-- Windows x86 -->
          <div class="card text-center">
            <div class="text-4xl mb-3">🪟</div>
            <h3 class="text-lg font-semibold text-white mb-2">Windows 32-bit</h3>
            <p class="text-gray-400 text-sm mb-4">v1.0.0 • 43 MB</p>
            <a href="/downloads/voidbot-1.0.0-windows-x86.msi" class="btn btn-secondary w-full mb-4">
              Télécharger .msi (x86)
            </a>
            <details>
              <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-400">SHA256</summary>
              <p class="text-xs text-gray-600 mt-2 font-mono break-all">1637b0f7ca01d96403cba6f51c6a93147d5a470befbd226d9f303d7f99e17f1e</p>
            </details>
          </div>
          
          <!-- Linux ARM64 -->
          <div class="card text-center">
            <div class="text-4xl mb-3">🐧</div>
            <h3 class="text-lg font-semibold text-white mb-2">Linux ARM64</h3>
            <p class="text-gray-400 text-sm mb-4">v1.0.0 • 36 MB</p>
            <a href="/downloads/voidbot-1.0.0-linux-arm64.deb" class="btn btn-secondary w-full mb-4">
              Télécharger .deb (ARM64)
            </a>
            <details>
              <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-400">SHA256</summary>
              <p class="text-xs text-gray-600 mt-2 font-mono break-all">55c4e0388e02c466c29de8758ebf219b17a4d14e7ad853ea6ab670473ab59453</p>
            </details>
          </div>
          
          <!-- Linux AppImage -->
          <div class="card text-center">
            <div class="text-4xl mb-3">📦</div>
            <h3 class="text-lg font-semibold text-white mb-2">Linux Portable</h3>
            <p class="text-gray-400 text-sm mb-4">v1.0.0 • 41 MB</p>
            <a href="/downloads/voidbot-1.0.0-linux-x64.AppImage" class="btn btn-secondary w-full mb-4">
              Télécharger .AppImage
            </a>
            <details>
              <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-400">SHA256</summary>
              <p class="text-xs text-gray-600 mt-2 font-mono break-all">bc99c7352a010b29cae414fe460372f0f4715224e15af339f17c3d116c734866</p>
            </details>
          </div>
        </div>
      </div>
    </section>

    <!-- Alternative Downloads -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl font-bold text-white mb-8">Autres Options</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <a href="#" onclick="alert('GitHub Releases sera bientôt disponible')" class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">📦</div>
            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">
              GitHub Releases
            </h3>
            <p class="text-gray-400 text-sm">
              Versions de développement et historique complet
            </p>
          </a>
          
          <a href="/docs/build" class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">🔧</div>
            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">
              Compiler from Source
            </h3>
            <p class="text-gray-400 text-sm">
              Instructions pour compiler VoidBot vous-même
            </p>
          </a>
          
          <a href="/docs/portable" class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">💾</div>
            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">
              Version Portable
            </h3>
            <p class="text-gray-400 text-sm">
              Exécutable sans installation sur clé USB
            </p>
          </a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>

<script>
  // OS Detection
  function detectOS() {
    const userAgent = navigator.userAgent;
    
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    
    return null;
  }

  // Show OS detection banner
  document.addEventListener('DOMContentLoaded', () => {
    const detectedOS = detectOS();
    
    if (detectedOS) {
      const banner = document.getElementById('os-detection');
      const osText = document.getElementById('detected-os');
      const autoDownload = document.getElementById('auto-download');
      
      if (banner && osText && autoDownload) {
        osText.textContent = detectedOS;
        banner.classList.remove('hidden');
        
        // Set auto-download link
        const downloadButton = document.querySelector(`[data-os="${detectedOS.toLowerCase()}"]`);
        if (downloadButton) {
          autoDownload.href = downloadButton.href;
        }
      }
    }
  });

  // Download tracking
  document.querySelectorAll('a[href^="/downloads/"]').forEach(link => {
    link.addEventListener('click', (e) => {
      const filename = e.target.closest('a').href.split('/').pop();
      console.log('Download started:', filename);
      
      // Analytics tracking could go here
      if (typeof gtag === 'function') {
        gtag('event', 'download', {
          'event_category': 'engagement',
          'event_label': filename
        });
      }
    });
  });
</script>