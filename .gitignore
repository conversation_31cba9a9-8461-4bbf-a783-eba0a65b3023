# MCP
.mcp.json

# Rust
/target/
**/*.rs.bk
Cargo.lock

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Build outputs
/dist/
/build/
*.exe
*.dmg
*.app
*.AppImage
*.deb
*.rpm

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Security
keys/
secrets/
tokens/
*.pem
*.key
*.crt

# Tauri
src-tauri/target/
src-tauri/gen/

# Specific to VoidBot
voidbot.db
user_data/
themes/
scripts_cache/