# ================================================================
# Script PowerShell de build Windows pour VoidBot v1.0
# G<PERSON><PERSON> installeurs MSI et NSIS pour Windows x64 et x86
# ================================================================

[CmdletBinding()]
param(
    [switch]$SkipX86,
    [switch]$Clean,
    [switch]$Verbose
)

# Configuration
$ErrorActionPreference = "Stop"
$PROJECT_ROOT = Split-Path -Parent $PSScriptRoot
$DESKTOP_DIR = Join-Path $PROJECT_ROOT "voidbot-desktop"
$BUILD_DIR = Join-Path $PROJECT_ROOT "builds\windows"
$LOG_FILE = Join-Path $PROJECT_ROOT "build-windows.log"

# Fonctions d'affichage
function Write-Info($Message) {
    Write-Host "ℹ️  $Message" -ForegroundColor Cyan
}

function Write-Success($Message) {
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning($Message) {
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error($Message) {
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Header($Message) {
    Write-Host ""
    Write-Host "🚀 $Message" -ForegroundColor Magenta
    Write-Host ("=" * ($Message.Length + 3))
}

# Fonction de log
function Write-Log($Message) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "[$timestamp] $Message" | Out-File -Append -FilePath $LOG_FILE
    if ($Verbose) {
        Write-Host "LOG: $Message" -ForegroundColor Gray
    }
}

# Démarrage
Write-Header "VoidBot - Build Windows Automatisé"
Write-Log "Début du build Windows"

try {
    # Vérification de l'environnement
    Write-Info "Vérification de l'environnement..."
    
    # Vérifier Node.js
    try {
        $nodeVersion = & node --version 2>$null
        if (-not $nodeVersion) { throw }
    } catch {
        Write-Error "Node.js non trouvé"
        Write-Host "Installer Node.js depuis: https://nodejs.org" -ForegroundColor Yellow
        exit 1
    }
    
    # Vérifier npm
    try {
        $npmVersion = & npm --version 2>$null
        if (-not $npmVersion) { throw }
    } catch {
        Write-Error "npm non trouvé"
        exit 1
    }
    
    # Vérifier Rust
    try {
        $cargoVersion = & cargo --version 2>$null
        if (-not $cargoVersion) { throw }
    } catch {
        Write-Error "Rust/Cargo non trouvé"
        Write-Host "Installer Rust depuis: https://rustup.rs" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Success "Environnement prêt:"
    Write-Host "   Node.js: $nodeVersion" -ForegroundColor White
    Write-Host "   npm: $npmVersion" -ForegroundColor White  
    Write-Host "   Cargo: $cargoVersion" -ForegroundColor White
    Write-Log "Environnement vérifié - Node: $nodeVersion, npm: $npmVersion, Cargo: $cargoVersion"
    
    # Vérifier le dossier du projet
    if (-not (Test-Path $DESKTOP_DIR)) {
        Write-Error "Dossier voidbot-desktop non trouvé: $DESKTOP_DIR"
        exit 1
    }
    
    Set-Location $DESKTOP_DIR
    Write-Log "Changement vers dossier: $DESKTOP_DIR"
    
    # Nettoyer si demandé
    if ($Clean) {
        Write-Info "Nettoyage des builds précédents..."
        if (Test-Path "src-tauri\target") {
            Remove-Item "src-tauri\target" -Recurse -Force
        }
        if (Test-Path "dist") {
            Remove-Item "dist" -Recurse -Force
        }
        if (Test-Path "node_modules") {
            Remove-Item "node_modules" -Recurse -Force
        }
        Write-Success "Nettoyage terminé"
        Write-Log "Nettoyage effectué"
    }
    
    # Installation des dépendances
    Write-Info "Installation des dépendances..."
    
    if (-not (Test-Path "node_modules")) {
        Write-Host "   Installation npm en cours..." -ForegroundColor Yellow
        & npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Erreur installation npm"
            exit 1
        }
    } else {
        Write-Host "   Dépendances npm déjà installées" -ForegroundColor Gray
    }
    
    Write-Success "Dépendances prêtes"
    Write-Log "Dépendances npm installées"
    
    # Configuration Rust
    Write-Info "Configuration des cibles Rust..."
    & rustup target add x86_64-pc-windows-msvc 2>$null
    & rustup target add i686-pc-windows-msvc 2>$null
    Write-Success "Cibles Windows installées"
    Write-Log "Cibles Rust configurées"
    
    # Créer dossier de build
    if (-not (Test-Path $BUILD_DIR)) {
        New-Item -ItemType Directory -Path $BUILD_DIR -Force | Out-Null
        Write-Log "Dossier build créé: $BUILD_DIR"
    }
    
    # Génération des assets
    Write-Info "Génération des assets..."
    Set-Location "src-tauri"
    if (Test-Path "build-assets.py") {
        try {
            & python build-assets.py
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Assets générés"
                Write-Log "Assets générés avec succès"
            } else {
                Write-Warning "Erreur génération assets, continuation..."
                Write-Log "Erreur génération assets"
            }
        } catch {
            Write-Warning "Erreur génération assets, continuation..."
            Write-Log "Exception génération assets: $_"
        }
    } else {
        Write-Warning "Script build-assets.py non trouvé"
        Write-Log "Script build-assets.py manquant"
    }
    Set-Location ".."
    
    # Build Frontend
    Write-Info "Build Frontend React..."
    & npm run build:prod
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Erreur build frontend"
        exit 1
    }
    Write-Success "Frontend buildé"
    Write-Log "Frontend React buildé"
    
    # Build Windows x64
    Write-Info "Build Windows x64 (MSI + NSIS)..."
    Write-Host "   Ceci peut prendre 5-15 minutes..." -ForegroundColor Yellow
    Write-Log "Début build Windows x64"
    
    & npx tauri build --target x86_64-pc-windows-msvc --bundles msi,nsis
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Erreur build Windows x64"
        Write-Host "Vérifier les logs dans le dossier src-tauri/target" -ForegroundColor Yellow
        Write-Log "Erreur build Windows x64"
        exit 1
    }
    Write-Success "Build Windows x64 terminé"
    Write-Log "Build Windows x64 réussi"
    
    # Build Windows x86 (optionnel)
    if (-not $SkipX86) {
        Write-Info "Build Windows x86 (MSI + NSIS)..."
        Write-Host "   Ceci peut prendre 5-15 minutes..." -ForegroundColor Yellow
        Write-Log "Début build Windows x86"
        
        try {
            & npx tauri build --target i686-pc-windows-msvc --bundles msi,nsis
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Build Windows x86 terminé"
                Write-Log "Build Windows x86 réussi"
            } else {
                Write-Warning "Erreur build Windows x86, continuation..."
                Write-Log "Erreur build Windows x86"
            }
        } catch {
            Write-Warning "Erreur build Windows x86, continuation..."
            Write-Log "Exception build Windows x86: $_"
        }
    } else {
        Write-Info "Build x86 ignoré (-SkipX86)"
        Write-Log "Build x86 ignoré par paramètre"
    }
    
    # Copie des installeurs
    Write-Info "Copie des installeurs..."
    
    $TARGET_DIR = "src-tauri\target"
    $copied = 0
    
    # Copier installeurs x64
    $RELEASE_DIR_X64 = Join-Path $TARGET_DIR "x86_64-pc-windows-msvc\release\bundle"
    
    if (Test-Path "$RELEASE_DIR_X64\msi") {
        Get-ChildItem "$RELEASE_DIR_X64\msi\*.msi" | Copy-Item -Destination $BUILD_DIR -Force
        Write-Host "   ✅ MSI x64 copié" -ForegroundColor Green
        $copied++
    }
    
    if (Test-Path "$RELEASE_DIR_X64\nsis") {
        Get-ChildItem "$RELEASE_DIR_X64\nsis\*.exe" | Copy-Item -Destination $BUILD_DIR -Force
        Write-Host "   ✅ NSIS x64 copié" -ForegroundColor Green
        $copied++
    }
    
    # Copier installeurs x86 si disponibles
    if (-not $SkipX86) {
        $RELEASE_DIR_X86 = Join-Path $TARGET_DIR "i686-pc-windows-msvc\release\bundle"
        
        if (Test-Path "$RELEASE_DIR_X86\msi") {
            Get-ChildItem "$RELEASE_DIR_X86\msi\*.msi" | Copy-Item -Destination $BUILD_DIR -Force
            Write-Host "   ✅ MSI x86 copié" -ForegroundColor Green
            $copied++
        }
        
        if (Test-Path "$RELEASE_DIR_X86\nsis") {
            Get-ChildItem "$RELEASE_DIR_X86\nsis\*.exe" | Copy-Item -Destination $BUILD_DIR -Force
            Write-Host "   ✅ NSIS x86 copié" -ForegroundColor Green
            $copied++
        }
    }
    
    Write-Log "Installeurs copiés: $copied fichiers"
    
    # Résumé final
    Write-Host ""
    Write-Success "🎉 BUILD WINDOWS TERMINÉ !"
    Write-Host "========================" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "📁 Installeurs disponibles dans:" -ForegroundColor Cyan
    Write-Host "   $BUILD_DIR" -ForegroundColor White
    Write-Host ""
    Write-Host "📦 Fichiers générés:" -ForegroundColor Cyan
    
    if (Test-Path $BUILD_DIR) {
        Get-ChildItem $BUILD_DIR | ForEach-Object {
            $size = [math]::Round($_.Length / 1MB, 2)
            Write-Host "   $($_.Name) ($size MB)" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "🚀 Prêt pour distribution !" -ForegroundColor Green
    Write-Log "Build Windows terminé avec succès"
    
} catch {
    Write-Error "Erreur critique: $_"
    Write-Log "Erreur critique: $_"
    exit 1
}

# Pause si exécuté directement
if ($Host.Name -eq "ConsoleHost") {
    Write-Host ""
    Write-Host "Appuyer sur Entrée pour fermer..." -ForegroundColor Gray
    Read-Host
}