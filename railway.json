{"build": {"builder": "NIXPACKS", "buildCommand": "cargo build --release --bin voidbot-bot"}, "deploy": {"startCommand": "./target/release/voidbot-bot", "healthcheckPath": "/health", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}, "environments": {"production": {"variables": {"RUST_LOG": "info", "DISCORD_BOT_TOKEN": "${{DISCORD_BOT_TOKEN}}", "DATABASE_URL": "${{Postgres.DATABASE_URL}}"}}}}