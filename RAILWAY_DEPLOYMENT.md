# 🚂 Guide de Déploiement Railway - VoidBot

## 🎯 Objectif

Déployer le site vitrine VoidBot sur Railway pour une URL publique accessible.

## 📋 Prérequis

### **1. Compte Railway**
- Créer un compte sur https://railway.app
- Connecter avec GitHub (recommandé)

### **2. Repository GitHub**
- Code VoidBot pushé sur GitHub
- Branch principale avec le dossier `website/`

## 🚀 Déploiement Automatique

### **Étape 1 : Créer un nouveau projet**
1. Aller sur https://railway.app/dashboard
2. Cliquer "New Project"
3. Sélectionner "Deploy from GitHub repo"
4. Choisir le repository VoidBot
5. Configurer les paramètres

### **Étape 2 : Configuration du projet**
```yaml
# Configuration automatique détectée :
Build Command: npm ci && npm run build
Start Command: npm run preview
Port: 4321
Root Directory: /website
```

### **Étape 3 : Variables d'environnement**
```env
NODE_ENV=production
PORT=4321
HOST=0.0.0.0
```

### **Étape 4 : Configuration domaine**
- Railway génère automatiquement : `voidbot-xyz.up.railway.app`
- Optionnel : Configurer domaine personnalisé `voidbot.com`

## 🔧 Configuration Fichiers

### **railway.toml** (déjà configuré)
```toml
[build]
builder = "NIXPACKS"
buildCommand = "npm ci && npm run build"

[deploy]
startCommand = "npm run preview"
healthcheckPath = "/"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[environments.production]
NODE_ENV = "production"
```

### **package.json** (déjà configuré)
```json
{
  "scripts": {
    "build": "astro check && astro build",
    "preview": "astro preview --host 0.0.0.0 --port 4321",
    "start": "astro preview --host 0.0.0.0 --port 4321"
  }
}
```

## 📁 Structure Railway

```
website/                    # Root directory Railway
├── src/                   # Code source Astro
├── public/                # Assets statiques
├── dist/                  # Build output (généré)
├── package.json           # Dépendances et scripts
├── railway.toml           # Configuration Railway
├── astro.config.mjs       # Configuration Astro
└── tailwind.config.js     # Configuration Tailwind
```

## ⚡ Build et Déploiement

### **Process Railway :**
1. **Clone** du repository GitHub
2. **Détection** Astro/Node.js automatique
3. **Installation** `npm ci` 
4. **Build** `npm run build` → génère `dist/`
5. **Start** `npm run preview` → serveur Astro
6. **Deploy** sur URL Railway

### **Temps estimés :**
- **Premier déploiement** : 3-5 minutes
- **Redéploiements** : 1-2 minutes
- **Cold start** : 10-30 secondes

## 🌐 URLs et Accès

### **URL Railway (automatique) :**
```
https://voidbot-[hash].up.railway.app
```

### **Pages disponibles :**
- `/` - Landing page
- `/features` - Fonctionnalités détaillées
- `/download` - Téléchargements par OS
- `/docs` - Documentation
- `/support` - Support et FAQ
- `/discord` - Invitation Discord
- `/status` - Statut services

## 🔄 Auto-déploiement

### **GitHub Integration :**
- **Push sur main** → Redéploiement automatique
- **PR merge** → Build et deploy
- **Rollback** facile depuis dashboard Railway

### **Webhook Discord (optionnel) :**
```env
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...
```

## 📊 Monitoring Railway

### **Métriques disponibles :**
- **CPU Usage** - Utilisation processeur
- **Memory Usage** - Consommation RAM
- **Network** - Bande passante
- **Response Time** - Temps de réponse
- **Error Rate** - Taux d'erreur

### **Logs temps réel :**
```bash
# Dans Railway dashboard
railway logs --follow
```

## 🔒 Sécurité

### **HTTPS automatique :**
- Certificats SSL/TLS automatiques
- Redirection HTTP → HTTPS

### **Headers sécurité :**
```javascript
// astro.config.mjs
export default defineConfig({
  vite: {
    server: {
      headers: {
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    }
  }
});
```

## 💡 Optimisations

### **Performance :**
- **Static Site** - Astro génère du HTML statique
- **CDN Railway** - Distribution mondiale
- **Compression GZIP** - Automatique
- **Caching** - Headers appropriés

### **SEO :**
- **Sitemap** automatique Astro
- **Meta tags** optimisés
- **OpenGraph** pour réseaux sociaux
- **Schema.org** markup

## 🐛 Dépannage

### **Erreurs courantes :**

#### **"Build failed: npm run build"**
```bash
# Solution : Vérifier localement
cd website
npm install
npm run build
```

#### **"Port déjà utilisé"**
```bash
# Solution : Railway utilise PORT automatique
# Ne pas hardcoder le port
```

#### **"Module not found"**
```bash
# Solution : Vérifier package.json
npm ci  # au lieu de npm install
```

#### **"Site non accessible"**
```bash
# Solution : Vérifier healthcheck
startCommand = "npm run preview"
healthcheckPath = "/"
```

### **Debug logs :**
```bash
# Voir les logs Railway
railway logs

# Logs spécifiques
railway logs --service=website --tail=100
```

## 📈 Scalabilité

### **Plan Railway :**
- **Hobby** : 500h/mois gratuit
- **Pro** : $5/mois, ressources dédiées
- **Team** : À partir de $20/mois

### **Ressources :**
- **CPU** : 1 vCPU (auto-scale)
- **RAM** : 512MB-1GB (auto-scale)  
- **Storage** : 1GB (build cache)
- **Bandwidth** : 100GB/mois

## 🔄 Workflow Complet

### **Développement :**
```bash
# 1. Modifier le site
cd website
npm run dev

# 2. Tester localement
npm run build
npm run preview

# 3. Commit et push
git add .
git commit -m "Update website"
git push origin main
```

### **Déploiement automatique :**
```
Push GitHub → Railway détecte → Build → Deploy → URL mise à jour
```

## ✅ Checklist Déploiement

### **Avant déploiement :**
- [ ] Compte Railway créé et configuré
- [ ] Repository GitHub connecté
- [ ] Site se build localement sans erreur
- [ ] railway.toml configuré
- [ ] Variables d'environnement définies

### **Après déploiement :**
- [ ] URL Railway accessible
- [ ] Toutes les pages se chargent
- [ ] Assets (images, CSS) fonctionnent
- [ ] Liens de navigation OK
- [ ] Responsive design testé
- [ ] Performance satisfaisante (<3s chargement)

## 📞 Support

### **Ressources :**
- **Railway Docs** : https://docs.railway.app
- **Astro Docs** : https://docs.astro.build
- **Railway Discord** : https://discord.gg/railway

### **En cas de problème :**
1. Vérifier logs Railway dashboard
2. Tester build local identique
3. Consulter Railway status page
4. Contacter support Railway si nécessaire

---

**🎉 Site VoidBot sera accessible publiquement via Railway avec auto-déploiement depuis GitHub !**