import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Skull, 
  Play, 
  Square, 
  Timer, 
  Target, 
  MessageSquare,
  Keyboard,
  Repeat,
  Settings,
  AlertTriangle,
  Eye
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';

interface TrollSession {
  id: string;
  type: 'NoLeave' | 'GhostPing' | 'Spam' | 'FakeTyping' | 'InvisibleMessage' | 'AutoReact' | 'EchoMessages';
  target_user?: string;
  guild_id?: string;
  channel_id?: string;
  started_at: string;
  config: {
    target_user?: string;
    message_content?: string;
    delay_ms: number;
    max_actions: number;
    auto_stop_minutes?: number;
  };
}

const TrollIcons: Record<string, any> = {
  NoLeave: Target,
  GhostPing: Eye,
  Spam: MessageSquare,
  FakeTyping: Keyboard,
  InvisibleMessage: Eye,
  AutoReact: Repeat,
  EchoMessages: MessageSquare,
};

const TrollLabels: Record<string, string> = {
  NoLeave: 'No Leave',
  GhostPing: 'Ghost Ping',
  Spam: 'Spam',
  FakeTyping: 'Fake Typing',
  InvisibleMessage: 'Message Invisible',
  AutoReact: 'Auto React',
  EchoMessages: 'Echo Messages',
};

export function TrollControl() {
  const [activeTrolls, setActiveTrolls] = useState<TrollSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTroll, setSelectedTroll] = useState<string>('ghostping');
  
  // Paramètres pour nouveau troll
  const [targetUser, setTargetUser] = useState('');
  const [message, setMessage] = useState('');
  const [count, setCount] = useState(5);
  const [delay, setDelay] = useState(3);
  const [duration, setDuration] = useState(5);

  useEffect(() => {
    loadActiveTrolls();
    
    // Rafraîchir toutes les 5 secondes
    const interval = setInterval(loadActiveTrolls, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadActiveTrolls = async () => {
    try {
      // Note: Ces commandes Tauri devront être ajoutées
      const trolls = await invoke<TrollSession[]>('get_active_trolls');
      setActiveTrolls(trolls);
    } catch (error) {
      console.error('Erreur chargement trolls actifs:', error);
      setActiveTrolls([]);
    } finally {
      setLoading(false);
    }
  };

  const startTroll = async () => {
    try {
      switch (selectedTroll) {
        case 'ghostping':
          if (!targetUser) {
            alert('Utilisateur cible requis pour GhostPing');
            return;
          }
          await invoke('start_ghostping_troll', {
            targetUserId: targetUser,
            message: message || undefined
          });
          break;

        case 'spam':
          if (!message) {
            alert('Message requis pour Spam');
            return;
          }
          await invoke('start_spam_troll', {
            message,
            count,
            delaySeconds: delay
          });
          break;

        case 'typing':
          await invoke('start_fake_typing_troll', {
            durationMinutes: duration
          });
          break;

        case 'noleave':
          await invoke('start_noleave_troll', {
            targetUserId: targetUser || undefined
          });
          break;
      }

      // Rafraîchir la liste
      loadActiveTrolls();
      
      // Reset form
      setTargetUser('');
      setMessage('');
    } catch (error) {
      console.error('Erreur démarrage troll:', error);
      alert(`Erreur: ${error}`);
    }
  };

  const stopTroll = async (trollId: string) => {
    try {
      await invoke('stop_troll', { trollId });
      loadActiveTrolls();
    } catch (error) {
      console.error('Erreur arrêt troll:', error);
    }
  };

  const stopAllTrolls = async () => {
    try {
      await invoke('stop_all_trolls');
      loadActiveTrolls();
    } catch (error) {
      console.error('Erreur arrêt tous trolls:', error);
    }
  };

  const formatDuration = (startedAt: string) => {
    const start = new Date(startedAt);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    
    if (diffMins > 0) {
      return `${diffMins}m ${diffSecs}s`;
    }
    return `${diffSecs}s`;
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400">Chargement des trolls...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Skull className="w-8 h-8 text-red-500" />
          <div>
            <h1 className="text-2xl font-bold text-white">Contrôle des Trolls</h1>
            <p className="text-gray-400">Gérez vos trolls Discord actifs</p>
          </div>
        </div>

        {activeTrolls.length > 0 && (
          <button
            onClick={stopAllTrolls}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Square className="w-4 h-4" />
            <span>Arrêter Tous</span>
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trolls Actifs */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
            <Timer className="w-5 h-5 text-orange-500" />
            <span>Trolls Actifs ({activeTrolls.length})</span>
          </h2>

          {activeTrolls.length === 0 ? (
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <Skull className="w-12 h-12 text-gray-500 mx-auto mb-3" />
              <p className="text-gray-400">Aucun troll actif</p>
              <p className="text-sm text-gray-500">Démarrez un nouveau troll ci-contre</p>
            </div>
          ) : (
            <div className="space-y-3">
              <AnimatePresence>
                {activeTrolls.map((troll) => {
                  const Icon = TrollIcons[troll.type] || Skull;
                  return (
                    <motion.div
                      key={troll.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="bg-gray-800 rounded-lg p-4 border border-gray-700"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Icon className="w-6 h-6 text-red-500" />
                          <div>
                            <h3 className="font-semibold text-white">
                              {TrollLabels[troll.type]}
                            </h3>
                            <div className="text-sm text-gray-400 space-y-1">
                              <div>Durée: {formatDuration(troll.started_at)}</div>
                              {troll.config.target_user && (
                                <div>Cible: {troll.config.target_user}</div>
                              )}
                              {troll.config.message_content && (
                                <div>Message: "{troll.config.message_content}"</div>
                              )}
                              {troll.config.auto_stop_minutes && (
                                <div>Auto-stop: {troll.config.auto_stop_minutes}min</div>
                              )}
                            </div>
                          </div>
                        </div>

                        <button
                          onClick={() => stopTroll(troll.id)}
                          className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                          title="Arrêter ce troll"
                        >
                          <Square className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Barre de progression si auto-stop */}
                      {troll.config.auto_stop_minutes && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-400 mb-1">
                            <span>Progression</span>
                            <span>{Math.floor((Date.now() - new Date(troll.started_at).getTime()) / 60000)}/{troll.config.auto_stop_minutes}min</span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-2">
                            <div 
                              className="bg-red-500 h-2 rounded-full transition-all duration-1000"
                              style={{
                                width: `${Math.min(100, ((Date.now() - new Date(troll.started_at).getTime()) / (troll.config.auto_stop_minutes * 60000)) * 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* Nouveau Troll */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
            <Play className="w-5 h-5 text-green-500" />
            <span>Nouveau Troll</span>
          </h2>

          <div className="bg-gray-800 rounded-lg p-6 space-y-4">
            {/* Sélection type de troll */}
            <div className="space-y-2">
              <label className="text-white font-medium">Type de Troll</label>
              <select
                value={selectedTroll}
                onChange={(e) => setSelectedTroll(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="ghostping">👻 Ghost Ping</option>
                <option value="spam">💬 Spam</option>
                <option value="typing">⌨️ Fake Typing</option>
                <option value="noleave">🔒 No Leave</option>
              </select>
            </div>

            {/* Paramètres spécifiques */}
            {(selectedTroll === 'ghostping' || selectedTroll === 'noleave') && (
              <div className="space-y-2">
                <label className="text-white font-medium">
                  Utilisateur Cible {selectedTroll === 'ghostping' ? '(requis)' : '(optionnel)'}
                </label>
                <input
                  type="text"
                  value={targetUser}
                  onChange={(e) => setTargetUser(e.target.value)}
                  placeholder="ID utilisateur Discord"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                />
              </div>
            )}

            {(selectedTroll === 'ghostping' || selectedTroll === 'spam') && (
              <div className="space-y-2">
                <label className="text-white font-medium">
                  Message {selectedTroll === 'spam' ? '(requis)' : '(optionnel)'}
                </label>
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={selectedTroll === 'ghostping' ? 'Message avant mention (optionnel)' : 'Message à spam'}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                />
              </div>
            )}

            {selectedTroll === 'spam' && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-white font-medium">Nombre (max 20)</label>
                  <input
                    type="number"
                    min="1"
                    max="20"
                    value={count}
                    onChange={(e) => setCount(parseInt(e.target.value) || 5)}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-white font-medium">Délai (sec, min 3)</label>
                  <input
                    type="number"
                    min="3"
                    max="60"
                    value={delay}
                    onChange={(e) => setDelay(parseInt(e.target.value) || 3)}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  />
                </div>
              </div>
            )}

            {selectedTroll === 'typing' && (
              <div className="space-y-2">
                <label className="text-white font-medium">Durée (minutes, max 30)</label>
                <input
                  type="number"
                  min="1"
                  max="30"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value) || 5)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>
            )}

            {/* Avertissement */}
            <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3 flex items-start space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-200">
                <p className="font-medium">Utilisation responsable</p>
                <p>Les trolls sont automatiquement limités pour éviter l'abus. Utilisez avec modération.</p>
              </div>
            </div>

            {/* Bouton de démarrage */}
            <button
              onClick={startTroll}
              className="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-purple-600 hover:from-red-600 hover:to-purple-700 text-white rounded-lg font-medium flex items-center justify-center space-x-2 transition-all"
            >
              <Play className="w-5 h-5" />
              <span>Démarrer le Troll</span>
            </button>
          </div>
        </div>
      </div>

      {/* Guide rapide */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <Settings className="w-5 h-5 text-blue-500" />
          <span>Guide des Trolls</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div className="space-y-2">
            <h4 className="font-medium text-white">👻 Ghost Ping</h4>
            <p className="text-gray-400">Envoie des mentions puis supprime les messages immédiatement.</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-white">💬 Spam</h4>
            <p className="text-gray-400">Envoie plusieurs messages avec délai configurable.</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-white">⌨️ Fake Typing</h4>
            <p className="text-gray-400">Simule que vous tapez en permanence dans le canal.</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-white">🔒 No Leave</h4>
            <p className="text-gray-400">Empêche les utilisateurs de quitter les groupes.</p>
          </div>
        </div>
      </div>
    </div>
  );
}