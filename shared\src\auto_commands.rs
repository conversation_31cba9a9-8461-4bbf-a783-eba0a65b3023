use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AutoTranslateConfig {
    pub enabled: bool,
    pub source_lang: String,
    pub target_lang: String,
    pub channels: Vec<u64>, // Channel IDs où activer la traduction
    pub exclude_bots: bool,
    pub exclude_self: bool,
    pub min_length: usize, // Longueur minimum pour déclencher la traduction
    pub service: TranslationService,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TranslationService {
    Google,
    DeepL,
    Bing,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoSlashConfig {
    pub enabled: bool,
    pub triggers: HashMap<String, SlashTrigger>, // keyword -> commande
    pub global_enabled: bool, // Actif dans tous les serveurs
    pub guild_settings: HashMap<u64, GuildSlashSettings>, // Guild-specific settings
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SlashTrigger {
    pub command: String, // Commande slash à exécuter
    pub options: HashMap<String, String>, // Options de la commande
    pub delay_ms: u64, // D<PERSON><PERSON> avant exécution
    pub enabled: bool,
    pub case_sensitive: bool,
    pub exact_match: bool, // True = match exact, False = contient le mot
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GuildSlashSettings {
    pub enabled: bool,
    pub allowed_channels: Vec<u64>,
    pub allowed_roles: Vec<u64>,
    pub cooldown_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoReplyConfig {
    pub enabled: bool,
    pub triggers: HashMap<String, AutoReply>,
    pub global_cooldown_seconds: u64,
    pub per_user_cooldown_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoReply {
    pub response: String,
    pub enabled: bool,
    pub random_responses: Vec<String>, // Liste de réponses aléatoires
    pub probability: f32, // 0.0 to 1.0, probabilité de répondre
    pub delete_trigger: bool, // Supprimer le message déclencheur
    pub case_sensitive: bool,
    pub dm_only: bool, // Répondre seulement en DM
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoCommandsState {
    pub auto_translate: AutoTranslateConfig,
    pub auto_slash: AutoSlashConfig,
    pub auto_reply: AutoReplyConfig,
    pub last_translation_cache: HashMap<u64, String>, // Channel -> last translated message
    pub slash_cooldowns: HashMap<(u64, u64), u64>, // (guild_id, user_id) -> timestamp
    pub reply_cooldowns: HashMap<u64, u64>, // user_id -> timestamp
}

impl Default for AutoTranslateConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            source_lang: "auto".to_string(),
            target_lang: "fr".to_string(),
            channels: Vec::new(),
            exclude_bots: true,
            exclude_self: true,
            min_length: 10,
            service: TranslationService::Google,
        }
    }
}

impl Default for AutoSlashConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            triggers: HashMap::new(),
            global_enabled: false,
            guild_settings: HashMap::new(),
        }
    }
}

impl Default for AutoReplyConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            triggers: HashMap::new(),
            global_cooldown_seconds: 5,
            per_user_cooldown_seconds: 30,
        }
    }
}

impl Default for AutoCommandsState {
    fn default() -> Self {
        Self {
            auto_translate: AutoTranslateConfig::default(),
            auto_slash: AutoSlashConfig::default(),
            auto_reply: AutoReplyConfig::default(),
            last_translation_cache: HashMap::new(),
            slash_cooldowns: HashMap::new(),
            reply_cooldowns: HashMap::new(),
        }
    }
}

// Événements pour la communication avec le frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AutoCommandEvent {
    TranslationTriggered {
        channel_id: u64,
        original_text: String,
        translated_text: String,
        source_lang: String,
        target_lang: String,
    },
    SlashTriggered {
        guild_id: Option<u64>,
        channel_id: u64,
        trigger: String,
        command: String,
        user_id: u64,
    },
    AutoReplyTriggered {
        channel_id: u64,
        trigger: String,
        response: String,
        user_id: u64,
    },
    ConfigUpdated {
        component: String, // "translate", "slash", "reply"
    },
}