import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { invoke } from '@tauri-apps/api/core';
import { 
  Bot, 
  Plus, 
  Trash2, 
  Edit, 
  Globe, 
  Zap, 
  MessageSquare,
  RotateCcw,
  Power,
  Target
} from 'lucide-react';

/**
 * Configuration pour le système de traduction automatique des messages
 * Permet de traduire automatiquement les messages Discord en temps réel
 */
interface AutoTranslateConfig {
  /** Active/désactive la traduction automatique */
  enabled: boolean;
  /** Service de traduction utilisé ('google', 'deepl', 'bing') */
  service: string;
  /** Code de langue cible (ex: 'fr', 'en', 'es') */
  target_language: string;
  /** Liste des IDs de canaux à ignorer */
  ignore_channels: string[];
  /** Liste des IDs d'utilisateurs à ignorer */
  ignore_users: string[];
  /** Longueur minimum du message pour déclencher la traduction */
  min_length: number;
  /** Seuil de similarité pour éviter les traductions inutiles (0.0-1.0) */
  similarity_threshold: number;
}

/**
 * Définition d'un trigger pour l'exécution automatique de commandes slash
 * Permet de déclencher des commandes Discord basées sur des mots-clés
 */
interface SlashTrigger {
  /** Mot ou phrase qui déclenche la commande */
  trigger: string;
  /** Commande slash à exécuter (ex: "/ping", "/anime status") */
  command: string;
  /** Délai en millisecondes avant d'exécuter la commande */
  delay_ms: number;
  /** Temps de cooldown en secondes entre les exécutions */
  cooldown_seconds: number;
  /** Indique si ce trigger est actif */
  enabled: boolean;
  /** Liste des IDs de canaux où ce trigger est actif (vide = tous) */
  channels: string[];
  /** Si true, le trigger doit correspondre exactement au message */
  exact_match: boolean;
  /** Si true, la comparaison est sensible à la casse */
  case_sensitive: boolean;
}

/**
 * Configuration globale du système Auto-Slash
 * Gère l'exécution automatique de commandes slash basées sur des triggers
 */
interface AutoSlashConfig {
  /** Active/désactive le système Auto-Slash */
  enabled: boolean;
  /** Liste de tous les triggers configurés */
  triggers: SlashTrigger[];
  /** Cooldown global en secondes entre toutes les exécutions */
  global_cooldown_seconds: number;
}

/**
 * Définition d'une réponse automatique aux messages
 * Permet de répondre automatiquement à certains mots-clés ou phrases
 */
interface AutoReply {
  /** Mot ou phrase qui déclenche la réponse */
  trigger: string;
  /** Liste des réponses possibles (choix aléatoire) */
  responses: string[];
  /** Délai en millisecondes avant d'envoyer la réponse */
  delay_ms: number;
  /** Temps de cooldown en secondes entre les réponses */
  cooldown_seconds: number;
  /** Indique si cette réponse auto est active */
  enabled: boolean;
  /** Probabilité de répondre (0.0-1.0, 1.0 = toujours) */
  probability: number;
  /** Si true, ne répond qu'en messages privés */
  dm_only: boolean;
  /** Si true, supprime le message qui a déclenché la réponse */
  delete_trigger: boolean;
  /** Si true, envoie la réponse en mode fantôme (ephemeral) */
  ghost_mode: boolean;
}

/**
 * Configuration globale du système Auto-Reply
 * Gère les réponses automatiques aux messages Discord
 */
interface AutoReplyConfig {
  /** Active/désactive le système Auto-Reply */
  enabled: boolean;
  /** Liste de toutes les réponses automatiques configurées */
  replies: AutoReply[];
  /** Cooldown global en secondes entre toutes les réponses */
  global_cooldown_seconds: number;
}

/**
 * Composant de gestion des commandes automatiques VoidBot
 * 
 * Fonctionnalités :
 * - Auto-Translate: Traduction automatique des messages
 * - Auto-Slash: Exécution automatique de commandes slash
 * - Auto-Reply: Réponses automatiques aux messages
 * 
 * Interface à onglets avec configuration temps réel et synchronisation Tauri
 * Chaque onglet permet de configurer sa fonctionnalité respective
 * 
 * @returns Composant React de gestion des auto-commandes
 */
export function AutoCommands() {
  const [activeTab, setActiveTab] = useState<'translate' | 'slash' | 'reply'>('translate');
  const [isLoading, setIsLoading] = useState(false);

  // Auto-Translate State
  const [translateConfig, setTranslateConfig] = useState<AutoTranslateConfig>({
    enabled: false,
    service: 'google',
    target_language: 'fr',
    ignore_channels: [],
    ignore_users: [],
    min_length: 10,
    similarity_threshold: 0.8,
  });

  // Auto-Slash State
  const [slashConfig, setSlashConfig] = useState<AutoSlashConfig>({
    enabled: false,
    triggers: [],
    global_cooldown_seconds: 5,
  });

  // Auto-Reply State
  const [replyConfig, setReplyConfig] = useState<AutoReplyConfig>({
    enabled: false,
    replies: [],
    global_cooldown_seconds: 10,
  });

  useEffect(() => {
    loadConfigurations();
  }, []);

  /**
   * Charge toutes les configurations des auto-commandes depuis le backend Rust
   * Exécute les requêtes en parallèle pour optimiser les performances
   */
  const loadConfigurations = async () => {
    setIsLoading(true);
    try {
      // Charger toutes les configurations en parallèle
      const [translate, slash, reply] = await Promise.all([
        invoke<AutoTranslateConfig>('get_auto_translate_config'),
        invoke<AutoSlashConfig>('get_auto_slash_config'),
        invoke<AutoReplyConfig>('get_auto_reply_config'),
      ]);
      
      // Mettre à jour l'état local avec les configurations
      setTranslateConfig(translate);
      setSlashConfig(slash);
      setReplyConfig(reply);
    } catch (error) {
      console.error('Erreur chargement configurations auto-commandes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateTranslateConfig = async (config: AutoTranslateConfig) => {
    try {
      await invoke('update_auto_translate_config', { config });
      setTranslateConfig(config);
    } catch (error) {
      console.error('Erreur mise à jour auto-translate:', error);
    }
  };

  const updateSlashConfig = async (config: AutoSlashConfig) => {
    try {
      await invoke('update_auto_slash_config', { config });
      setSlashConfig(config);
    } catch (error) {
      console.error('Erreur mise à jour auto-slash:', error);
    }
  };

  const updateReplyConfig = async (config: AutoReplyConfig) => {
    try {
      await invoke('update_auto_reply_config', { config });
      setReplyConfig(config);
    } catch (error) {
      console.error('Erreur mise à jour auto-reply:', error);
    }
  };

  /**
   * Remet à zéro tous les cooldowns des auto-commandes
   * Permet de réactiver immédiatement tous les triggers et réponses
   */
  const clearCooldowns = async () => {
    try {
      await invoke('clear_auto_commands_cooldowns');
      alert('Cooldowns réinitialisés avec succès !');
    } catch (error) {
      console.error('Erreur reset cooldowns auto-commandes:', error);
    }
  };

  const renderTranslateTab = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Globe className="w-6 h-6 text-blue-500" />
          <div>
            <h2 className="text-xl font-semibold text-white">Auto-Traduction</h2>
            <p className="text-gray-400">Traduction automatique des messages</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => updateTranslateConfig({...translateConfig, enabled: !translateConfig.enabled})}
            className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center space-x-2 ${
              translateConfig.enabled
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
            }`}
          >
            <Power className="w-4 h-4" />
            <span>{translateConfig.enabled ? 'Activé' : 'Désactivé'}</span>
          </button>
        </div>
      </div>

      {/* Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Configuration Service</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Service de traduction</label>
              <select
                value={translateConfig.service}
                onChange={(e) => updateTranslateConfig({...translateConfig, service: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="google">Google Translate (Gratuit)</option>
                <option value="deepl">DeepL Pro</option>
                <option value="bing">Bing Translator</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Langue cible</label>
              <select
                value={translateConfig.target_language}
                onChange={(e) => updateTranslateConfig({...translateConfig, target_language: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="fr">Français</option>
                <option value="en">Anglais</option>
                <option value="es">Espagnol</option>
                <option value="de">Allemand</option>
                <option value="it">Italien</option>
                <option value="pt">Portugais</option>
              </select>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Filtres</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Longueur minimum</label>
              <input
                type="number"
                value={translateConfig.min_length}
                onChange={(e) => updateTranslateConfig({...translateConfig, min_length: parseInt(e.target.value)})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="1"
                max="1000"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Seuil de similarité</label>
              <input
                type="range"
                value={translateConfig.similarity_threshold}
                onChange={(e) => updateTranslateConfig({...translateConfig, similarity_threshold: parseFloat(e.target.value)})}
                className="w-full"
                min="0.1"
                max="1.0"
                step="0.1"
              />
              <span className="text-gray-400 text-sm">{Math.round(translateConfig.similarity_threshold * 100)}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSlashTab = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-6 h-6 text-yellow-500" />
          <div>
            <h2 className="text-xl font-semibold text-white">Auto-Slash</h2>
            <p className="text-gray-400">Déclenchement automatique de commandes slash</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => alert('Fonctionnalité en développement')}
            className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white rounded-lg font-medium transition-all flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Ajouter Trigger</span>
          </button>
          
          <button
            onClick={() => updateSlashConfig({...slashConfig, enabled: !slashConfig.enabled})}
            className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center space-x-2 ${
              slashConfig.enabled
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
            }`}
          >
            <Power className="w-4 h-4" />
            <span>{slashConfig.enabled ? 'Activé' : 'Désactivé'}</span>
          </button>
        </div>
      </div>

      {/* Configuration globale */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Configuration Globale</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Cooldown global (secondes)</label>
            <input
              type="number"
              value={slashConfig.global_cooldown_seconds}
              onChange={(e) => updateSlashConfig({...slashConfig, global_cooldown_seconds: parseInt(e.target.value)})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
              min="1"
              max="3600"
            />
          </div>
          
          <div className="flex items-end">
            <button
              onClick={clearCooldowns}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all flex items-center space-x-2"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Cooldowns</span>
            </button>
          </div>
        </div>
      </div>

      {/* Liste des triggers */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <Target className="w-5 h-5 text-yellow-500" />
          <span>Triggers Configurés ({slashConfig.triggers.length})</span>
        </h3>
        
        {slashConfig.triggers.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Aucun trigger configuré</p>
            <p className="text-sm">Cliquez sur "Ajouter Trigger" pour commencer</p>
          </div>
        ) : (
          <div className="space-y-3">
            {slashConfig.triggers.map((trigger, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded font-medium">
                        {trigger.trigger}
                      </span>
                      <span className="text-gray-300">→</span>
                      <span className="text-blue-400 font-mono">{trigger.command}</span>
                      <div className={`w-2 h-2 rounded-full ${trigger.enabled ? 'bg-green-500' : 'bg-red-500'}`} />
                    </div>
                    
                    <div className="text-sm text-gray-400 space-x-4">
                      <span>Délai: {trigger.delay_ms}ms</span>
                      <span>Cooldown: {trigger.cooldown_seconds}s</span>
                      <span>{trigger.exact_match ? 'Exact' : 'Contient'}</span>
                      <span>{trigger.case_sensitive ? 'Sensible casse' : 'Insensible casse'}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => alert('Édition en développement')}
                      className="p-2 text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => {
                        const newTriggers = slashConfig.triggers.filter((_, i) => i !== index);
                        updateSlashConfig({...slashConfig, triggers: newTriggers});
                      }}
                      className="p-2 text-red-400 hover:text-red-300 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderReplyTab = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <MessageSquare className="w-6 h-6 text-green-500" />
          <div>
            <h2 className="text-xl font-semibold text-white">Auto-Reply</h2>
            <p className="text-gray-400">Réponses automatiques aux messages</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => alert('Fonctionnalité en développement')}
            className="px-4 py-2 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Ajouter Réponse</span>
          </button>
          
          <button
            onClick={() => updateReplyConfig({...replyConfig, enabled: !replyConfig.enabled})}
            className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center space-x-2 ${
              replyConfig.enabled
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
            }`}
          >
            <Power className="w-4 h-4" />
            <span>{replyConfig.enabled ? 'Activé' : 'Désactivé'}</span>
          </button>
        </div>
      </div>

      {/* Configuration globale */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Configuration Globale</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Cooldown global (secondes)</label>
            <input
              type="number"
              value={replyConfig.global_cooldown_seconds}
              onChange={(e) => updateReplyConfig({...replyConfig, global_cooldown_seconds: parseInt(e.target.value)})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500"
              min="1"
              max="3600"
            />
          </div>
          
          <div className="flex items-end">
            <button
              onClick={clearCooldowns}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all flex items-center space-x-2"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Cooldowns</span>
            </button>
          </div>
        </div>
      </div>

      {/* Liste des réponses */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-green-500" />
          <span>Réponses Configurées ({replyConfig.replies.length})</span>
        </h3>
        
        {replyConfig.replies.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Aucune réponse configurée</p>
            <p className="text-sm">Cliquez sur "Ajouter Réponse" pour commencer</p>
          </div>
        ) : (
          <div className="space-y-3">
            {replyConfig.replies.map((reply, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="px-2 py-1 bg-green-600 text-white text-xs rounded font-medium">
                        {reply.trigger}
                      </span>
                      <span className="text-gray-300">→</span>
                      <span className="text-blue-400">{reply.responses.length} réponse(s)</span>
                      <div className={`w-2 h-2 rounded-full ${reply.enabled ? 'bg-green-500' : 'bg-red-500'}`} />
                    </div>
                    
                    <div className="text-sm text-gray-400 space-x-4">
                      <span>Délai: {reply.delay_ms}ms</span>
                      <span>Cooldown: {reply.cooldown_seconds}s</span>
                      <span>Probabilité: {Math.round(reply.probability * 100)}%</span>
                      {reply.dm_only && <span className="text-purple-400">DM only</span>}
                      {reply.ghost_mode && <span className="text-indigo-400">Mode fantôme</span>}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => alert('Édition en développement')}
                      className="p-2 text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => {
                        const newReplies = replyConfig.replies.filter((_, i) => i !== index);
                        updateReplyConfig({...replyConfig, replies: newReplies});
                      }}
                      className="p-2 text-red-400 hover:text-red-300 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Chargement des configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Bot className="w-8 h-8 text-purple-500" />
          <div>
            <h1 className="text-2xl font-bold text-white">Auto-Commands</h1>
            <p className="text-gray-400">Configuration des commandes automatiques</p>
          </div>
        </div>

        <div className="flex items-center space-x-4 text-sm">
          <div className={`flex items-center space-x-2 ${translateConfig.enabled ? 'text-green-400' : 'text-gray-400'}`}>
            <div className={`w-2 h-2 rounded-full ${translateConfig.enabled ? 'bg-green-500' : 'bg-gray-500'}`} />
            <span>Auto-Translate</span>
          </div>
          <div className={`flex items-center space-x-2 ${slashConfig.enabled ? 'text-green-400' : 'text-gray-400'}`}>
            <div className={`w-2 h-2 rounded-full ${slashConfig.enabled ? 'bg-green-500' : 'bg-gray-500'}`} />
            <span>Auto-Slash</span>
          </div>
          <div className={`flex items-center space-x-2 ${replyConfig.enabled ? 'text-green-400' : 'text-gray-400'}`}>
            <div className={`w-2 h-2 rounded-full ${replyConfig.enabled ? 'bg-green-500' : 'bg-gray-500'}`} />
            <span>Auto-Reply</span>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'translate', label: 'Auto-Translate', icon: Globe, color: 'blue' },
            { id: 'slash', label: 'Auto-Slash', icon: Zap, color: 'yellow' },
            { id: 'reply', label: 'Auto-Reply', icon: MessageSquare, color: 'green' },
          ].map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? `border-${tab.color}-500 text-${tab.color}-500`
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1">
        {activeTab === 'translate' && renderTranslateTab()}
        {activeTab === 'slash' && renderSlashTab()}
        {activeTab === 'reply' && renderReplyTab()}
      </div>
    </div>
  );
}