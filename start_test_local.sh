#!/bin/bash

# Script de test local complet pour VoidBot
# Lance le backend Discord en mode test + frontend Tauri

echo "🚀 Démarrage VoidBot en mode test local complet"
echo "=============================================="

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour nettoyer les processus en arrière-plan
cleanup() {
    echo -e "\n${YELLOW}🛑 Arrêt des processus VoidBot...${NC}"
    kill $DISCORD_PID 2>/dev/null
    kill $TAURI_PID 2>/dev/null
    echo -e "${GREEN}✅ Nettoyage terminé${NC}"
    exit 0
}

# Capturer Ctrl+C
trap cleanup SIGINT SIGTERM

echo -e "${BLUE}📋 Configuration du mode test...${NC}"

# Vérifier que les dépendances sont installées
if ! command -v cargo &> /dev/null; then
    echo -e "${RED}❌ Cargo (Rust) n'est pas installé${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm (Node.js) n'est pas installé${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Dépendances OK${NC}"

# Étape 1 : Lancer le backend Discord en mode test
echo -e "\n${BLUE}🤖 Démarrage du bot Discord en mode test...${NC}"
cd discord-bot

# S'assurer que le .env est configuré pour le test
if [ ! -f ".env" ]; then
    echo "❌ Fichier .env manquant dans discord-bot/"
    exit 1
fi

# Lancer le bot Discord en arrière-plan
cargo run &
DISCORD_PID=$!

echo -e "${GREEN}✅ Bot Discord démarré (PID: $DISCORD_PID)${NC}"
echo -e "${YELLOW}📝 Mode test: Interface accessible, Discord simulé${NC}"

# Attendre que le bot soit prêt
sleep 3

# Étape 2 : Lancer l'interface Tauri
echo -e "\n${BLUE}🖥️  Démarrage de l'interface VoidBot...${NC}"
cd ../voidbot-desktop

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installation des dépendances npm...${NC}"
    npm install
fi

# Lancer Tauri en arrière-plan
npm run tauri dev &
TAURI_PID=$!

echo -e "${GREEN}✅ Interface Tauri démarrée (PID: $TAURI_PID)${NC}"

# Étape 3 : Afficher les informations de test
echo -e "\n${GREEN}🎉 VoidBot Test Local DÉMARRÉ !${NC}"
echo "=============================================="
echo -e "${BLUE}📱 Interface:${NC} Se lance automatiquement"
echo -e "${BLUE}🤖 Backend:${NC} http://localhost:3001/status"
echo -e "${BLUE}🔧 Frontend:${NC} http://localhost:5173"
echo ""
echo -e "${YELLOW}🧪 MODE TEST ACTIF:${NC}"
echo "  • Interface VoidBot complète disponible"
echo "  • Toutes les pages navigables"
echo "  • Backend simulé (pas de Discord réel)"
echo "  • Base de données locale SQLite"
echo "  • Parfait pour tester l'UX/UI"
echo ""
echo -e "${YELLOW}⚠️  Limitations mode test:${NC}"
echo "  • Pas de vraie connexion Discord"
echo "  • Webview d'auth ne fonctionnera pas en WSL2"
echo "  • Commandes Discord simulées"
echo ""
echo -e "${GREEN}🛑 Appuyez sur Ctrl+C pour arrêter${NC}"

# Boucle d'attente avec monitoring
while true; do
    # Vérifier que les processus tournent encore
    if ! kill -0 $DISCORD_PID 2>/dev/null; then
        echo -e "${RED}❌ Bot Discord s'est arrêté${NC}"
        cleanup
    fi
    
    if ! kill -0 $TAURI_PID 2>/dev/null; then
        echo -e "${RED}❌ Interface Tauri s'est arrêtée${NC}"
        cleanup
    fi
    
    # Afficher un heartbeat toutes les 30 secondes
    sleep 30
    echo -e "${GREEN}💓 VoidBot Test Local - Actif${NC} (Ctrl+C pour arrêter)"
done