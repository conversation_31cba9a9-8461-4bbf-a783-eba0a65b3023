---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const supportChannels = [
  {
    title: "Discord Support",
    icon: "💬",
    description: "Support en temps réel 24/7",
    details: "Rejoignez notre serveur Discord pour obtenir de l'aide instantanée de notre équipe et de la communauté.",
    action: "Rejoindre Discord",
    link: "#",
    primary: true
  },
  {
    title: "GitHub Issues",
    icon: "🐛",
    description: "Signaler bugs et demandes",
    details: "Rapportez des bugs, proposez des fonctionnalités ou consultez les problèmes connus sur notre repository GitHub.",
    action: "Ouvrir Issue",
    link: "#",
    primary: false
  },
  {
    title: "Documentation",
    icon: "📚",
    description: "Guides et tutoriels complets",
    details: "Consultez notre documentation complète avec guides d'installation, tutoriels et FAQ.",
    action: "Lire la Doc",
    link: "/docs",
    primary: false
  }
];

const faq = [
  {
    question: "VoidBot peut-il me faire bannir de Discord ?",
    answer: "VoidBot respecte les limites de l'API Discord et inclut un système de rate limiting avancé pour minimiser les risques. Cependant, comme tout selfbot, l'utilisation excessive ou abusive peut entraîner des sanctions. Utilisez de manière responsable et respectez les ToS Discord."
  },
  {
    question: "Pourquoi VoidBot ne se connecte pas à Discord ?",
    answer: "Vérifiez votre connexion internet, assurez-vous que votre token Discord est valide et que vous n'avez pas activé la 2FA récemment. Consultez le guide de dépannage dans la documentation pour plus de détails."
  },
  {
    question: "Comment mettre à jour VoidBot ?",
    answer: "VoidBot inclut un auto-updater intégré qui vous notifiera des nouvelles versions. Vous pouvez aussi télécharger manuellement la dernière version depuis la page de téléchargement."
  },
  {
    question: "Mes données sont-elles sécurisées ?",
    answer: "Oui, VoidBot chiffre tous vos tokens Discord avec AES-256-GCM et toutes les données restent sur votre machine locale. Aucune information n'est envoyée à des serveurs externes."
  },
  {
    question: "VoidBot fonctionne-t-il sur Linux ?",
    answer: "Oui, VoidBot est compatible avec Windows, macOS et Linux. Des packages .deb sont disponibles pour Ubuntu/Debian, et vous pouvez compiler depuis les sources pour d'autres distributions."
  },
  {
    question: "Comment configurer le Nitro Sniper ?",
    answer: "Allez dans l'onglet Snipers de l'application, activez le Nitro Sniper et configurez vos filtres (serveurs surveillés, délais, etc.). Le mode test est activé par défaut pour éviter les récupérations accidentelles."
  },
  {
    question: "Puis-je utiliser plusieurs comptes Discord ?",
    answer: "Oui, VoidBot supporte la gestion multi-comptes. Vous pouvez ajouter plusieurs tokens Discord et basculer entre eux rapidement via l'interface."
  },
  {
    question: "Comment signaler un bug ?",
    answer: "Ouvrez une issue sur notre GitHub avec une description détaillée du problème, vos logs et étapes pour reproduire le bug. Plus vous donnez d'informations, plus vite nous pourrons corriger le problème."
  }
];

const contactInfo = {
  email: "<EMAIL>",
  discord: "#",
  github: "#",
  twitter: "https://twitter.com/VoidBotApp"
};
---

<Layout title="Support VoidBot - Aide et Assistance" description="Obtenez de l'aide pour VoidBot : support Discord 24/7, documentation complète et FAQ pour résoudre tous vos problèmes.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl sm:text-5xl font-bold mb-6">
          <span class="text-white">Support &</span>
          <br>
          <span class="gradient-text">Assistance</span>
        </h1>
        <p class="text-xl text-gray-300 mb-8">
          Notre équipe et notre communauté sont là pour vous aider à résoudre tous vos problèmes avec VoidBot.
          <br class="hidden sm:block">
          Support gratuit 24/7 via Discord.
        </p>
      </div>
    </section>

    <!-- Support Channels -->
    <section class="py-12">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">
          <span class="text-white">Canaux de</span>
          <span class="gradient-text"> Support</span>
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          {supportChannels.map((channel, index) => (
            <div class={`card-hover group text-center ${channel.primary ? 'ring-2 ring-indigo-500/20' : ''}`}>
              <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-200">
                {channel.icon}
              </div>
              <h3 class="text-xl font-bold mb-3 text-white group-hover:text-indigo-400 transition-colors duration-200">
                {channel.title}
              </h3>
              <p class="text-indigo-400 font-medium mb-3">{channel.description}</p>
              <p class="text-gray-400 text-sm mb-6">{channel.details}</p>
              <a 
                href={channel.link} 
                onclick={channel.primary ? "alert('Serveur Discord VoidBot sera bientôt disponible ! Restez connectés pour les annonces.'); return false;" : channel.title === 'GitHub Issues' ? "alert('GitHub Issues sera bientôt disponible'); return false;" : null}
                target={channel.link.startsWith('/') ? undefined : "_blank"}
                class={`btn w-full ${channel.primary ? 'btn-primary glow' : 'btn-secondary'}`}
              >
                {channel.action}
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">
          <span class="text-white">Questions</span>
          <span class="gradient-text"> Fréquentes</span>
        </h2>
        
        <div class="space-y-6">
          {faq.map((item, index) => (
            <details class="card-hover group">
              <summary class="cursor-pointer text-lg font-semibold text-white mb-3 group-hover:text-indigo-400 transition-colors duration-200 flex items-center justify-between">
                <span>{item.question}</span>
                <svg class="w-5 h-5 text-gray-400 group-hover:text-indigo-400 transition-colors duration-200 details-chevron" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div class="text-gray-400 pl-6 pr-8 pb-2 leading-relaxed">
                {item.answer}
              </div>
            </details>
          ))}
        </div>
        
        <div class="text-center mt-12">
          <p class="text-gray-400 mb-6">
            Vous ne trouvez pas la réponse à votre question ?
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="javascript:void(0)" onclick="alert('Serveur Discord VoidBot sera bientôt disponible ! Restez connectés pour les annonces.')" class="btn btn-primary glow">
              Poser une Question
            </a>
            <a href="/docs" class="btn btn-secondary">
              Consulter la Documentation
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Information -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">
          <span class="text-white">Autres</span>
          <span class="gradient-text"> Contacts</span>
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <a href={`mailto:${contactInfo.email}`} class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">📧</div>
            <h3 class="font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">Email</h3>
            <p class="text-gray-400 text-sm">{contactInfo.email}</p>
          </a>
          
          <a href="javascript:void(0)" onclick="alert('Serveur Discord VoidBot sera bientôt disponible ! Restez connectés pour les annonces.')" class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">💬</div>
            <h3 class="font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">Discord</h3>
            <p class="text-gray-400 text-sm">Support en direct</p>
          </a>
          
          <a href="javascript:void(0)" onclick="alert('Repository GitHub sera bientôt disponible')" class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">🐙</div>
            <h3 class="font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">GitHub</h3>
            <p class="text-gray-400 text-sm">Issues & bugs</p>
          </a>
          
          <a href="javascript:void(0)" onclick="alert('Twitter VoidBot sera bientôt disponible')" class="card-hover group text-center">
            <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">🐦</div>
            <h3 class="font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">Twitter</h3>
            <p class="text-gray-400 text-sm">Actualités</p>
          </a>
        </div>
      </div>
    </section>

    <!-- Status & Monitoring -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-8">
          <span class="text-white">Statut des</span>
          <span class="gradient-text"> Services</span>
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="card-hover">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold text-white">Discord API</span>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <span class="text-green-400 text-sm">Opérationnel</span>
              </div>
            </div>
            <p class="text-gray-400 text-sm">API Discord fonctionnelle</p>
          </div>
          
          <div class="card-hover">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold text-white">Site Web</span>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <span class="text-green-400 text-sm">Opérationnel</span>
              </div>
            </div>
            <p class="text-gray-400 text-sm">Site et téléchargements</p>
          </div>
          
          <div class="card-hover">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold text-white">Support Discord</span>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <span class="text-green-400 text-sm">Opérationnel</span>
              </div>
            </div>
            <p class="text-gray-400 text-sm">Équipe de support active</p>
          </div>
        </div>
        
        <p class="text-gray-400 text-sm">
          Dernière mise à jour : <span id="status-updated">maintenant</span> • 
          <a href="/status" class="text-indigo-400 hover:text-indigo-300 underline">Page de statut complète</a>
        </p>
      </div>
    </section>

    <!-- Community Guidelines -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">
          <span class="text-white">Règles de la</span>
          <span class="gradient-text"> Communauté</span>
        </h2>
        
        <div class="space-y-6">
          <div class="card">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0 w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-white mb-2">Soyez respectueux</h3>
                <p class="text-gray-400">Traitez tous les membres de la communauté avec respect et courtoisie.</p>
              </div>
            </div>
          </div>
          
          <div class="card">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-white mb-2">Recherchez avant de poser une question</h3>
                <p class="text-gray-400">Consultez la FAQ et la documentation avant de demander de l'aide.</p>
              </div>
            </div>
          </div>
          
          <div class="card">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0 w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-white mb-2">Utilisez VoidBot de manière responsable</h3>
                <p class="text-gray-400">Respectez les ToS Discord et n'utilisez pas VoidBot pour harceler ou spammer.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>

<script>
  // FAQ toggle animation
  document.addEventListener('DOMContentLoaded', () => {
    const detailElements = document.querySelectorAll('details');
    
    detailElements.forEach(details => {
      const summary = details.querySelector('summary');
      const chevron = summary?.querySelector('.details-chevron');
      
      if (summary && chevron) {
        details.addEventListener('toggle', () => {
          if (details.open) {
            chevron.style.transform = 'rotate(180deg)';
          } else {
            chevron.style.transform = 'rotate(0deg)';
          }
        });
      }
    });
    
    // Update status timestamp
    const statusElement = document.getElementById('status-updated');
    if (statusElement) {
      const now = new Date();
      statusElement.textContent = now.toLocaleTimeString('fr-FR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  });

  // Real-time status checking (optional)
  async function checkServiceStatus() {
    try {
      // Check Discord API status
      const discordResponse = await fetch('https://status.discord.com/api/v2/status.json');
      const discordStatus = await discordResponse.json();
      
      // Update status indicators based on response
      console.log('Discord status:', discordStatus.status.indicator);
      
    } catch (error) {
      console.log('Status check failed:', error);
    }
  }

  // Check status every 5 minutes
  setInterval(checkServiceStatus, 5 * 60 * 1000);
</script>

<style>
  details[open] summary {
    margin-bottom: 1rem;
  }
  
  .details-chevron {
    transition: transform 0.3s ease;
  }
</style>