use crate::error_handling::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON>ever<PERSON>};
use tokio::time::{timeout, Duration};

#[tokio::test]
async fn test_void_bot_error_creation() {
    let error = VoidBotError::discord_api_error("Test API error", Some(429));
    
    match error {
        VoidBotError::DiscordApi { message, code } => {
            assert_eq!(message, "Test API error");
            assert_eq!(code, Some(429));
        },
        _ => panic!("Type d'erreur incorrect"),
    }
}

#[tokio::test]
async fn test_cache_not_found_error() {
    let error = VoidBotError::cache_not_found("Guild", "123456789");
    
    match error {
        VoidBotError::CacheNotFound { resource, id } => {
            assert_eq!(resource, "Guild");
            assert_eq!(id, "123456789");
        },
        _ => panic!("Type d'erreur incorrect"),
    }
}

#[tokio::test]
async fn test_error_severity_classification() {
    let api_error_401 = VoidBotError::discord_api_error("Unauthorized", Some(401));
    assert_eq!(api_error_401.severity(), ErrorSeverity::High);
    
    let api_error_429 = VoidBotError::discord_api_error("Rate limited", Some(429));
    assert_eq!(api_error_429.severity(), ErrorSeverity::Medium);
    
    let api_error_500 = VoidBotError::discord_api_error("Server error", Some(500));
    assert_eq!(api_error_500.severity(), ErrorSeverity::Medium);
    
    let cache_error = VoidBotError::cache_not_found("User", "123");
    assert_eq!(cache_error.severity(), ErrorSeverity::Low);
    
    let config_error = VoidBotError::config_error("Invalid token", Some("discord_token"));
    assert_eq!(config_error.severity(), ErrorSeverity::High);
}

#[tokio::test]
async fn test_error_recoverability() {
    let api_error_500 = VoidBotError::discord_api_error("Server error", Some(500));
    assert!(api_error_500.is_recoverable());
    
    let api_error_401 = VoidBotError::discord_api_error("Unauthorized", Some(401));
    assert!(!api_error_401.is_recoverable());
    
    let cache_error = VoidBotError::cache_not_found("Guild", "123");
    assert!(cache_error.is_recoverable());
    
    let network_error = VoidBotError::Network {
        message: "Connection timeout".to_string(),
        url: Some("https://discord.com".to_string()),
    };
    assert!(network_error.is_recoverable());
}

#[tokio::test]
async fn test_retry_delay_calculation() {
    let rate_limit_error = VoidBotError::RateLimit {
        message: "Rate limited".to_string(),
        retry_after: Some(30),
    };
    assert_eq!(rate_limit_error.retry_delay(), Some(30));
    
    let api_error = VoidBotError::discord_api_error("Server error", Some(500));
    assert_eq!(api_error.retry_delay(), Some(5));
    
    let network_error = VoidBotError::Network {
        message: "Timeout".to_string(),
        url: None,
    };
    assert_eq!(network_error.retry_delay(), Some(3));
    
    let cache_error = VoidBotError::cache_not_found("User", "123");
    assert_eq!(cache_error.retry_delay(), Some(1));
}

#[tokio::test]
async fn test_error_handler_retry_success() {
    use std::sync::{Arc, Mutex};
    let attempt_count = Arc::new(Mutex::new(0));
    let attempt_count_clone = attempt_count.clone();

    let operation = move || {
        let mut count = attempt_count_clone.lock().unwrap();
        *count += 1;
        if *count < 3 {
            Err(VoidBotError::Network {
                message: "Temporary failure".to_string(),
                url: None,
            })
        } else {
            Ok("Success!")
        }
    };

    let result = ErrorHandler::handle_with_retry(operation, 5, "test operation").await;

    assert!(result.is_ok());
    assert_eq!(result.unwrap(), "Success!");
    assert_eq!(*attempt_count.lock().unwrap(), 3);
}

#[tokio::test]
async fn test_error_handler_retry_failure() {
    use std::sync::{Arc, Mutex};
    let attempt_count = Arc::new(Mutex::new(0));
    let attempt_count_clone = attempt_count.clone();

    let operation = move || {
        let mut count = attempt_count_clone.lock().unwrap();
        *count += 1;
        Err(VoidBotError::Network {
            message: "Persistent failure".to_string(),
            url: None,
        })
    };

    let result = ErrorHandler::handle_with_retry(operation, 2, "test operation").await;

    assert!(result.is_err());
    assert_eq!(*attempt_count.lock().unwrap(), 3); // 2 retries + 1 initial attempt
}

#[tokio::test]
async fn test_error_handler_non_recoverable_error() {
    use std::sync::{Arc, Mutex};
    let attempt_count = Arc::new(Mutex::new(0));
    let attempt_count_clone = attempt_count.clone();

    let operation = move || {
        let mut count = attempt_count_clone.lock().unwrap();
        *count += 1;
        Err(VoidBotError::discord_api_error("Unauthorized", Some(401)))
    };

    let result = ErrorHandler::handle_with_retry(operation, 5, "test operation").await;

    assert!(result.is_err());
    assert_eq!(*attempt_count.lock().unwrap(), 1); // Pas de retry pour erreur non récupérable
}

#[tokio::test]
async fn test_serenity_error_conversion() {
    // Test simplifié de conversion d'erreur Serenity
    let serenity_error = serenity::Error::Json(serde_json::Error::io(
        std::io::Error::new(std::io::ErrorKind::InvalidData, "test error")
    ));

    let void_error: VoidBotError = serenity_error.into();

    match void_error {
        VoidBotError::Serialization { message } => {
            assert!(message.contains("test error"));
        },
        _ => panic!("Conversion d'erreur Serenity incorrecte"),
    }
}

#[tokio::test]
async fn test_reqwest_error_conversion() {
    // Simuler une erreur reqwest (difficile à créer directement)
    let network_error = VoidBotError::Network {
        message: "Connection timeout".to_string(),
        url: Some("https://discord.com".to_string()),
    };
    
    assert!(network_error.is_recoverable());
    assert_eq!(network_error.severity(), ErrorSeverity::Medium);
}

#[tokio::test]
async fn test_serde_json_error_conversion() {
    // Tenter de parser du JSON invalide
    let invalid_json = r#"{"invalid": json syntax"#;
    let parse_result: Result<serde_json::Value, serde_json::Error> = serde_json::from_str(invalid_json);
    
    if let Err(json_error) = parse_result {
        let void_error: VoidBotError = json_error.into();
        
        match void_error {
            VoidBotError::Serialization { message } => {
                assert!(message.contains("EOF") || message.contains("expected"));
            },
            _ => panic!("Conversion d'erreur JSON incorrecte"),
        }
    }
}

#[test]
fn test_error_severity_display() {
    assert_eq!(ErrorSeverity::Low.to_string(), "LOW");
    assert_eq!(ErrorSeverity::Medium.to_string(), "MEDIUM");
    assert_eq!(ErrorSeverity::High.to_string(), "HIGH");
    assert_eq!(ErrorSeverity::Critical.to_string(), "CRITICAL");
}

#[test]
fn test_error_clone() {
    let original = VoidBotError::cache_not_found("Guild", "123");
    let cloned = original.clone();
    
    match (original, cloned) {
        (VoidBotError::CacheNotFound { resource: r1, id: i1 }, 
         VoidBotError::CacheNotFound { resource: r2, id: i2 }) => {
            assert_eq!(r1, r2);
            assert_eq!(i1, i2);
        },
        _ => panic!("Clone d'erreur incorrect"),
    }
}
