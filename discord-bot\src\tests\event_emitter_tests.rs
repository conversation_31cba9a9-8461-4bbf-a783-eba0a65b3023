use crate::event_emitter::{EventEmitter, create_test_event_emitter};
use voidbot_shared::{VoidNotification, NotificationEventType, NotificationPriority};
use serde_json::json;

#[tokio::test]
async fn test_event_emitter_creation() {
    let emitter = create_test_event_emitter();
    // Test basique de création - l'émetteur doit être créé sans erreur
    assert!(true);
}

#[tokio::test]
async fn test_notification_emission() {
    let emitter = create_test_event_emitter();

    let notification = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Test Ghostping".to_string(),
        "Un utilisateur vous a mentionné puis a supprimé son message".to_string(),
        NotificationPriority::High,
    );

    // Émettre la notification (en mode test, cela doit réussir)
    let result = emitter.emit_notification(notification.clone()).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_giveaway_event_emission() {
    let emitter = create_test_event_emitter();

    let giveaway_info = json!({
        "title": "Test Giveaway",
        "prize": "Discord Nitro",
        "end_time": "2024-12-31T23:59:59Z"
    });

    // Émettre l'événement giveaway (en mode test, cela doit réussir)
    let result = emitter.emit_giveaway_event("detected", giveaway_info.clone()).await;
    assert!(result.is_ok());
}

// Tests simplifiés pour la compilation
#[tokio::test]
async fn test_basic_functionality() {
    let emitter = create_test_event_emitter();

    // Test des différents types d'événements
    let notification = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Test".to_string(),
        "Test message".to_string(),
        NotificationPriority::Low,
    );

    let nitro_info = json!({"code": "TEST"});
    let giveaway_info = json!({"title": "Test"});

    // Tous ces appels doivent réussir en mode test
    assert!(emitter.emit_notification(notification).await.is_ok());
    assert!(emitter.emit_nitro_event("detected", nitro_info).await.is_ok());
    assert!(emitter.emit_giveaway_event("detected", giveaway_info).await.is_ok());
    assert!(emitter.emit_bot_status_update("connected", None, None).await.is_ok());
}

// Tests supplémentaires peuvent être ajoutés ici plus tard
