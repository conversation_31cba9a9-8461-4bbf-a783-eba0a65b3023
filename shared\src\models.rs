use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum StealthMode {
    Normal,
    Ghost,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnimationProfile {
    pub id: Uuid,
    pub user_id: String,
    pub name: String,
    pub bio_frames: Vec<String>,
    pub status_frames: Vec<StatusFrame>,
    pub avatar_frames: Vec<String>, // URLs ou base64
    pub interval_ms: u64,
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StatusFrame {
    pub text: String,
    pub emoji: Option<String>,
    pub status_type: StatusType,
}

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum StatusType {
    Online,
    Idle,
    Dnd,
    Invisible,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RichPresence {
    pub id: Uuid,
    pub user_id: String,
    pub name: String,
    pub application_id: String,
    pub state: Option<String>,
    pub details: Option<String>,
    pub timestamps: Option<Timestamps>,
    pub assets: Option<Assets>,
    pub buttons: Vec<Button>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Timestamps {
    pub start: Option<i64>,
    pub end: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Assets {
    pub large_image: Option<String>,
    pub large_text: Option<String>,
    pub small_image: Option<String>,
    pub small_text: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Button {
    pub label: String,
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerBackup {
    pub id: Uuid,
    pub guild_id: String,
    pub guild_name: String,
    pub channels: Vec<ChannelBackup>,
    pub roles: Vec<RoleBackup>,
    pub emojis: Vec<EmojiBackup>,
    pub settings: GuildSettings,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChannelBackup {
    pub id: String,
    pub name: String,
    pub channel_type: ChannelType,
    pub position: i32,
    pub parent_id: Option<String>,
    pub permissions: Vec<PermissionOverwrite>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ChannelType {
    Text,
    Voice,
    Category,
    News,
    Stage,
    Forum,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleBackup {
    pub id: String,
    pub name: String,
    pub color: u32,
    pub position: i32,
    pub permissions: u64,
    pub mentionable: bool,
    pub hoist: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmojiBackup {
    pub id: String,
    pub name: String,
    pub animated: bool,
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GuildSettings {
    pub verification_level: u8,
    pub default_message_notifications: u8,
    pub explicit_content_filter: u8,
    pub afk_channel_id: Option<String>,
    pub afk_timeout: u64,
    pub system_channel_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionOverwrite {
    pub id: String,
    pub overwrite_type: OverwriteType,
    pub allow: u64,
    pub deny: u64,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OverwriteType {
    Role,
    Member,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSettings {
    pub user_id: String,
    pub stealth_mode: StealthMode,
    pub auto_delete_delay: Option<u64>,
    pub hidden_prefix: String,
    pub notifications_enabled: bool,
    pub sniper_enabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}