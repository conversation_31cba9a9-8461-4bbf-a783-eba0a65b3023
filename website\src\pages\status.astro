---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const services = [
  {
    name: "Site Web VoidBot",
    status: "operational",
    description: "Site principal et pages de téléchargement",
    uptime: "99.9%",
    responseTime: "120ms"
  },
  {
    name: "Discord API",
    status: "operational", 
    description: "API Discord pour les fonctionnalités bot",
    uptime: "99.8%",
    responseTime: "45ms"
  },
  {
    name: "Téléchargements",
    status: "operational",
    description: "Serveur de fichiers et installeurs",
    uptime: "99.9%",
    responseTime: "200ms"
  },
  {
    name: "Auto-Updater",
    status: "operational",
    description: "Service de mise à jour automatique",
    uptime: "99.7%",
    responseTime: "180ms"
  }
];

const incidents = [
  {
    date: "2024-07-05",
    title: "Maintenance programmée du serveur",
    description: "Mise à jour de sécurité et optimisations de performance",
    status: "resolved",
    duration: "30 minutes"
  },
  {
    date: "2024-07-01",
    title: "Lancement de VoidBot v1.0",
    description: "Déploiement de la version stable avec toutes les fonctionnalités",
    status: "resolved",
    duration: "2 heures"
  }
];

const statusColors = {
  operational: "text-green-400 bg-green-500/20 border-green-500/30",
  degraded: "text-yellow-400 bg-yellow-500/20 border-yellow-500/30",
  outage: "text-red-400 bg-red-500/20 border-red-500/30"
};

const statusIcons = {
  operational: "✅",
  degraded: "⚠️", 
  outage: "❌"
};

const statusLabels = {
  operational: "Opérationnel",
  degraded: "Dégradé",
  outage: "Panne"
};
---

<Layout title="Statut des Services VoidBot" description="Surveillance en temps réel du statut de tous les services VoidBot : site web, API Discord, téléchargements et auto-updater.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl sm:text-5xl font-bold mb-6">
          <span class="text-white">Statut des</span>
          <br>
          <span class="gradient-text">Services</span>
        </h1>
        <p class="text-xl text-gray-300 mb-8">
          Surveillance en temps réel de tous les services VoidBot pour vous assurer la meilleure expérience possible.
        </p>
        
        <!-- Overall status -->
        <div class="inline-flex items-center gap-3 px-6 py-3 bg-green-500/10 border border-green-500/20 rounded-full mb-8">
          <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <span class="text-green-400 font-medium">Tous les systèmes opérationnels</span>
        </div>
      </div>
    </section>

    <!-- Services Status -->
    <section class="py-12">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-white mb-8">Services</h2>
        
        <div class="space-y-4">
          {services.map((service, index) => (
            <div class="card-hover">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                  <div class="text-2xl">{statusIcons[service.status]}</div>
                  <div>
                    <h3 class="font-semibold text-white">{service.name}</h3>
                    <p class="text-gray-400 text-sm">{service.description}</p>
                  </div>
                </div>
                <div class="text-right">
                  <span class={`px-3 py-1 rounded-full text-sm font-medium border ${statusColors[service.status]}`}>
                    {statusLabels[service.status]}
                  </span>
                  <div class="text-gray-400 text-xs mt-2">
                    Uptime: {service.uptime} | Réponse: {service.responseTime}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Response Times -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-white mb-8 text-center">Temps de Réponse (24h)</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {services.map((service) => (
            <div class="card text-center">
              <h3 class="font-semibold text-white mb-2">{service.name}</h3>
              <div class="text-3xl font-bold gradient-text-primary mb-1">{service.responseTime}</div>
              <p class="text-gray-400 text-sm">Temps moyen</p>
              
              <!-- Fake chart bar -->
              <div class="mt-4 h-2 bg-gray-800 rounded-full overflow-hidden">
                <div class="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full" style="width: 85%"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Recent Incidents -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-white mb-8">Incidents Récents</h2>
        
        {incidents.length > 0 ? (
          <div class="space-y-6">
            {incidents.map((incident) => (
              <div class="card">
                <div class="flex items-start gap-4">
                  <div class="flex-shrink-0 w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                    <span class="text-green-400 text-sm">✓</span>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center gap-3 mb-2">
                      <h3 class="font-semibold text-white">{incident.title}</h3>
                      <span class="px-2 py-1 bg-green-500/20 border border-green-500/30 rounded text-green-400 text-xs">
                        Résolu
                      </span>
                    </div>
                    <p class="text-gray-400 mb-2">{incident.description}</p>
                    <div class="text-gray-500 text-sm">
                      {new Date(incident.date).toLocaleDateString('fr-FR')} • Durée: {incident.duration}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div class="text-center py-12">
            <div class="text-6xl mb-4">🎉</div>
            <h3 class="text-xl font-semibold text-white mb-2">Aucun incident récent</h3>
            <p class="text-gray-400">Tous nos services fonctionnent parfaitement !</p>
          </div>
        )}
      </div>
    </section>

    <!-- Subscribe to updates -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-6">
          <span class="text-white">Restez</span>
          <span class="gradient-text"> Informés</span>
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          Soyez notifiés en cas de maintenance ou d'incident sur nos services.
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/discord" class="btn btn-primary text-lg px-8 py-4">
            Rejoindre Discord
          </a>
          <a href="/twitter" class="btn btn-secondary text-lg px-8 py-4">
            Suivre sur Twitter
          </a>
        </div>
        
        <p class="text-gray-400 text-sm mt-6">
          Page mise à jour automatiquement toutes les 5 minutes • 
          Dernière vérification : <span id="last-check">maintenant</span>
        </p>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>

<script>
  // Update last check time
  document.addEventListener('DOMContentLoaded', () => {
    const lastCheckElement = document.getElementById('last-check');
    if (lastCheckElement) {
      const updateTime = () => {
        const now = new Date();
        lastCheckElement.textContent = now.toLocaleTimeString('fr-FR', { 
          hour: '2-digit', 
          minute: '2-digit',
          second: '2-digit'
        });
      };
      
      updateTime();
      setInterval(updateTime, 30000); // Update every 30 seconds
    }
  });
</script>