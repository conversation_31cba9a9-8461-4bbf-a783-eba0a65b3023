import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { 
  Power, 
  Monitor, 
  Settings, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  Cpu,
  HardDrive,
  Clock,
  Activity
} from 'lucide-react';

interface DiscordProcess {
  pid: number;
  name: string;
  exe_path?: string;
  memory_usage: number;
  cpu_usage: number;
  start_time: number;
}

interface OverlayConfig {
  enabled: boolean;
  auto_show: boolean;
  transparency: number;
  theme: string;
}

interface ContextMenuData {
  mouse_position: { x: number; y: number };
  discord_window_active: boolean;
  timestamp: number;
}

export function SystemManager() {
  const [autoStartupEnabled, setAutoStartupEnabled] = useState(false);
  const [discordProcesses, setDiscordProcesses] = useState<DiscordProcess[]>([]);
  const [isDiscordRunning, setIsDiscordRunning] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [monitoringActive, setMonitoringActive] = useState(false);
  const [overlayConfig, setOverlayConfig] = useState<OverlayConfig>({
    enabled: true,
    auto_show: true,
    transparency: 0.95,
    theme: 'dark'
  });
  const [overlayActive, setOverlayActive] = useState(false);
  const [mouseListenerActive, setMouseListenerActive] = useState(false);

  useEffect(() => {
    loadAutoStartupStatus();
    scanDiscordProcesses();
    startMonitoring();
    loadOverlayConfig();
    checkOverlayStatus();

    // Écouter les événements Discord
    const setupEventListeners = async () => {
      await listen('discord-detected', (event) => {
        setDiscordProcesses(event.payload as DiscordProcess[]);
        setIsDiscordRunning(true);
        
        // Auto-démarrer l'overlay si configuré
        if (overlayConfig.auto_show && overlayConfig.enabled) {
          startOverlay();
        }
      });

      await listen('discord-lost', () => {
        setDiscordProcesses([]);
        setIsDiscordRunning(false);
        
        // Arrêter l'overlay
        stopOverlay();
      });

      await listen('context-menu-requested', (event) => {
        const data = event.payload as ContextMenuData;
        showContextMenu(data.mouse_position.x, data.mouse_position.y);
      });
    };

    setupEventListeners();
  }, [overlayConfig]);

  const loadAutoStartupStatus = async () => {
    try {
      const status = await invoke<boolean>('get_auto_startup_status');
      setAutoStartupEnabled(status);
    } catch (error) {
      console.error('Erreur chargement auto-startup:', error);
    }
  };

  const toggleAutoStartup = async () => {
    try {
      if (autoStartupEnabled) {
        await invoke('disable_auto_startup');
        setAutoStartupEnabled(false);
      } else {
        await invoke('enable_auto_startup');
        setAutoStartupEnabled(true);
      }
    } catch (error) {
      console.error('Erreur toggle auto-startup:', error);
      alert(`Erreur: ${error}`);
    }
  };

  const scanDiscordProcesses = async () => {
    setIsScanning(true);
    try {
      const processes = await invoke<DiscordProcess[]>('scan_discord_processes');
      setDiscordProcesses(processes);
      setIsDiscordRunning(processes.length > 0);
    } catch (error) {
      console.error('Erreur scan Discord:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const startMonitoring = async () => {
    try {
      await invoke('start_discord_monitoring');
      setMonitoringActive(true);
    } catch (error) {
      console.error('Erreur démarrage monitoring:', error);
    }
  };

  const loadOverlayConfig = async () => {
    try {
      const config = await invoke<OverlayConfig>('get_overlay_config');
      setOverlayConfig(config);
    } catch (error) {
      console.error('Erreur chargement config overlay:', error);
    }
  };

  const checkOverlayStatus = async () => {
    try {
      const isActive = await invoke<boolean>('is_overlay_active');
      setOverlayActive(isActive);
      if (isActive) {
        setMouseListenerActive(true);
      }
    } catch (error) {
      console.error('Erreur vérification état overlay:', error);
      setOverlayActive(false);
      setMouseListenerActive(false);
    }
  };

  const startOverlay = async () => {
    try {
      // Vérifier l'état actuel de l'overlay
      const isActive = await invoke<boolean>('is_overlay_active');
      if (isActive) {
        console.log('Overlay déjà actif, destruction avant recréation');
        await invoke('destroy_overlay_window');
        // Petit délai pour s'assurer que la fenêtre est fermée
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      await invoke('create_overlay_window');
      await invoke('start_mouse_listener');
      setOverlayActive(true);
      setMouseListenerActive(true);
    } catch (error) {
      console.error('Erreur démarrage overlay:', error);
      setOverlayActive(false);
      setMouseListenerActive(false);
      // Afficher l'erreur de manière moins intrusive
      const errorMessage = typeof error === 'string' ? error : 'Erreur inconnue';
      console.error(`Erreur overlay: ${errorMessage}`);
    }
  };

  const stopOverlay = async () => {
    try {
      await invoke('destroy_overlay_window');
      await invoke('stop_mouse_listener');
      setOverlayActive(false);
      setMouseListenerActive(false);
    } catch (error) {
      console.error('Erreur arrêt overlay:', error);
    }
  };

  const toggleOverlay = async () => {
    if (overlayActive) {
      await stopOverlay();
    } else {
      await startOverlay();
    }
  };

  const showContextMenu = async (x: number, y: number) => {
    try {
      await invoke('show_context_menu', { x, y });
    } catch (error) {
      console.error('Erreur affichage menu:', error);
    }
  };

  const updateOverlayConfig = async (newConfig: Partial<OverlayConfig>) => {
    const updatedConfig = { ...overlayConfig, ...newConfig };
    try {
      await invoke('update_overlay_config', { config: updatedConfig });
      setOverlayConfig(updatedConfig);
    } catch (error) {
      console.error('Erreur mise à jour config:', error);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (startTime: number) => {
    const now = Date.now() / 1000;
    const uptime = now - startTime;
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Settings className="w-8 h-8 text-blue-500" />
          <div>
            <h1 className="text-2xl font-bold text-white">Gestionnaire Système</h1>
            <p className="text-gray-400">Configuration du système et monitoring Discord</p>
          </div>
        </div>

        {monitoringActive && (
          <div className="flex items-center space-x-2 text-green-400">
            <Activity className="w-5 h-5 animate-pulse" />
            <span className="text-sm">Monitoring actif</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Auto-Startup */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Power className="w-6 h-6 text-purple-500" />
              <h2 className="text-xl font-semibold text-white">Démarrage Automatique</h2>
            </div>
            
            <div className={`flex items-center space-x-2 ${autoStartupEnabled ? 'text-green-400' : 'text-gray-400'}`}>
              {autoStartupEnabled ? <CheckCircle className="w-5 h-5" /> : <XCircle className="w-5 h-5" />}
              <span className="text-sm font-medium">
                {autoStartupEnabled ? 'Activé' : 'Désactivé'}
              </span>
            </div>
          </div>

          <p className="text-gray-300 mb-6">
            Démarre automatiquement VoidBot avec le système pour un accès permanent aux fonctionnalités.
          </p>

          <button
            onClick={toggleAutoStartup}
            className={`w-full px-4 py-3 rounded-lg font-medium transition-all flex items-center justify-center space-x-2 ${
              autoStartupEnabled
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white'
            }`}
          >
            <Power className="w-5 h-5" />
            <span>{autoStartupEnabled ? 'Désactiver' : 'Activer'} le démarrage auto</span>
          </button>
        </motion.div>

        {/* État Discord */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Monitor className="w-6 h-6 text-indigo-500" />
              <h2 className="text-xl font-semibold text-white">État Discord</h2>
            </div>
            
            <button
              onClick={scanDiscordProcesses}
              disabled={isScanning}
              className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
              title="Rafraîchir"
            >
              <RefreshCw className={`w-4 h-4 ${isScanning ? 'animate-spin' : ''}`} />
            </button>
          </div>

          <div className={`flex items-center space-x-3 mb-4 ${isDiscordRunning ? 'text-green-400' : 'text-red-400'}`}>
            <div className={`w-3 h-3 rounded-full ${isDiscordRunning ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
            <span className="font-medium">
              {isDiscordRunning ? 'Discord détecté' : 'Discord non détecté'}
            </span>
          </div>

          <p className="text-gray-300 text-sm">
            {isDiscordRunning 
              ? `${discordProcesses.length} processus Discord actif${discordProcesses.length > 1 ? 's' : ''}`
              : 'Aucun processus Discord trouvé sur le système'
            }
          </p>
        </motion.div>

        {/* Configuration Overlay */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Monitor className="w-6 h-6 text-green-500" />
              <h2 className="text-xl font-semibold text-white">Menu Contextuel</h2>
            </div>
            
            <div className={`flex items-center space-x-2 ${overlayActive ? 'text-green-400' : 'text-gray-400'}`}>
              {overlayActive ? <CheckCircle className="w-5 h-5" /> : <XCircle className="w-5 h-5" />}
              <span className="text-sm font-medium">
                {overlayActive ? 'Actif' : 'Inactif'}
              </span>
            </div>
          </div>

          <p className="text-gray-300 mb-4">
            Overlay transparent qui détecte les clics droits dans Discord pour afficher le menu VoidBot.
          </p>

          <div className="space-y-4">
            {/* Contrôles principaux */}
            <div className="flex space-x-3">
              <button
                onClick={toggleOverlay}
                className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all flex items-center justify-center space-x-2 ${
                  overlayActive
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white'
                }`}
              >
                <Monitor className="w-5 h-5" />
                <span>{overlayActive ? 'Arrêter' : 'Démarrer'} l'overlay</span>
              </button>
              
              <button
                onClick={loadOverlayConfig}
                className="px-4 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                title="Recharger la configuration"
              >
                <RefreshCw className="w-5 h-5" />
              </button>
            </div>

            {/* Configuration */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Auto-démarrage</label>
                <button
                  onClick={() => updateOverlayConfig({ auto_show: !overlayConfig.auto_show })}
                  className={`w-full px-3 py-2 rounded-lg text-sm transition-colors ${
                    overlayConfig.auto_show
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
                  }`}
                >
                  {overlayConfig.auto_show ? 'Activé' : 'Désactivé'}
                </button>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Overlay activé</label>
                <button
                  onClick={() => updateOverlayConfig({ enabled: !overlayConfig.enabled })}
                  className={`w-full px-3 py-2 rounded-lg text-sm transition-colors ${
                    overlayConfig.enabled
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
                  }`}
                >
                  {overlayConfig.enabled ? 'Activé' : 'Désactivé'}
                </button>
              </div>
            </div>

            {/* Status indicators */}
            <div className="flex items-center justify-between text-sm">
              <div className={`flex items-center space-x-2 ${mouseListenerActive ? 'text-green-400' : 'text-gray-400'}`}>
                <div className={`w-2 h-2 rounded-full ${mouseListenerActive ? 'bg-green-500' : 'bg-gray-500'}`} />
                <span>Détection souris: {mouseListenerActive ? 'Active' : 'Inactive'}</span>
              </div>
              
              <div className="text-gray-400">
                Transparence: {Math.round(overlayConfig.transparency * 100)}%
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Liste des processus Discord */}
      {discordProcesses.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Activity className="w-5 h-5 text-green-500" />
            <span>Processus Discord Détectés</span>
          </h3>

          <div className="space-y-3">
            {discordProcesses.map((process) => (
              <motion.div
                key={process.pid}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <Monitor className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white">{process.name}</h4>
                      <p className="text-sm text-gray-400">PID: {process.pid}</p>
                    </div>
                  </div>

                  <div className="text-right space-y-1">
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center space-x-1 text-blue-400">
                        <Cpu className="w-4 h-4" />
                        <span>{process.cpu_usage.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center space-x-1 text-green-400">
                        <HardDrive className="w-4 h-4" />
                        <span>{formatBytes(process.memory_usage)}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-purple-400">
                        <Clock className="w-4 h-4" />
                        <span>{formatUptime(process.start_time)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {process.exe_path && (
                  <div className="mt-2 pt-2 border-t border-gray-600">
                    <p className="text-xs text-gray-400 truncate" title={process.exe_path}>
                      {process.exe_path}
                    </p>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Informations sur le monitoring */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-xl p-6 border border-blue-500/30"
      >
        <h3 className="text-lg font-semibold text-white mb-3 flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-500" />
          <span>Monitoring en Temps Réel</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="space-y-2">
            <h4 className="font-medium text-blue-400">🔍 Détection Automatique</h4>
            <p className="text-gray-300">Scan automatique des processus Discord toutes les 5 secondes</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-green-400">📡 Événements Temps Réel</h4>
            <p className="text-gray-300">Notifications instantanées à l'ouverture/fermeture de Discord</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-purple-400">⚡ Overlay Automatique</h4>
            <p className="text-gray-300">Le menu contextuel s'active automatiquement avec Discord</p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}