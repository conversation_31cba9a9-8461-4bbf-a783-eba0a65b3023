import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Download, 
  RefreshCw, 
  Settings, 
  AlertCircle, 
  CheckCircle2, 
  X, 
  Clock,
  Zap,
  Loader2,
  ExternalLink,
  Smartphone
} from 'lucide-react';
import { useUpdateStore } from '../stores/updateStore';

interface UpdateManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const UpdateManager: React.FC<UpdateManagerProps> = ({ isOpen, onClose }) => {
  const {
    config,
    status,
    currentVersion,
    isInitialized,
    initialize,
    checkForUpdates,
    downloadAndInstall,
    cancelUpdate,
    restartApplication,
    updateConfig,
    formatFileSize,
    isUpdateRequired,
    getTimeSinceLastCheck,
  } = useUpdateStore();

  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [isInitialized, initialize]);

  const handleConfigChange = (key: keyof typeof config, value: any) => {
    updateConfig({ [key]: value });
  };

  const getStatusIcon = () => {
    if (status.checking) return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
    if (status.downloading || status.installing) return <Download className="w-5 h-5 text-blue-500" />;
    if (status.error) return <AlertCircle className="w-5 h-5 text-red-500" />;
    if (status.available) return <Zap className="w-5 h-5 text-green-500" />;
    return <CheckCircle2 className="w-5 h-5 text-gray-500" />;
  };

  const getStatusText = () => {
    if (status.checking) return 'Vérification en cours...';
    if (status.downloading) return `Téléchargement... ${Math.round(status.progress)}%`;
    if (status.installing) return 'Installation en cours...';
    if (status.error) return status.error;
    if (status.available && status.update_info) {
      return `Mise à jour v${status.update_info.version} disponible`;
    }
    return 'Aucune mise à jour disponible';
  };

  const getReleaseNotes = () => {
    if (!status.update_info?.notes) return '';
    
    // Convertir markdown basique en HTML
    return status.update_info.notes
      .replace(/^## (.*$)/gim, '<h3 class="text-lg font-semibold text-white mb-2 mt-4">$1</h3>')
      .replace(/^### (.*$)/gim, '<h4 class="text-md font-medium text-gray-300 mb-2 mt-3">$1</h4>')
      .replace(/^\- (.*$)/gim, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="text-white">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="text-gray-300">$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-800 px-1 rounded text-cyan-400">$1</code>');
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-gray-900 rounded-xl border border-gray-700 w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-500/20 rounded-lg">
                <Smartphone className="w-6 h-6 text-indigo-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Gestionnaire de Mises à Jour</h2>
                <p className="text-gray-400 text-sm">Version actuelle: v{currentVersion}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
            {/* Status Section */}
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-4">
                {getStatusIcon()}
                <span className="text-white font-medium">{getStatusText()}</span>
              </div>

              {/* Progress Bar */}
              {(status.downloading || status.installing) && (
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-400 mb-2">
                    <span>Progression</span>
                    <span>{Math.round(status.progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${status.progress}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </div>
              )}

              {/* Last Check */}
              {config.last_check && (
                <div className="flex items-center gap-2 text-sm text-gray-400 mb-4">
                  <Clock className="w-4 h-4" />
                  <span>Dernière vérification: {getTimeSinceLastCheck()}</span>
                </div>
              )}
            </div>

            {/* Update Available */}
            {status.available && status.update_info && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-6"
              >
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-lg font-semibold text-green-400 mb-1">
                      VoidBot v{status.update_info.version}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      Sortie le {new Date(status.update_info.release_date).toLocaleDateString('fr-FR')}
                    </p>
                    {status.update_info.size > 0 && (
                      <p className="text-gray-500 text-sm">
                        Taille: {formatFileSize(status.update_info.size)}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    {!status.downloading && !status.installing && (
                      <button
                        onClick={downloadAndInstall}
                        className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                      >
                        <Download className="w-4 h-4 inline mr-2" />
                        Installer
                      </button>
                    )}
                    {(status.downloading || status.installing) && (
                      <button
                        onClick={cancelUpdate}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
                      >
                        Annuler
                      </button>
                    )}
                  </div>
                </div>

                {/* Release Notes */}
                {status.update_info.notes && (
                  <details className="group">
                    <summary className="cursor-pointer text-green-400 font-medium mb-2 group-open:mb-3">
                      Notes de version
                    </summary>
                    <div
                      className="text-gray-300 text-sm prose prose-invert max-w-none"
                      dangerouslySetInnerHTML={{ __html: getReleaseNotes() }}
                    />
                  </details>
                )}
              </motion.div>
            )}

            {/* Actions */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              <button
                onClick={checkForUpdates}
                disabled={status.checking}
                className="flex items-center justify-center gap-2 p-3 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
              >
                <RefreshCw className={`w-4 h-4 ${status.checking ? 'animate-spin' : ''}`} />
                <span className="text-white font-medium">Vérifier</span>
              </button>

              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center justify-center gap-2 p-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span className="text-white font-medium">Configuration</span>
              </button>
            </div>

            {/* Advanced Settings */}
            <AnimatePresence>
              {showAdvanced && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-gray-800/50 rounded-lg p-4 space-y-4"
                >
                  <h3 className="text-lg font-semibold text-white mb-3">Configuration Avancée</h3>

                  {/* Auto Check */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-white font-medium">Vérification automatique</label>
                      <p className="text-gray-400 text-sm">Vérifier automatiquement les mises à jour</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={config.auto_check}
                        onChange={(e) => handleConfigChange('auto_check', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  {/* Auto Download */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-white font-medium">Téléchargement automatique</label>
                      <p className="text-gray-400 text-sm">Télécharger automatiquement les mises à jour</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={config.auto_download}
                        onChange={(e) => handleConfigChange('auto_download', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  {/* Auto Install */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-white font-medium">Installation automatique</label>
                      <p className="text-gray-400 text-sm">Installer automatiquement les mises à jour</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={config.auto_install}
                        onChange={(e) => handleConfigChange('auto_install', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  {/* Check Interval */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-white font-medium">Intervalle de vérification</label>
                      <p className="text-gray-400 text-sm">Fréquence des vérifications automatiques</p>
                    </div>
                    <select
                      value={config.check_interval_hours}
                      onChange={(e) => handleConfigChange('check_interval_hours', parseInt(e.target.value))}
                      className="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 text-sm"
                    >
                      <option value={1}>Chaque heure</option>
                      <option value={6}>Toutes les 6 heures</option>
                      <option value={12}>Toutes les 12 heures</option>
                      <option value={24}>Quotidienne</option>
                      <option value={168}>Hebdomadaire</option>
                    </select>
                  </div>

                  {/* Beta Updates */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-white font-medium">Versions bêta</label>
                      <p className="text-gray-400 text-sm">Inclure les versions de développement</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={config.beta_updates}
                        onChange={(e) => handleConfigChange('beta_updates', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-gray-700">
              <div className="flex items-center justify-between text-sm text-gray-400">
                <span>Système de mise à jour Tauri</span>
                <a
                  href="https://github.com/VoidBot/VoidBot/releases"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 hover:text-indigo-400 transition-colors"
                >
                  <span>Voir toutes les versions</span>
                  <ExternalLink className="w-3 h-3" />
                </a>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};