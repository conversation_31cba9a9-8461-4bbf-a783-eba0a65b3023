@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* VoidBot Color Palette */
  --voidbot-primary: #6366f1;     /* Indigo */
  --voidbot-secondary: #8b5cf6;   /* Purple */
  --voidbot-accent: #22d3ee;      /* Cyan */
  --voidbot-dark: #0a0a0a;        /* Near Black */
  --voidbot-darker: #1a1a1a;      /* Darker Gray */
  --voidbot-success: #10b981;     /* Emerald */
  --voidbot-warning: #f59e0b;     /* Amber */
  --voidbot-error: #ef4444;       /* Red */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.8);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Button animations */
.btn {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900;
}

.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl focus:ring-indigo-500;
}

.btn-secondary {
  @apply bg-gray-800 hover:bg-gray-700 text-white border border-gray-700 hover:border-gray-600 focus:ring-gray-500;
}

.btn-accent {
  @apply bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white shadow-lg hover:shadow-xl focus:ring-cyan-500;
}

/* Card styles */
.card {
  @apply bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 shadow-xl;
}

.card-hover {
  @apply card hover:bg-gray-900/80 hover:border-gray-700 transition-all duration-300 hover:shadow-2xl;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-indigo-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent;
}

.gradient-text-primary {
  @apply bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent;
}

/* Glow effects */
.glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.glow-accent {
  box-shadow: 0 0 20px rgba(34, 211, 238, 0.3);
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

.bounce-in {
  animation: bounceIn 1s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes bounceIn {
  0% { 
    opacity: 0; 
    transform: scale(0.3); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.05); 
  }
  70% { 
    transform: scale(0.9); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Code blocks */
.code-block {
  @apply bg-gray-950 border border-gray-800 rounded-lg p-4 font-mono text-sm overflow-x-auto;
}

/* Navigation */
.nav-link {
  @apply text-gray-300 hover:text-white transition-colors duration-200 relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #6366f1, #8b5cf6);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Feature grid */
.feature-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .btn {
    @apply px-4 py-2 text-sm;
  }
}