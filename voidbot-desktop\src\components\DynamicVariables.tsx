import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

interface DynamicVariableConfig {
  enabled: boolean;
  cache_duration_seconds: number;
  max_custom_variables: number;
  auto_refresh_interval_seconds: number;
  enabled_providers: string[];
}

interface CachedVariable {
  name: string;
  value: string;
  cached_at: string;
  expires_at: string;
  provider: string;
}

interface VariableProvider {
  name: string;
  variables: string[];
  description: string;
  icon: string;
}

const VariableChip = ({ name, value, provider, onClick }: {
  name: string;
  value: string;
  provider: string;
  onClick: () => void;
}) => {
  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'time': return 'from-blue-500/20 to-cyan-500/20 border-blue-500/30';
      case 'discord': return 'from-indigo-500/20 to-purple-500/20 border-indigo-500/30';
      case 'system': return 'from-green-500/20 to-emerald-500/20 border-green-500/30';
      case 'spotify': return 'from-pink-500/20 to-rose-500/20 border-pink-500/30';
      case 'custom': return 'from-orange-500/20 to-yellow-500/20 border-orange-500/30';
      default: return 'from-gray-500/20 to-gray-600/20 border-gray-500/30';
    }
  };

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-white bg-gradient-to-r ${getProviderColor(provider)} border backdrop-blur-sm transition-all duration-200 hover:shadow-lg`}
      title={`${name}: ${value} (${provider})`}
    >
      <span className="font-mono text-xs">{`{${name}}`}</span>
      <span className="text-xs opacity-80">→</span>
      <span className="max-w-20 truncate">{value}</span>
    </motion.button>
  );
};

const TemplateCard = ({ title, description, template, category, onUse }: {
  title: string;
  description: string;
  template: string;
  category: string;
  onUse: (template: string) => void;
}) => {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Rich Presence': return 'from-purple-500/20 to-pink-500/20 border-purple-500/30';
      case 'Bio/Status': return 'from-blue-500/20 to-cyan-500/20 border-blue-500/30';
      case 'Auto-Reply': return 'from-green-500/20 to-emerald-500/20 border-green-500/30';
      case 'Animation': return 'from-orange-500/20 to-red-500/20 border-orange-500/30';
      default: return 'from-gray-500/20 to-gray-600/20 border-gray-500/30';
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      className={`bg-gradient-to-br ${getCategoryColor(category)} backdrop-blur-sm rounded-xl p-4 border cursor-pointer`}
      onClick={() => onUse(template)}
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="text-white font-semibold">{title}</h4>
          <p className="text-gray-300 text-sm mt-1">{description}</p>
        </div>
        <span className="text-xs bg-white/20 text-white px-2 py-1 rounded-full">
          {category}
        </span>
      </div>
      <div className="bg-gray-800/50 rounded-lg p-3 font-mono text-sm text-gray-300">
        {template}
      </div>
      <div className="mt-3 text-center">
        <span className="text-indigo-400 text-sm font-medium">Cliquer pour utiliser →</span>
      </div>
    </motion.div>
  );
};

const VariableEditor = ({ value, onChange, placeholder }: {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
}) => {
  const [, setCursorPosition] = useState(0); // Future: cursor tracking

  // Future: cursor-based variable insertion
  // const insertVariable = useCallback((variable: string) => {
  //   const beforeCursor = value.substring(0, cursorPosition);
  //   const afterCursor = value.substring(cursorPosition);
  //   const newValue = beforeCursor + `{${variable}}` + afterCursor;
  //   onChange(newValue);
  //   setCursorPosition(beforeCursor.length + variable.length + 2);
  // }, [value, cursorPosition, onChange]);

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    setCursorPosition(e.target.selectionStart);
  };

  const handleTextareaSelect = (e: React.SyntheticEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    setCursorPosition(target.selectionStart);
  };

  return (
    <div className="space-y-3">
      <textarea
        value={value}
        onChange={handleTextareaChange}
        onSelect={handleTextareaSelect}
        placeholder={placeholder}
        className="w-full h-32 px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm resize-none"
      />
      <div className="text-xs text-gray-400">
        💡 Tapez <code className="bg-gray-700 px-1 rounded">{`{nom_variable}`}</code> ou cliquez sur les variables ci-dessous
      </div>
    </div>
  );
};

const PreviewBox = ({ template, resolvedText }: {
  template: string;
  resolvedText: string;
}) => {
  return (
    <div className="space-y-3">
      <div className="bg-gray-800/80 rounded-lg p-4 border border-gray-600/50">
        <h4 className="text-white font-medium mb-2 flex items-center">
          📝 Template (avec variables)
        </h4>
        <div className="font-mono text-sm text-gray-300 bg-gray-900/50 p-3 rounded">
          {template || 'Tapez votre template...'}
        </div>
      </div>
      
      <div className="bg-gradient-to-r from-indigo-900/50 to-purple-900/50 rounded-lg p-4 border border-indigo-500/30">
        <h4 className="text-white font-medium mb-2 flex items-center">
          ✨ Résultat (temps réel)
        </h4>
        <div className="font-mono text-sm text-white bg-gray-900/50 p-3 rounded">
          {resolvedText || 'Le résultat apparaîtra ici...'}
        </div>
      </div>
    </div>
  );
};

export default function DynamicVariables() {
  const [config, setConfig] = useState<DynamicVariableConfig>({
    enabled: true,
    cache_duration_seconds: 10,
    max_custom_variables: 50,
    auto_refresh_interval_seconds: 5,
    enabled_providers: ['time', 'discord', 'system', 'spotify'],
  });

  const [cachedVariables, setCachedVariables] = useState<CachedVariable[]>([]);
  const [customVariables, setCustomVariables] = useState<Record<string, string>>({});
  // const [availableVariables, setAvailableVariables] = useState<string[]>([]); // Future: available variables list

  const [activeTab, setActiveTab] = useState<'editor' | 'templates' | 'variables' | 'config'>('editor');
  
  // Editor state
  const [currentTemplate, setCurrentTemplate] = useState('');
  const [resolvedText, setResolvedText] = useState('');
  const [isResolving, setIsResolving] = useState(false);

  // Custom variable state
  const [newVariableName, setNewVariableName] = useState('');
  const [newVariableValue, setNewVariableValue] = useState('');
  const [showAddVariableModal, setShowAddVariableModal] = useState(false);

  const providers: VariableProvider[] = [
    {
      name: 'time',
      variables: ['local_time', 'utc_time', 'date', 'timestamp'],
      description: 'Variables temporelles en temps réel',
      icon: '🕐'
    },
    {
      name: 'discord',
      variables: ['username', 'server_count', 'friend_count', 'user_id'],
      description: 'Informations Discord du compte',
      icon: '💬'
    },
    {
      name: 'system',
      variables: ['os', 'hostname', 'active_app', 'uptime'],
      description: 'Informations système local',
      icon: '🖥️'
    },
    {
      name: 'spotify',
      variables: ['spotify_track', 'spotify_artist', 'spotify_album', 'spotify_lyrics'],
      description: 'Musique Spotify en cours',
      icon: '🎵'
    }
  ];

  const templates = [
    {
      title: 'Rich Presence Gaming',
      description: 'Presence pour les jeux avec temps',
      template: 'Playing {game_name}\nLevel {level} - {local_time}',
      category: 'Rich Presence'
    },
    {
      title: 'Rich Presence Musique',
      description: 'Affichage de la musique actuelle',
      template: '🎵 Listening to {spotify_track}\nby {spotify_artist} - {local_time}',
      category: 'Rich Presence'
    },
    {
      title: 'Bio Discord Complète',
      description: 'Bio avec stats et musique',
      template: '🎵 {spotify_track} by {spotify_artist}\n📊 {server_count} servers | {friend_count} friends\n🕐 {local_time}',
      category: 'Bio/Status'
    },
    {
      title: 'Status Simple',
      description: 'Status avec heure locale',
      template: 'Online at {local_time} 🌟',
      category: 'Bio/Status'
    },
    {
      title: 'Auto-Reply Personnalisé',
      description: 'Réponse automatique avec infos',
      template: 'Hey! Je suis actuellement en train d\'écouter {spotify_track}. Il est {local_time} chez moi 🎵',
      category: 'Auto-Reply'
    },
    {
      title: 'Auto-Reply Gaming',
      description: 'Réponse quand en jeu',
      template: 'Salut ! Je suis en jeu actuellement. Message reçu à {local_time} 🎮',
      category: 'Auto-Reply'
    },
    {
      title: 'Animation Bio Musique',
      description: 'Animation avec musique qui change',
      template: '♪ {spotify_track} ♪\n🕐 {local_time}\n📊 {server_count} serveurs',
      category: 'Animation'
    },
    {
      title: 'Animation Temps',
      description: 'Animation avec heure en temps réel',
      template: '🌟 VoidBot User 🌟\n⏰ {local_time}\n🖥️ {os} | {hostname}',
      category: 'Animation'
    }
  ];

  useEffect(() => {
    loadConfig();
    loadVariables();
    loadCustomVariables();
    loadAvailableVariables();

    // Auto-refresh des variables
    const interval = setInterval(() => {
      if (config.enabled) {
        loadVariables();
        if (currentTemplate) {
          resolveTemplate(currentTemplate);
        }
      }
    }, config.auto_refresh_interval_seconds * 1000);

    return () => clearInterval(interval);
  }, [config.enabled, config.auto_refresh_interval_seconds]);

  // Résoudre le template quand il change
  useEffect(() => {
    if (currentTemplate) {
      const debounceTimer = setTimeout(() => {
        resolveTemplate(currentTemplate);
      }, 300);
      return () => clearTimeout(debounceTimer);
    } else {
      setResolvedText('');
    }
  }, [currentTemplate]);

  const loadConfig = async () => {
    try {
      const dynamicConfig = await invoke<DynamicVariableConfig>('get_dynamic_variable_config');
      setConfig(dynamicConfig);
    } catch (error) {
      console.error('Erreur lors du chargement de la config Dynamic Variables:', error);
    }
  };

  const loadVariables = async () => {
    try {
      const variables = await invoke<CachedVariable[]>('get_cached_variables');
      setCachedVariables(variables);
    } catch (error) {
      console.error('Erreur lors du chargement des variables:', error);
    }
  };

  const loadCustomVariables = async () => {
    try {
      const customVars = await invoke<Record<string, string>>('get_custom_variables');
      setCustomVariables(customVars);
    } catch (error) {
      console.error('Erreur lors du chargement des variables custom:', error);
    }
  };

  const loadAvailableVariables = async () => {
    try {
      await invoke<string[]>('get_available_variables');
      // setAvailableVariables(variables); // Future: set available variables
    } catch (error) {
      console.error('Erreur lors du chargement des variables disponibles:', error);
    }
  };

  const resolveTemplate = async (template: string) => {
    if (!template.trim()) {
      setResolvedText('');
      return;
    }

    setIsResolving(true);
    try {
      const resolved = await invoke<string>('resolve_text', { text: template });
      setResolvedText(resolved);
    } catch (error) {
      console.error('Erreur lors de la résolution:', error);
      setResolvedText('Erreur lors de la résolution...');
    } finally {
      setIsResolving(false);
    }
  };

  const updateConfig = async (newConfig: DynamicVariableConfig) => {
    try {
      await invoke('update_dynamic_variable_config', { config: newConfig });
      setConfig(newConfig);
      toast.success('✅ Configuration mise à jour', {
        style: { background: '#10B981', color: 'white' },
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('❌ Erreur lors de la mise à jour');
    }
  };

  const addCustomVariable = async () => {
    if (!newVariableName || !newVariableValue) {
      toast.error('Nom et valeur requis');
      return;
    }

    if (Object.keys(customVariables).length >= config.max_custom_variables) {
      toast.error(`Limite de ${config.max_custom_variables} variables atteinte`);
      return;
    }

    try {
      await invoke('add_custom_variable', {
        name: newVariableName,
        value: newVariableValue,
      });
      
      toast.success('✨ Variable ajoutée', {
        style: { background: '#10B981', color: 'white' },
      });
      
      setNewVariableName('');
      setNewVariableValue('');
      setShowAddVariableModal(false);
      await loadCustomVariables();
      await loadAvailableVariables();
    } catch (error) {
      console.error('Erreur lors de l\'ajout:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const removeCustomVariable = async (name: string) => {
    try {
      await invoke('remove_custom_variable', { name });
      toast.success('🗑️ Variable supprimée');
      await loadCustomVariables();
      await loadAvailableVariables();
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const refreshAllVariables = async () => {
    try {
      await invoke('refresh_all_variables');
      toast.success('🔄 Variables actualisées', {
        style: { background: '#3B82F6', color: 'white' },
      });
      await loadVariables();
    } catch (error) {
      console.error('Erreur lors de l\'actualisation:', error);
      toast.error('❌ Erreur lors de l\'actualisation');
    }
  };

  const insertVariableIntoTemplate = (variableName: string) => {
    const variable = `{${variableName}}`;
    setCurrentTemplate(prev => prev + variable);
  };

  const tabs = [
    { id: 'editor', label: 'Éditeur', icon: '📝' },
    { id: 'templates', label: 'Templates', icon: '📋' },
    { id: 'variables', label: 'Variables', icon: '✨' },
    { id: 'config', label: 'Configuration', icon: '⚙️' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
              ✨ Dynamic Variables
            </h1>
            <p className="text-gray-400 mt-2">
              Système de variables dynamiques pour Rich Presence, Bio, Auto-Reply et plus
              {!config.enabled && (
                <span className="text-red-400 ml-2 animate-pulse">● DÉSACTIVÉ</span>
              )}
              {config.enabled && (
                <span className="text-green-400 ml-2 animate-pulse">● ACTIF</span>
              )}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={refreshAllVariables}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🔄 Actualiser
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowAddVariableModal(true)}
              className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-medium transition-all duration-300 shadow-lg shadow-indigo-500/25"
            >
              ➕ Variable Custom
            </motion.button>
          </div>
        </motion.div>

        {/* Warning si désactivé */}
        <AnimatePresence>
          {!config.enabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 border border-yellow-700/50 rounded-xl p-4 backdrop-blur-sm"
            >
              <div className="flex items-center">
                <div className="text-yellow-400 text-2xl mr-3 animate-bounce">⚠️</div>
                <div>
                  <h3 className="text-yellow-400 font-medium text-lg">Dynamic Variables désactivé</h3>
                  <p className="text-yellow-300 text-sm mt-1">
                    Activez le système pour utiliser les variables dans Rich Presence, Bio et Auto-Reply
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Stats rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-4"
        >
          <div className="bg-gradient-to-br from-indigo-500/20 to-purple-600/20 backdrop-blur-sm rounded-xl p-4 border border-indigo-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{cachedVariables.length}</div>
                <div className="text-sm text-gray-300">Variables en cache</div>
                <div className="text-xs text-gray-400 mt-1">Temps réel</div>
              </div>
              <div className="text-3xl opacity-80">📊</div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-sm rounded-xl p-4 border border-green-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{Object.keys(customVariables).length}</div>
                <div className="text-sm text-gray-300">Variables custom</div>
                <div className="text-xs text-gray-400 mt-1">Personnalisées</div>
              </div>
              <div className="text-3xl opacity-80">✨</div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-blue-500/20 to-cyan-600/20 backdrop-blur-sm rounded-xl p-4 border border-blue-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{config.enabled_providers.length}</div>
                <div className="text-sm text-gray-300">Providers actifs</div>
                <div className="text-xs text-gray-400 mt-1">Sources de données</div>
              </div>
              <div className="text-3xl opacity-80">🔌</div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-sm rounded-xl p-4 border border-purple-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{templates.length}</div>
                <div className="text-sm text-gray-300">Templates</div>
                <div className="text-xs text-gray-400 mt-1">Prêts à utiliser</div>
              </div>
              <div className="text-3xl opacity-80">📋</div>
            </div>
          </div>
        </motion.div>

        {/* Tabs Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="border-b border-gray-700/50"
        >
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                whileHover={{ y: -2 }}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-400 bg-indigo-500/10 rounded-t-lg'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                }`}
              >
                <span className="mr-2 text-lg">{tab.icon}</span>
                {tab.label}
              </motion.button>
            ))}
          </nav>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Editor Tab */}
            {activeTab === 'editor' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Editor */}
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                    <h3 className="text-xl font-semibold text-white mb-4">📝 Éditeur de Template</h3>
                    
                    <VariableEditor
                      value={currentTemplate}
                      onChange={setCurrentTemplate}
                      placeholder="Tapez votre template ici... Ex: Salut ! Il est {local_time} et j'écoute {spotify_track}"
                    />
                    
                    <div className="mt-4 flex justify-between items-center">
                      <div className="text-sm text-gray-400">
                        Variables: {(currentTemplate.match(/\{[^}]+\}/g) || []).length}
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setCurrentTemplate('')}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm"
                      >
                        🗑️ Effacer
                      </motion.button>
                    </div>
                  </div>

                  {/* Variables disponibles */}
                  <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                    <h4 className="text-lg font-semibold text-white mb-4">✨ Variables Disponibles</h4>
                    
                    {providers.map((provider) => (
                      <div key={provider.name} className="mb-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-lg">{provider.icon}</span>
                          <span className="text-white font-medium capitalize">{provider.name}</span>
                          <span className="text-xs text-gray-400">({provider.description})</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {provider.variables.map((variable) => {
                            const cachedVar = cachedVariables.find(v => v.name === variable);
                            return (
                              <VariableChip
                                key={variable}
                                name={variable}
                                value={cachedVar?.value || 'N/A'}
                                provider={provider.name}
                                onClick={() => insertVariableIntoTemplate(variable)}
                              />
                            );
                          })}
                        </div>
                      </div>
                    ))}

                    {/* Variables custom */}
                    {Object.keys(customVariables).length > 0 && (
                      <div className="mt-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-lg">🎨</span>
                          <span className="text-white font-medium">Custom</span>
                          <span className="text-xs text-gray-400">(Variables personnalisées)</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {Object.entries(customVariables).map(([name, value]) => (
                            <VariableChip
                              key={name}
                              name={name}
                              value={value}
                              provider="custom"
                              onClick={() => insertVariableIntoTemplate(name)}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Column - Preview */}
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                    <div className="flex items-center space-x-2 mb-4">
                      <h3 className="text-xl font-semibold text-white">👁️ Preview Temps Réel</h3>
                      {isResolving && (
                        <div className="animate-spin w-4 h-4 border-2 border-indigo-500 border-t-transparent rounded-full"></div>
                      )}
                    </div>
                    
                    <PreviewBox
                      template={currentTemplate}
                      resolvedText={resolvedText}
                    />
                  </div>

                  {/* Actions rapides */}
                  <div className="bg-gradient-to-r from-indigo-900/50 to-purple-900/50 rounded-xl p-6 border border-indigo-500/30">
                    <h4 className="text-lg font-semibold text-white mb-4">🚀 Actions Rapides</h4>
                    
                    <div className="space-y-3">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setActiveTab('templates')}
                        className="w-full p-3 bg-purple-600/20 border border-purple-500/30 rounded-lg text-left hover:bg-purple-600/30 transition-colors"
                      >
                        <div className="text-white font-medium">📋 Utiliser un template</div>
                        <div className="text-gray-400 text-sm">Choisir parmi {templates.length} templates prédéfinis</div>
                      </motion.button>
                      
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          if (resolvedText) {
                            navigator.clipboard.writeText(resolvedText);
                            toast.success('📋 Résultat copié !');
                          }
                        }}
                        disabled={!resolvedText}
                        className="w-full p-3 bg-green-600/20 border border-green-500/30 rounded-lg text-left hover:bg-green-600/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <div className="text-white font-medium">📋 Copier le résultat</div>
                        <div className="text-gray-400 text-sm">Copier le texte résolu dans le presse-papier</div>
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Templates Tab */}
            {activeTab === 'templates' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">📋 Templates Prédéfinis</h3>
                  <p className="text-gray-400 mb-6">
                    Cliquez sur un template pour l'utiliser dans l'éditeur
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {templates.map((template, index) => (
                      <TemplateCard
                        key={index}
                        {...template}
                        onUse={(templateText) => {
                          setCurrentTemplate(templateText);
                          setActiveTab('editor');
                          toast.success('📝 Template chargé dans l\'éditeur');
                        }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Variables Tab */}
            {activeTab === 'variables' && (
              <div className="space-y-6">
                {/* Variables en cache */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">📊 Variables en Cache</h3>
                    <span className="text-sm text-gray-400">
                      Mise à jour toutes les {config.auto_refresh_interval_seconds}s
                    </span>
                  </div>
                  
                  {cachedVariables.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">📊</div>
                      <div className="text-gray-400 text-lg">Aucune variable en cache</div>
                      <div className="text-gray-500 text-sm">Les variables apparaîtront après actualisation</div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {cachedVariables.map((variable) => (
                        <motion.div
                          key={variable.name}
                          whileHover={{ scale: 1.02 }}
                          className="bg-gray-700/50 rounded-lg p-4 border border-gray-600/30"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <code className="text-indigo-400 font-mono text-sm">{`{${variable.name}}`}</code>
                            <span className="text-xs bg-blue-600/20 text-blue-300 px-2 py-1 rounded">
                              {variable.provider}
                            </span>
                          </div>
                          <div className="text-white font-medium mb-1">{variable.value}</div>
                          <div className="text-xs text-gray-400">
                            Mis à jour: {new Date(variable.cached_at).toLocaleTimeString('fr-FR')}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Variables custom */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">✨ Variables Personnalisées</h3>
                    <span className="text-sm text-gray-400">
                      {Object.keys(customVariables).length}/{config.max_custom_variables}
                    </span>
                  </div>
                  
                  {Object.keys(customVariables).length === 0 ? (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">✨</div>
                      <div className="text-gray-400 text-lg">Aucune variable personnalisée</div>
                      <div className="text-gray-500 text-sm">Créez vos propres variables pour vos besoins spécifiques</div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {Object.entries(customVariables).map(([name, value]) => (
                        <motion.div
                          key={name}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-gray-700/50 rounded-lg p-4 border border-gray-600/30"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <code className="text-orange-400 font-mono text-sm">{`{${name}}`}</code>
                              <div className="text-white font-medium mt-1">{value}</div>
                            </div>
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => removeCustomVariable(name)}
                              className="p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                            >
                              🗑️
                            </motion.button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Config Tab */}
            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-6">⚙️ Configuration Dynamic Variables</h3>
                  
                  <div className="space-y-8">
                    {/* Général */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">📊 Paramètres généraux</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Durée du cache (secondes)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="3600"
                            value={config.cache_duration_seconds}
                            onChange={(e) => setConfig(prev => ({ ...prev, cache_duration_seconds: parseInt(e.target.value) || 1 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Intervalle d'actualisation (secondes)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="300"
                            value={config.auto_refresh_interval_seconds}
                            onChange={(e) => setConfig(prev => ({ ...prev, auto_refresh_interval_seconds: parseInt(e.target.value) || 1 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Maximum de variables personnalisées
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="100"
                            value={config.max_custom_variables}
                            onChange={(e) => setConfig(prev => ({ ...prev, max_custom_variables: parseInt(e.target.value) || 1 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Providers */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">🔌 Fournisseurs de Variables</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {providers.map((provider) => (
                          <motion.label 
                            key={provider.name} 
                            className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                            whileHover={{ scale: 1.02 }}
                          >
                            <input
                              type="checkbox"
                              checked={config.enabled_providers.includes(provider.name)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setConfig(prev => ({
                                    ...prev,
                                    enabled_providers: [...prev.enabled_providers, provider.name]
                                  }));
                                } else {
                                  setConfig(prev => ({
                                    ...prev,
                                    enabled_providers: prev.enabled_providers.filter(p => p !== provider.name)
                                  }));
                                }
                              }}
                              className="w-4 h-4 text-indigo-600 bg-gray-700 border-gray-600 rounded focus:ring-indigo-500 mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-lg">{provider.icon}</span>
                                <span className="text-white font-medium capitalize">{provider.name}</span>
                              </div>
                              <div className="text-gray-400 text-sm mt-1">{provider.description}</div>
                              <div className="text-xs text-gray-500 mt-1">
                                Variables: {provider.variables.join(', ')}
                              </div>
                            </div>
                          </motion.label>
                        ))}
                      </div>
                    </div>
                    
                    {/* Options système */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">🎯 Options du système</h4>
                      <motion.label 
                        className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                        whileHover={{ scale: 1.02 }}
                      >
                        <input
                          type="checkbox"
                          checked={config.enabled}
                          onChange={(e) => setConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                          className="w-4 h-4 text-indigo-600 bg-gray-700 border-gray-600 rounded focus:ring-indigo-500 mt-1"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">✨</span>
                            <span className="text-white font-medium">Activer le système Dynamic Variables</span>
                          </div>
                          <div className="text-gray-400 text-sm mt-1">
                            Permettre la résolution de variables dans toute l'application
                          </div>
                        </div>
                      </motion.label>
                    </div>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateConfig(config)}
                      className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg shadow-indigo-500/25"
                    >
                      ✅ Sauvegarder la configuration
                    </motion.button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Add Custom Variable Modal */}
      <AnimatePresence>
        {showAddVariableModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={() => setShowAddVariableModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-600 max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-semibold text-white mb-4">✨ Ajouter une Variable Custom</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Nom de la variable *
                  </label>
                  <input
                    type="text"
                    placeholder="mon_variable"
                    value={newVariableName}
                    onChange={(e) => setNewVariableName(e.target.value.replace(/[^a-zA-Z0-9_]/g, ''))}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 font-mono"
                  />
                  <div className="text-xs text-gray-400 mt-1">
                    Sera utilisée comme <code className="bg-gray-700 px-1 rounded">{`{${newVariableName || 'nom_variable'}}`}</code>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Valeur *
                  </label>
                  <input
                    type="text"
                    placeholder="Valeur de votre variable"
                    value={newVariableValue}
                    onChange={(e) => setNewVariableValue(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowAddVariableModal(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Annuler
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={addCustomVariable}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  ✅ Ajouter
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}