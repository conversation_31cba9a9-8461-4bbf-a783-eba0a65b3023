<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="glow1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="glow2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="glow3" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:0" />
    </radialGradient>
    
    <filter id="blur">
      <feGaussianBlur stdDeviation="40"/>
    </filter>
    
    <filter id="textGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Background glow effects -->
  <circle cx="200" cy="200" r="150" fill="url(#glow1)" filter="url(#blur)"/>
  <circle cx="1000" cy="250" r="180" fill="url(#glow2)" filter="url(#blur)"/>
  <circle cx="250" cy="500" r="140" fill="url(#glow3)" filter="url(#blur)"/>
  
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#1a1a1a" stroke-width="1" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)"/>
  
  <!-- Main title -->
  <text x="600" y="280" text-anchor="middle" 
        font-family="Arial, sans-serif" 
        font-size="120" 
        font-weight="bold" 
        fill="url(#titleGradient)"
        filter="url(#textGlow)">VoidBot</text>
  
  <!-- Subtitle -->
  <text x="600" y="340" text-anchor="middle" 
        font-family="Arial, sans-serif" 
        font-size="36" 
        font-weight="600" 
        fill="#9ca3af">Toolkit Discord Avancé</text>
  
  <!-- Description -->
  <text x="600" y="380" text-anchor="middle" 
        font-family="Arial, sans-serif" 
        font-size="24" 
        fill="#6b7280">Clone moderne de Nighty • Interface desktop sécurisée • 100+ commandes</text>
  
  <!-- Features badges -->
  <g transform="translate(350, 450)">
    <rect x="0" y="0" width="120" height="30" rx="15" fill="#6366f1" opacity="0.2"/>
    <text x="60" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6366f1">Mode Furtif</text>
  </g>
  
  <g transform="translate(490, 450)">
    <rect x="0" y="0" width="140" height="30" rx="15" fill="#8b5cf6" opacity="0.2"/>
    <text x="70" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#8b5cf6">Snipers Ultra-Rapides</text>
  </g>
  
  <g transform="translate(650, 450)">
    <rect x="0" y="0" width="120" height="30" rx="15" fill="#06b6d4" opacity="0.2"/>
    <text x="60" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#06b6d4">100% Sécurisé</text>
  </g>
  
  <!-- VoidBot icon -->
  <g transform="translate(520, 150)">
    <circle cx="80" cy="40" r="35" fill="url(#titleGradient)" opacity="0.8"/>
    <text x="80" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="40" font-weight="bold" fill="white">V</text>
  </g>
</svg>