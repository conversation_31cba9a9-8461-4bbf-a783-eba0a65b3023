[package]
name = "voidbot-shared"
version.workspace = true
edition.workspace = true

[dependencies]
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
uuid = { workspace = true, features = ["v4"] }
thiserror.workspace = true

# Sécurité et chiffrement - VERSIONS ÉPINGLÉES SÉCURITÉ
aes-gcm = "=0.10.3"
sha2 = "=0.10.8"
base64 = "=0.22.1"
rand = "=0.8.5"
dirs = "=5.0.1"
secstr = "=0.5.1"

# Validation - VERSION ÉPINGLÉE
regex = "=1.10.6"

# Async - VERSIONS ÉPINGLÉES
async-trait = "=0.1.83"
tokio = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true }
reqwest = { workspace = true }

# Database - VERSION ÉPINGLÉE
sqlx = { workspace = true }

# System info - VERSION ÉPINGLÉE
hostname = "=0.4.0"

# OAuth support - VERSIONS ÉPINGLÉES
urlencoding = "=2.1.3"