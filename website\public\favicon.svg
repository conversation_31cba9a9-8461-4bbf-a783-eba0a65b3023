<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128">
  <defs>
    <linearGradient id="voidGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </radialGradient>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)"/>
  
  <!-- VoidBot V shape -->
  <path d="M32 35 L50 35 L64 75 L78 35 L96 35 L74 85 L64 65 L54 85 Z" 
        fill="url(#voidGradient)" 
        filter="url(#glow)"/>
  
  <!-- Accent dot -->
  <circle cx="64" cy="55" r="4" fill="#06b6d4" opacity="0.8"/>
  
  <!-- Responsive styles -->
  <style>
    @media (prefers-color-scheme: light) {
      circle:first-child { fill: url(#bgGradient); }
    }
    @media (prefers-color-scheme: dark) {
      circle:first-child { fill: url(#bgGradient); }
    }
  </style>
</svg>