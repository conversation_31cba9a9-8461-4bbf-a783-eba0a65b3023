#!/bin/bash

# Script de déploiement VoidBot sur Railway
echo "🚀 Déploiement VoidBot sur Railway..."

# Vérifier que Railway CLI est installé
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI n'est pas installé"
    echo "💡 Installez-le avec: npm install -g @railway/cli"
    exit 1
fi

# Se connecter à Railway (si pas déjà fait)
echo "🔐 Connexion à Railway..."
railway auth

# Créer le projet VoidBot
echo "📦 Création du projet VoidBot..."
railway project new voidbot

# Ajouter la base de données PostgreSQL
echo "🗄️ Ajout de PostgreSQL..."
railway add postgresql

# Configurer les variables d'environnement
echo "⚙️ Configuration des variables d'environnement..."
echo "📝 Veuillez configurer DISCORD_BOT_TOKEN via:"
echo "   railway variables set DISCORD_BOT_TOKEN=your_token_here"

# Déployer le bot Discord
echo "🚀 Déploiement du bot Discord..."
railway deploy

echo "✅ Déploiement terminé !"
echo "🌐 Votre bot est maintenant hébergé sur Railway"
echo "📊 URL de sanité: https://your-app.railway.app/health"