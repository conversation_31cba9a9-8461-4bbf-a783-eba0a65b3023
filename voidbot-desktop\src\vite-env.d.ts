/// <reference types="vite/client" />

/**
 * Déclarations TypeScript pour l'environnement VoidBot
 * Inclut les types Vite et les définitions personnalisées
 */

// Import des types VoidBot personnalisés
/// <reference path="./types/voidbot.d.ts" />

// Variables d'environnement Vite typées
interface ImportMetaEnv {
  /** Mode de développement Vite */
  readonly VITE_DEV_MODE: string;
  /** Version de l'application */
  readonly VITE_APP_VERSION: string;
  /** Nom de l'application */
  readonly VITE_APP_NAME: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
