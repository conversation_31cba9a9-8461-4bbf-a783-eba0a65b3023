import { useState } from 'react';
import { useAppStore } from '../stores/appStore';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

interface DiscordAuthResult {
  success: boolean;
  token?: string;
  user_info?: DiscordUserInfo;
  error?: string;
}

interface DiscordUserInfo {
  id: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
}

export function LoginScreen() {
  const [isAuthLoading, setIsAuthLoading] = useState(false);
  const [error, setError] = useState('');
  const [authStatus, setAuthStatus] = useState('');
  const { connectBot, botStatus } = useAppStore();


  const handleWebLogin = async () => {
    setIsAuthLoading(true);
    setError('');
    setAuthStatus('Ouverture webview Discord...');

    try {
      // En mode test, simuler directement l'extraction
      const testMode = import.meta.env.VITE_TEST_MODE === 'true';
      if (testMode) {
        setAuthStatus('Mode test détecté - Simulation extraction...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setAuthStatus('Token extrait ! Vérification...');
        await new Promise(resolve => setTimeout(resolve, 800));
        
        try {
          // Vérifier avec un token de test
          const _userInfo = await invoke('verify_discord_token', { 
            token: 'test_token_mock_development_mode_voidbot'
          });
          
          setAuthStatus('Connexion réussie !');
          
          // Connecter le bot avec le token de test
          await connectBot('test_token_mock_development_mode_voidbot', true);
          
        } catch (err) {
          console.error('Erreur vérification token:', err);
          setError('Erreur mode test : ' + err);
        }
        
        setIsAuthLoading(false);
        setAuthStatus('');
        return;
      }

      // Écouter l'événement d'extraction du token (mode normal)
      const unlisten = await listen('token-extracted', async (event: any) => {
        const result: DiscordAuthResult = event.payload;
        
        if (result.success && result.token) {
          setAuthStatus('Token extrait ! Vérification...');
          
          try {
            // Vérifier la validité du token
            const _userInfo = await invoke('verify_discord_token', { 
              token: result.token 
            });
            
            setAuthStatus('Connexion réussie !');
            
            // Connecter le bot avec le token utilisateur
            await connectBot(result.token, true);
            
            // Nettoyer les listeners
            unlisten();
            
          } catch (err) {
            console.error('Erreur vérification token:', err);
            setError('Token Discord invalide ou expiré');
          }
        } else {
          setError(result.error || 'Erreur extraction token Discord');
        }
        
        setIsAuthLoading(false);
        setAuthStatus('');
      });

      // Démarrer l'authentification webview
      setAuthStatus('Démarrage extraction automatique...');
      const result: DiscordAuthResult = await invoke('start_discord_webauth');
      
      if (!result.success) {
        setError(result.error || 'Échec authentification webview');
        setIsAuthLoading(false);
        setAuthStatus('');
        unlisten();
      }
      
    } catch (err) {
      console.error('Erreur authentification webview:', err);
      const errorMessage = typeof err === 'string' ? err : 'Erreur lors de l\'authentification Discord';
      setError(errorMessage);
      setIsAuthLoading(false);
      setAuthStatus('');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-voidbot-dark via-voidbot-darker to-black flex items-center justify-center p-8">
      <div className="w-full max-w-md">
        {/* Logo et titre */}
        <div className="text-center mb-8">
          <div className="relative">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-voidbot-primary via-voidbot-secondary to-voidbot-accent bg-clip-text text-transparent mb-4">
              VoidBot
            </h1>
            <div className="absolute -inset-1 bg-gradient-to-r from-voidbot-primary/20 to-voidbot-secondary/20 blur-xl rounded-lg -z-10"></div>
          </div>
          <p className="text-voidbot-secondary text-lg">
            Toolkit Discord avancé
          </p>
          <p className="text-voidbot-secondary/70 text-sm mt-2">
            Connectez-vous pour accéder à toutes les fonctionnalités
          </p>
        </div>

        {/* Statut de connexion */}
        {(botStatus === 'connecting' || isAuthLoading) && (
          <div className="mb-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg text-center">
            <div className="flex items-center justify-center gap-2 text-yellow-400">
              <div className="w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
              <span>{authStatus || 'Connexion en cours...'}</span>
            </div>
          </div>
        )}


        {/* Erreur */}
        {error && (
          <div className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg text-center">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Carte de connexion */}
        <div className="card space-y-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-voidbot-primary mb-2">
              Connexion Discord
            </h2>
            <p className="text-voidbot-secondary text-sm">
              Authentification sécurisée via Discord officiel
            </p>
          </div>

          {/* Connexion Discord automatique */}
          <div>
            <button
              onClick={handleWebLogin}
              className="w-full btn btn-primary py-4 text-base font-medium"
              disabled={isAuthLoading}
            >
              <div className="flex items-center justify-center gap-3">
                {isAuthLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <span className="text-xl">👻</span>
                )}
                <div className="text-center">
                  <div>Connexion Discord Automatique</div>
                  <div className="text-xs opacity-80">Extraction automatique du token (comme Nighty)</div>
                </div>
              </div>
            </button>
          </div>

          {/* Informations sur la sécurité */}
          <div className="bg-voidbot-dark/50 border border-voidbot-secondary/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <span className="text-xl">🛡️</span>
              <div className="text-sm text-voidbot-secondary">
                <p className="font-medium text-voidbot-primary mb-1">Extraction sécurisée</p>
                <p>VoidBot extrait automatiquement votre token Discord depuis la webview officielle. Aucune donnée n'est transmise à des tiers.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Infos Webview */}
        <div className="mt-8 p-4 bg-voidbot-primary/5 border border-voidbot-primary/20 rounded-lg">
          <h3 className="text-sm font-medium text-voidbot-primary mb-2 flex items-center gap-2">
            <span>👻</span>
            Authentification Selfbot (comme Nighty)
          </h3>
          <ul className="text-xs text-voidbot-secondary space-y-1">
            <li>• Webview Discord officielle intégrée</li>
            <li>• Extraction automatique du token utilisateur</li>
            <li>• Sessions chiffrées AES-256-GCM localement</li>
            <li>• Méthode identique à Nighty pour la compatibilité</li>
            <li>• Traitement 100% local, aucune transmission externe</li>
            <li>• Code source ouvert et auditable</li>
          </ul>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-xs text-voidbot-secondary/50">
            VoidBot v1.0.0 • Made with 💜 for Discord
          </p>
        </div>
      </div>
    </div>
  );
}