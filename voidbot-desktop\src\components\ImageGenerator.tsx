import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';

interface ImageConfig {
  enabled: boolean;
  default_width: number;
  default_height: number;
  default_font_size: number;
  max_image_size_mb: number;
  compression_quality: number;
}

export default function ImageGenerator() {
  const [config, setConfig] = useState<ImageConfig>({
    enabled: true,
    default_width: 800,
    default_height: 600,
    default_font_size: 48,
    max_image_size_mb: 5,
    compression_quality: 85,
  });

  const [activeTab, setActiveTab] = useState<'meme' | 'avatar' | 'banner' | 'reaction' | 'stats'>('meme');
  const [isLoading, setIsLoading] = useState(false);

  // Form states
  const [memeForm, setMemeForm] = useState({
    topText: '',
    bottomText: '',
  });

  const [bannerForm, setBannerForm] = useState({
    text: 'VoidBot',
    style: 'cyberpunk',
  });

  const [reactionForm, setReactionForm] = useState({
    type: 'think',
    text: '',
  });

  const [avatarOverlay, setAvatarOverlay] = useState('crown');

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const imageConfig = await invoke<ImageConfig>('get_image_config');
      setConfig(imageConfig);
    } catch (error) {
      console.error('Erreur lors du chargement de la config images:', error);
      toast.error('Impossible de charger la configuration des images');
    }
  };

  const updateConfig = async (updates: Partial<ImageConfig>) => {
    try {
      await invoke('update_image_config', {
        enabled: updates.enabled,
        width: updates.default_width,
        height: updates.default_height,
        fontSize: updates.default_font_size,
        maxSizeMb: updates.max_image_size_mb,
        quality: updates.compression_quality,
      });
      
      setConfig(prev => ({ ...prev, ...updates }));
      toast.success('Configuration mise à jour');
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('Erreur lors de la mise à jour de la configuration');
    }
  };

  const toggleImageGeneration = async () => {
    try {
      const newEnabled = !config.enabled;
      await invoke('toggle_image_generation', { enabled: newEnabled });
      setConfig(prev => ({ ...prev, enabled: newEnabled }));
      toast.success(newEnabled ? 'Génération d\'images activée' : 'Génération d\'images désactivée');
    } catch (error) {
      console.error('Erreur lors du toggle:', error);
      toast.error('Erreur lors du changement de statut');
    }
  };

  const resetConfig = async () => {
    try {
      await invoke('reset_image_config');
      await loadConfig();
      toast.success('Configuration réinitialisée');
    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      toast.error('Erreur lors de la réinitialisation');
    }
  };

  const generateImage = async () => {
    if (!config.enabled) {
      toast.error('La génération d\'images est désactivée');
      return;
    }

    setIsLoading(true);
    try {
      // Simulation d'une génération d'image
      // En production, ceci interagirait directement avec le bot Discord
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      let message = '';
      switch (activeTab) {
        case 'meme':
          message = `Meme généré: "${memeForm.topText}"`;
          break;
        case 'banner':
          message = `Bannière générée: "${bannerForm.text}" (style: ${bannerForm.style})`;
          break;
        case 'reaction':
          message = `Réaction générée: ${reactionForm.type}`;
          break;
        case 'avatar':
          message = `Overlay avatar généré: ${avatarOverlay}`;
          break;
        case 'stats':
          message = 'Statistiques utilisateur générées';
          break;
      }
      
      toast.success(message);
    } catch (error) {
      console.error('Erreur lors de la génération:', error);
      toast.error('Erreur lors de la génération de l\'image');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Générateur d'Images</h1>
          <p className="text-gray-400 mt-1">Créez des memes, bannières et images personnalisées</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Activé:</span>
            <button
              onClick={toggleImageGeneration}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-indigo-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
          
          <button
            onClick={resetConfig}
            className="px-3 py-1.5 text-sm bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Reset Config
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'meme', label: 'Memes', icon: '😂' },
            { id: 'avatar', label: 'Avatar Overlay', icon: '👑' },
            { id: 'banner', label: 'Bannières', icon: '🎨' },
            { id: 'reaction', label: 'Réactions', icon: '🎭' },
            { id: 'stats', label: 'Statistiques', icon: '📊' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form Panel */}
        <div className="lg:col-span-2 space-y-6">
          {/* Meme Form */}
          {activeTab === 'meme' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Générateur de Memes</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Texte supérieur
                  </label>
                  <input
                    type="text"
                    value={memeForm.topText}
                    onChange={(e) => setMemeForm(prev => ({ ...prev, topText: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    placeholder="Entrez le texte supérieur"
                    maxLength={100}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Texte inférieur (optionnel)
                  </label>
                  <input
                    type="text"
                    value={memeForm.bottomText}
                    onChange={(e) => setMemeForm(prev => ({ ...prev, bottomText: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    placeholder="Entrez le texte inférieur"
                    maxLength={100}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Banner Form */}
          {activeTab === 'banner' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Générateur de Bannières</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Texte de la bannière
                  </label>
                  <input
                    type="text"
                    value={bannerForm.text}
                    onChange={(e) => setBannerForm(prev => ({ ...prev, text: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    placeholder="Entrez le texte"
                    maxLength={50}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Style
                  </label>
                  <select
                    value={bannerForm.style}
                    onChange={(e) => setBannerForm(prev => ({ ...prev, style: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  >
                    <option value="gradient">Gradient</option>
                    <option value="neon">Néon</option>
                    <option value="minimal">Minimal</option>
                    <option value="cyberpunk">Cyberpunk</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Reaction Form */}
          {activeTab === 'reaction' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Générateur de Réactions</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Type de réaction
                  </label>
                  <select
                    value={reactionForm.type}
                    onChange={(e) => setReactionForm(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  >
                    <option value="triggered">Triggered</option>
                    <option value="stonks">Stonks</option>
                    <option value="notstonks">Not Stonks</option>
                    <option value="drake">Drake</option>
                    <option value="surprised">Surpris</option>
                    <option value="confused">Confus</option>
                    <option value="think">Réfléchir</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Texte personnalisé (optionnel)
                  </label>
                  <input
                    type="text"
                    value={reactionForm.text}
                    onChange={(e) => setReactionForm(prev => ({ ...prev, text: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    placeholder="Texte optionnel"
                    maxLength={100}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Avatar Overlay Form */}
          {activeTab === 'avatar' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Overlay Avatar</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Type d'overlay
                  </label>
                  <select
                    value={avatarOverlay}
                    onChange={(e) => setAvatarOverlay(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  >
                    <option value="crown">Couronne</option>
                    <option value="hat">Chapeau</option>
                    <option value="sunglasses">Lunettes</option>
                    <option value="frame">Cadre</option>
                    <option value="jail">Prison</option>
                    <option value="wanted">Wanted</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Stats Form */}
          {activeTab === 'stats' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Statistiques Utilisateur</h3>
              <p className="text-gray-400">
                Génère une image avec les statistiques de l'utilisateur actuel.
                Les données incluent : messages envoyés, serveurs rejoints, amis, temps en ligne.
              </p>
            </div>
          )}

          {/* Generate Button */}
          <button
            onClick={generateImage}
            disabled={!config.enabled || isLoading}
            className={`w-full py-3 px-6 rounded-lg font-medium text-white transition-colors ${
              !config.enabled || isLoading
                ? 'bg-gray-600 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-700'
            }`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Génération en cours...</span>
              </div>
            ) : (
              `Générer ${activeTab === 'meme' ? 'Meme' : activeTab === 'banner' ? 'Bannière' : activeTab === 'reaction' ? 'Réaction' : activeTab === 'avatar' ? 'Overlay' : 'Statistiques'}`
            )}
          </button>
        </div>

        {/* Config Panel */}
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Configuration</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Largeur par défaut
                </label>
                <input
                  type="number"
                  min="100"
                  max="4000"
                  value={config.default_width}
                  onChange={(e) => updateConfig({ default_width: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Hauteur par défaut
                </label>
                <input
                  type="number"
                  min="100"
                  max="4000"
                  value={config.default_height}
                  onChange={(e) => updateConfig({ default_height: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Taille de police
                </label>
                <input
                  type="number"
                  min="8"
                  max="200"
                  step="0.1"
                  value={config.default_font_size}
                  onChange={(e) => updateConfig({ default_font_size: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Qualité de compression (%)
                </label>
                <input
                  type="number"
                  min="10"
                  max="100"
                  value={config.compression_quality}
                  onChange={(e) => updateConfig({ compression_quality: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Taille max (MB)
                </label>
                <input
                  type="number"
                  min="1"
                  max="25"
                  value={config.max_image_size_mb}
                  onChange={(e) => updateConfig({ max_image_size_mb: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Statut</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">État:</span>
                <span className={config.enabled ? 'text-green-400' : 'text-red-400'}>
                  {config.enabled ? 'Activé' : 'Désactivé'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Résolution:</span>
                <span className="text-white">{config.default_width}x{config.default_height}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Police:</span>
                <span className="text-white">{config.default_font_size}px</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}