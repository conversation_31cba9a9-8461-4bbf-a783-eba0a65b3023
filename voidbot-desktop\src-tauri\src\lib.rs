use tauri::{command, Manager, State, Window, WebviewUrl, WebviewWindowBuilder, Emitter, AppHandle};
use std::sync::Arc;
use tokio::sync::Mutex;
use voidbot_shared::{StealthMode, VoidNotification, NotificationConfig, NotificationManager, AutoCommandsState, AutoTranslateConfig, AutoSlashConfig, AutoReplyConfig, SlashTrigger, AutoReply, SecureStorage, Validate, StringValidator, DiscordIdValidator, GiveawayManager, GiveawayConfig, GiveawayInfo, GiveawayStats, NitroSniperManager, NitroSniperConfig, NitroCodeInfo, NitroSniperStats, RateLimiter, RateLimitConfig, RateLimitStats, Database, DatabaseConfig, IpcMessage, InMemoryIpcClient, ActivityViewer, ActivityViewerConfig, TrackedUser, ActivityStats, ActivityEvent, DynamicVariableManager, DynamicVariableConfig, CachedVariable};
use voidbot_shared::database::{DatabaseStats, UsageStat, EventLog, CommandLog, EventSeverity};
use serde::{Deserialize, Serialize};
use chrono;
use sysinfo::System;
use auto_launch::{AutoLaunch, AutoLaunchBuilder};
use rdev::{listen, EventType, Button};
use std::sync::atomic::{AtomicBool, Ordering};
use tauri_plugin_updater::UpdaterExt;

mod webview_auth;
use webview_auth::{start_discord_webauth, verify_discord_token};

mod ipc_handler;
use ipc_handler::start_ipc_processor;

#[derive(Debug, Serialize, Deserialize)]
struct DiscordUser {
    pub id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub email: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ImageGenerationConfig {
    pub enabled: bool,
    pub default_width: u32,
    pub default_height: u32,
    pub default_font_size: f32,
    pub max_image_size_mb: u32,
    pub compression_quality: u8,
}

// Structures pour TrollControl
#[derive(Debug, Serialize, Deserialize, Clone)]
struct TrollSession {
    pub id: String,
    #[serde(rename = "type")]
    pub troll_type: String,
    pub target_user: Option<String>,
    pub guild_id: Option<String>,
    pub channel_id: Option<String>,
    pub started_at: String,
    pub config: TrollConfig,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct TrollConfig {
    pub target_user: Option<String>,
    pub message_content: Option<String>,
    pub delay_ms: u64,
    pub max_actions: i32,
    pub auto_stop_minutes: Option<u64>,
}

impl Default for ImageGenerationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            default_width: 800,
            default_height: 600,
            default_font_size: 48.0,
            max_image_size_mb: 5,
            compression_quality: 85,
        }
    }
}

// Structures pour Theme Builder
#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeColors {
    pub primary: String,
    pub secondary: String,
    pub accent: String,
    pub background: String,
    pub surface: String,
    pub text: String,
    pub text_secondary: String,
    pub success: String,
    pub warning: String,
    pub error: String,
    pub border: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeGradients {
    pub primary: String,
    pub secondary: String,
    pub accent: String,
    pub background: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeShadows {
    pub small: String,
    pub medium: String,
    pub large: String,
    pub glow: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeBorderRadius {
    pub small: String,
    pub medium: String,
    pub large: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeAnimations {
    pub duration: String,
    pub easing: String,
    pub scale: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeConfig {
    pub name: String,
    pub colors: ThemeColors,
    pub gradients: ThemeGradients,
    pub shadows: ThemeShadows,
    pub border_radius: ThemeBorderRadius,
    pub animations: ThemeAnimations,
    pub is_default: bool,
    pub created_at: String,
    pub last_modified: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct PresetTheme {
    pub id: String,
    pub name: String,
    pub description: String,
    pub preview: String,
    pub category: String, // 'builtin', 'community', 'custom'
    pub config: ThemeConfig,
}

impl Default for ThemeConfig {
    fn default() -> Self {
        let now = chrono::Utc::now().to_rfc3339();
        Self {
            name: "Default Cyberpunk".to_string(),
            colors: ThemeColors {
                primary: "#6366f1".to_string(),
                secondary: "#8b5cf6".to_string(),
                accent: "#22d3ee".to_string(),
                background: "#0a0a0a".to_string(),
                surface: "#1a1a1a".to_string(),
                text: "#ffffff".to_string(),
                text_secondary: "#a3a3a3".to_string(),
                success: "#10b981".to_string(),
                warning: "#f59e0b".to_string(),
                error: "#ef4444".to_string(),
                border: "#374151".to_string(),
            },
            gradients: ThemeGradients {
                primary: "linear-gradient(135deg, #6366f1, #8b5cf6)".to_string(),
                secondary: "linear-gradient(135deg, #8b5cf6, #ec4899)".to_string(),
                accent: "linear-gradient(135deg, #22d3ee, #06b6d4)".to_string(),
                background: "linear-gradient(135deg, #0a0a0a, #1a1a1a)".to_string(),
            },
            shadows: ThemeShadows {
                small: "0 1px 3px rgba(99, 102, 241, 0.12)".to_string(),
                medium: "0 4px 6px rgba(99, 102, 241, 0.15)".to_string(),
                large: "0 10px 25px rgba(99, 102, 241, 0.2)".to_string(),
                glow: "0 0 20px rgba(99, 102, 241, 0.4)".to_string(),
            },
            border_radius: ThemeBorderRadius {
                small: "0.375rem".to_string(),
                medium: "0.5rem".to_string(),
                large: "0.75rem".to_string(),
            },
            animations: ThemeAnimations {
                duration: "200ms".to_string(),
                easing: "cubic-bezier(0.4, 0, 0.2, 1)".to_string(),
                scale: 1.05,
            },
            is_default: true,
            created_at: now.clone(),
            last_modified: now,
        }
    }
}

// === AUTO-UPDATER STRUCTURES ===

#[derive(Debug, Serialize, Deserialize, Clone)]
struct UpdateInfo {
    pub version: String,
    pub release_date: String,
    pub notes: String,
    pub download_url: String,
    pub signature: String,
    pub size: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct UpdateConfig {
    pub auto_check: bool,
    pub auto_download: bool,
    pub auto_install: bool,
    pub check_interval_hours: u32,
    pub beta_updates: bool,
    pub last_check: Option<String>,
}

impl Default for UpdateConfig {
    fn default() -> Self {
        Self {
            auto_check: true,
            auto_download: false,
            auto_install: false,
            check_interval_hours: 24,
            beta_updates: false,
            last_check: None,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct UpdateStatus {
    pub checking: bool,
    pub available: bool,
    pub downloading: bool,
    pub installing: bool,
    pub error: Option<String>,
    pub progress: f64,
    pub update_info: Option<UpdateInfo>,
}

impl Default for UpdateStatus {
    fn default() -> Self {
        Self {
            checking: false,
            available: false,
            downloading: false,
            installing: false,
            error: None,
            progress: 0.0,
            update_info: None,
        }
    }
}

#[derive(Clone)]
struct AppState {
    stealth_mode: Arc<Mutex<StealthMode>>,
    bot_connected: Arc<Mutex<bool>>,
    notification_manager: Arc<Mutex<NotificationManager>>,
    auto_commands_state: Arc<Mutex<AutoCommandsState>>,
    secure_storage: Arc<Mutex<Option<SecureStorage>>>,
    giveaway_manager: Arc<Mutex<GiveawayManager>>,
    nitro_sniper_manager: Arc<Mutex<NitroSniperManager>>,
    image_config: Arc<Mutex<ImageGenerationConfig>>,
    rate_limiter: Arc<RateLimiter>,
    database: Arc<Database>,
    ipc_receiver: Arc<Mutex<tokio::sync::mpsc::UnboundedReceiver<IpcMessage>>>,
    activity_viewer: Arc<Mutex<ActivityViewer>>,
    dynamic_variables: Arc<Mutex<DynamicVariableManager>>,
    troll_sessions: Arc<Mutex<std::collections::HashMap<String, TrollSession>>>,
    system_monitor: Arc<Mutex<System>>,
    discord_processes: Arc<Mutex<Vec<DiscordProcess>>>,
    auto_launcher: Arc<Mutex<Option<AutoLaunch>>>,
    overlay_config: Arc<Mutex<OverlayConfig>>,
    last_mouse_position: Arc<Mutex<MousePosition>>,
    current_theme: Arc<Mutex<ThemeConfig>>,
    custom_themes: Arc<Mutex<Vec<PresetTheme>>>,
    update_config: Arc<Mutex<UpdateConfig>>,
    update_status: Arc<Mutex<UpdateStatus>>,
}

// Structure pour les processus Discord détectés
#[derive(Debug, Clone, Serialize, Deserialize)]
struct DiscordProcess {
    pub pid: u32,
    pub name: String,
    pub exe_path: Option<String>,
    pub memory_usage: u64,
    pub cpu_usage: f32,
    pub start_time: u64,
}

// Structure pour l'overlay et le menu contextuel
#[derive(Debug, Clone, Serialize, Deserialize)]
struct OverlayConfig {
    pub enabled: bool,
    pub auto_show: bool,
    pub transparency: f64,
    pub theme: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct MousePosition {
    pub x: f64,
    pub y: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ContextMenuData {
    pub mouse_position: MousePosition,
    pub discord_window_active: bool,
    pub timestamp: u64,
}

// Variables globales pour la gestion des événements
static OVERLAY_ACTIVE: AtomicBool = AtomicBool::new(false);
static RIGHT_CLICK_LISTENER: AtomicBool = AtomicBool::new(false);

// Fonction utilitaire pour scanner les processus Discord
async fn scan_discord_processes_impl(state: &AppState) -> Result<Vec<DiscordProcess>, String> {
    let mut system = state.system_monitor.lock().await;
    system.refresh_processes();
    
    let mut discord_processes = Vec::new();
    
    for (pid, process) in system.processes() {
        let process_name = process.name().to_string();
        
        // Détecter les processus Discord (Discord, DiscordCanary, DiscordPTB, etc.)
        if process_name.to_lowercase().contains("discord") {
            discord_processes.push(DiscordProcess {
                pid: pid.as_u32(),
                name: process_name.to_string(),
                exe_path: process.exe().map(|p| p.to_string_lossy().to_string()),
                memory_usage: process.memory(),
                cpu_usage: process.cpu_usage(),
                start_time: 0, // TODO: Get actual start time
            });
        }
    }
    
    Ok(discord_processes)
}

// Fonction utilitaire pour valider un token utilisateur Discord
fn is_valid_user_token(token: &str) -> bool {
    // Validation basique du format du token Discord
    if token.is_empty() || token.len() < 10 {
        return false;
    }
    
    // Vérifier que ce n'est pas un token de bot (doit commencer par "mfa.")
    if token.starts_with("Bot ") || token.starts_with("Bearer ") {
        return false;
    }
    
    // Validation plus sophistiquée si nécessaire
    // Pour l'instant, on fait juste une validation basique
    token.chars().all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '_' || c == '-')
}

impl AppState {
    pub async fn new() -> (Self, tokio::sync::mpsc::UnboundedReceiver<IpcMessage>) {
        let db_config = DatabaseConfig::default();
        let database = match Database::new(db_config).await {
            Ok(db) => db,
            Err(e) => {
                eprintln!("⚠️  Warning: Failed to initialize database: {}. Continuing without database...", e);
                // Créer une base temporaire en mémoire comme fallback
                let temp_config = DatabaseConfig {
                    database_path: std::path::PathBuf::from(":memory:"),
                    max_connections: 5,
                    connection_timeout: 30,
                    enable_sql_logging: false,
                };
                Database::new(temp_config).await.expect("Failed to create in-memory database")
            }
        };
        
        // Créer le canal IPC pour recevoir les messages du bot Discord
        let (_ipc_client, ipc_receiver) = InMemoryIpcClient::new();
        let (_, bot_receiver) = tokio::sync::mpsc::unbounded_channel();
        
        let app_state = Self {
            stealth_mode: Arc::new(Mutex::new(StealthMode::Normal)),
            bot_connected: Arc::new(Mutex::new(false)),
            notification_manager: Arc::new(Mutex::new(NotificationManager::new())),
            auto_commands_state: Arc::new(Mutex::new(AutoCommandsState::default())),
            secure_storage: Arc::new(Mutex::new(None)),
            giveaway_manager: Arc::new(Mutex::new(GiveawayManager::new())),
            nitro_sniper_manager: Arc::new(Mutex::new(NitroSniperManager::new())),
            image_config: Arc::new(Mutex::new(ImageGenerationConfig::default())),
            rate_limiter: Arc::new(RateLimiter::new(RateLimitConfig::default())),
            database: Arc::new(database),
            ipc_receiver: Arc::new(Mutex::new(bot_receiver)),
            activity_viewer: Arc::new(Mutex::new(ActivityViewer::new())),
            dynamic_variables: Arc::new(Mutex::new(DynamicVariableManager::new())),
            troll_sessions: Arc::new(Mutex::new(std::collections::HashMap::new())),
            system_monitor: Arc::new(Mutex::new(System::new_all())),
            discord_processes: Arc::new(Mutex::new(Vec::new())),
            auto_launcher: Arc::new(Mutex::new(None)),
            overlay_config: Arc::new(Mutex::new(OverlayConfig {
                enabled: true,
                auto_show: true,
                transparency: 0.95,
                theme: "dark".to_string(),
            })),
            last_mouse_position: Arc::new(Mutex::new(MousePosition { x: 0.0, y: 0.0 })),
            current_theme: Arc::new(Mutex::new(ThemeConfig::default())),
            custom_themes: Arc::new(Mutex::new(Vec::new())),
            update_config: Arc::new(Mutex::new(UpdateConfig::default())),
            update_status: Arc::new(Mutex::new(UpdateStatus::default())),
        };
        
        (app_state, ipc_receiver)
    }
}

// Commandes Tauri pour VoidBot

#[command]
async fn open_url(url: String) -> Result<(), String> {
    // Validation de l'URL
    if !url.starts_with("http://") && !url.starts_with("https://") {
        return Err("URL invalide".to_string());
    }
    
    // Ouvrir l'URL dans le navigateur par défaut
    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("cmd")
            .args(["/C", "start", &url])
            .spawn()
            .map_err(|e| format!("Erreur ouverture URL: {}", e))?;
    }
    
    #[cfg(target_os = "linux")]
    {
        std::process::Command::new("xdg-open")
            .arg(&url)
            .spawn()
            .map_err(|e| format!("Erreur ouverture URL: {}", e))?;
    }
    
    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg(&url)
            .spawn()
            .map_err(|e| format!("Erreur ouverture URL: {}", e))?;
    }
    
    Ok(())
}

#[command]
async fn set_stealth_mode(
    mode: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation stricte du mode
    StringValidator::new()
        .required()
        .max_length(10)
        .allowed_chars("abcdefghijklmnopqrstuvwxyz")
        .validate("mode", &mode)
        .map_err(|e| format!("Validation mode: {}", e))?;
    
    let stealth_mode = match mode.as_str() {
        "normal" => StealthMode::Normal,
        "ghost" => StealthMode::Ghost,
        _ => return Err("Mode invalide. Modes acceptés: 'normal', 'ghost'".to_string()),
    };
    
    let mut current_mode = state.stealth_mode.lock().await;
    *current_mode = stealth_mode;
    
    // Émettre l'événement de changement de mode
    window.emit("stealth-mode-changed", &mode).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn get_stealth_mode(state: State<'_, AppState>) -> Result<String, String> {
    let mode = state.stealth_mode.lock().await;
    Ok(match *mode {
        StealthMode::Normal => "normal".to_string(),
        StealthMode::Ghost => "ghost".to_string(),
    })
}

#[command]
async fn connect_discord_bot(
    token: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<String, String> {
    // Mode test local - simule une connexion réussie
    if token == "test_token_mock_development_mode_voidbot" || std::env::var("VOIDBOT_TEST_MODE").unwrap_or_default() == "true" {
        tokio::time::sleep(tokio::time::Duration::from_millis(1500)).await; // Simule connexion
        window.emit("bot-connected", serde_json::json!({
            "status": "connected",
            "user": {
                "id": "123456789012345678",
                "username": "TestUser",
                "discriminator": "0001",
                "avatar": null
            }
        })).ok();
        return Ok("Connexion simulée réussie (mode test local)".to_string());
    }

    // Validation stricte de l'entrée
    if token.is_empty() {
        return Err("Token utilisateur Discord requis".to_string());
    }
    
    // Validation du format et sécurité du token
    if !is_valid_user_token(&token) {
        return Err("Format de token utilisateur invalide".to_string());
    }
    
    // Limite la longueur pour éviter les attaques DoS
    if token.len() > 1000 {
        return Err("Token trop long".to_string());
    }
    
    // Initialise le stockage sécurisé si nécessaire
    {
        let mut storage_guard = state.secure_storage.lock().await;
        if storage_guard.is_none() {
            match SecureStorage::new() {
                Ok(storage) => *storage_guard = Some(storage),
                Err(e) => return Err(format!("Erreur d'initialisation du stockage sécurisé: {}", e)),
            }
        }
    }
    
    // Tester la validité du token avec l'API Discord
    match validate_discord_token(&token).await {
        Ok(user_info) => {
            // Sauvegarde sécurisée du token
            {
                let storage_guard = state.secure_storage.lock().await;
                if let Some(storage) = storage_guard.as_ref() {
                    if let Err(e) = storage.save_token(&token).await {
                        return Err(format!("Erreur de sauvegarde sécurisée: {}", e));
                    }
                }
            }
            
            let mut connected = state.bot_connected.lock().await;
            *connected = true;
            
            // Émission sécurisée de l'événement (sans token)
            let safe_user_info = DiscordUser {
                id: user_info.id,
                username: sanitize_string(&user_info.username),
                discriminator: user_info.discriminator,
                avatar: user_info.avatar,
                email: None, // Ne jamais exposer l'email
            };
            
            window.emit("discord-connected", &safe_user_info).map_err(|e| e.to_string())?;
            
            Ok(format!("Selfbot connecté : {}", safe_user_info.username))
        }
        Err(e) => {
            Err(format!("Échec de l'authentification Discord : {}", e))
        }
    }
}

#[command]
async fn disconnect_discord_bot(
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut connected = state.bot_connected.lock().await;
    *connected = false;
    
    // Émettre l'événement de déconnexion
    window.emit("discord-disconnected", false).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn get_bot_status(state: State<'_, AppState>) -> Result<bool, String> {
    let connected = state.bot_connected.lock().await;
    Ok(*connected)
}

#[command]
async fn minimize_window(window: Window) -> Result<(), String> {
    window.minimize().map_err(|e| e.to_string())
}

#[command]
async fn maximize_window(window: Window) -> Result<(), String> {
    if window.is_maximized().unwrap_or(false) {
        window.unmaximize().map_err(|e| e.to_string())
    } else {
        window.maximize().map_err(|e| e.to_string())
    }
}

#[command]
async fn close_window(window: Window) -> Result<(), String> {
    window.close().map_err(|e| e.to_string())
}

#[command]
async fn open_discord_login_webview(
    app: tauri::AppHandle,
) -> Result<(), String> {
    let webview = WebviewWindowBuilder::new(
        &app,
        "discord-login",
        WebviewUrl::External("https://discord.com/login".parse().unwrap()),
    )
    .title("Connexion Discord - VoidBot")
    .inner_size(500.0, 700.0)
    .center()
    .resizable(false)
    .build()
    .map_err(|e| format!("Erreur création webview : {}", e))?;
    
    // Injecter le script de capture du token après le chargement de la page - OPTIMISÉ RACE CONDITIONS
    let script = r#"
        (function() {
            // Variables globales pour éviter les race conditions
            if (window.VoidBotTokenCapture) return; // Prévenir les double-injections
            window.VoidBotTokenCapture = {
                tokenCaptured: false,
                attempts: 0,
                maxAttempts: 300, // 5 minutes à 1 tentative/seconde
                interval: null,
                timeout: null
            };
            
            const capture = window.VoidBotTokenCapture;
            
            // Fonction pour capturer le token depuis localStorage - THREAD-SAFE
            function captureToken() {
                if (capture.tokenCaptured) return true; // Race condition protection
                
                capture.attempts++;
                
                try {
                    const token = localStorage.getItem('token');
                    if (token && token.length > 10) { // Validation basique
                        capture.tokenCaptured = true; // Atomic flag set
                        
                        // Nettoyer le token (retirer les guillemets)
                        const cleanToken = token.replace(/['"]/g, '');
                        
                        // Validation finale du token
                        if (cleanToken.length > 20) {
                            // Émettre l'événement avec le token capturé
                            window.__TAURI__.event.emit('discord-token-captured', { 
                                token: cleanToken,
                                timestamp: Date.now(),
                                attempts: capture.attempts
                            });
                            
                            // Cleanup immédiat
                            if (capture.interval) clearInterval(capture.interval);
                            if (capture.timeout) clearTimeout(capture.timeout);
                            return true;
                        } else {
                            capture.tokenCaptured = false; // Reset si token invalide
                        }
                    }
                } catch (error) {
                    console.error('VoidBot token capture error:', error);
                }
                
                // Arrêter après max tentatives
                if (capture.attempts >= capture.maxAttempts) {
                    window.__TAURI__.event.emit('discord-login-timeout', { 
                        message: 'Timeout - Token non trouvé après 5 minutes',
                        attempts: capture.attempts
                    });
                    
                    // Cleanup
                    if (capture.interval) clearInterval(capture.interval);
                    if (capture.timeout) clearTimeout(capture.timeout);
                    return true;
                }
                
                return false;
            }
            
            // Vérifier périodiquement si le token est disponible - ATOMIC
            capture.interval = setInterval(() => {
                if (!capture.tokenCaptured && captureToken()) {
                    clearInterval(capture.interval);
                    capture.interval = null;
                }
            }, 1000);
            
            // Sécurité: Nettoyer l'interval après timeout - MEMORY LEAK PROTECTION
            capture.timeout = setTimeout(() => {
                if (capture.interval) {
                    clearInterval(capture.interval);
                    capture.interval = null;
                }
                capture.timeout = null;
                
                // Émettre timeout si pas encore capturé
                if (!capture.tokenCaptured) {
                    window.__TAURI__.event.emit('discord-login-timeout', { 
                        message: 'Timeout général - 5 minutes écoulées',
                        attempts: capture.attempts
                    });
                }
            }, 5 * 60 * 1000);
            
            // Immédiate check pour tokens déjà présents
            setTimeout(() => captureToken(), 100);
        })();
    "#;
    
    // Attendre que la page soit chargée puis injecter le script
    webview.eval(script).map_err(|e| format!("Erreur injection script : {}", e))?;
    
    Ok(())
}

// Fonctions utilitaires pour l'authentification Discord

/// Valide un token Discord avec timeout et sécurité renforcée
async fn validate_discord_token(token: &str) -> Result<DiscordUser, String> {
    use std::time::Duration;
    
    // Mode test local - simule une validation réussie
    if token == "test_token_mock_development_mode_voidbot" || std::env::var("VOIDBOT_TEST_MODE").unwrap_or_default() == "true" {
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await; // Simule requête API
        
        return Ok(DiscordUser {
            id: "123456789012345678".to_string(),
            username: "TestUser".to_string(),
            discriminator: "0001".to_string(),
            avatar: None,
            email: None,
        });
    }
    
    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(10)) // Timeout de sécurité
        .build()
        .map_err(|e| format!("Erreur création client HTTP : {}", e))?;
    
    let response = client
        .get("https://discord.com/api/v10/users/@me")
        .header("Authorization", token)
        .header("User-Agent", "VoidBot/1.0") // User-Agent obligatoire
        .send()
        .await
        .map_err(|e| format!("Erreur réseau : {}", e))?;

    if response.status().is_success() {
        let user: DiscordUser = response
            .json()
            .await
            .map_err(|e| format!("Erreur parsing JSON : {}", e))?;
        
        // Validation supplémentaire des données reçues
        if user.id.is_empty() || user.username.is_empty() {
            return Err("Données utilisateur invalides reçues de Discord".to_string());
        }
        
        // Sanitise les données reçues
        let sanitized_user = DiscordUser {
            id: sanitize_string(&user.id),
            username: sanitize_string(&user.username),
            discriminator: user.discriminator,
            avatar: user.avatar,
            email: None, // Ne jamais exposer l'email
        };
        
        Ok(sanitized_user)
    } else {
        match response.status().as_u16() {
            401 => Err("Token invalide ou expiré".to_string()),
            403 => Err("Token non autorisé".to_string()),
            429 => Err("Trop de tentatives, réessayez plus tard".to_string()),
            _ => Err(format!("Erreur Discord API : {}", response.status())),
        }
    }
}

/// Sanitise une chaîne de caractères pour éviter les injections
fn sanitize_string(input: &str) -> String {
    input
        .chars()
        .filter(|c| c.is_alphanumeric() || " _-".contains(*c))
        .take(100) // Limite la longueur
        .collect()
}

// Commandes pour le Notification Center
#[command]
async fn get_notifications(state: State<'_, AppState>) -> Result<Vec<VoidNotification>, String> {
    let manager = state.notification_manager.lock().await;
    Ok(manager.notifications.clone())
}

#[command]
async fn get_notification_config(state: State<'_, AppState>) -> Result<NotificationConfig, String> {
    let manager = state.notification_manager.lock().await;
    Ok(manager.config.clone())
}

#[command]
async fn mark_notification_as_read(
    notification_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    // Validation de l'ID de notification
    StringValidator::new()
        .required()
        .max_length(100)
        .allowed_chars("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_")
        .validate("notification_id", &notification_id)
        .map_err(|e| format!("Validation notification_id: {}", e))?;
    
    let mut manager = state.notification_manager.lock().await;
    Ok(manager.mark_as_read(&notification_id))
}

#[command]
async fn mark_all_notifications_as_read(state: State<'_, AppState>) -> Result<(), String> {
    let mut manager = state.notification_manager.lock().await;
    manager.mark_all_as_read();
    Ok(())
}

#[command]
async fn delete_notification(
    notification_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    // Validation de l'ID de notification
    StringValidator::new()
        .required()
        .max_length(100)
        .allowed_chars("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_")
        .validate("notification_id", &notification_id)
        .map_err(|e| format!("Validation notification_id: {}", e))?;
    
    let mut manager = state.notification_manager.lock().await;
    Ok(manager.remove_notification(&notification_id))
}

#[command]
async fn clear_all_notifications(state: State<'_, AppState>) -> Result<(), String> {
    let mut manager = state.notification_manager.lock().await;
    manager.clear_all_notifications();
    Ok(())
}

#[command]
async fn update_notification_config(
    config: NotificationConfig,
    state: State<'_, AppState>,
) -> Result<(), String> {
    // Validation de la configuration
    config.validate()
        .map_err(|e| format!("Validation config: {}", e))?;
    
    let mut manager = state.notification_manager.lock().await;
    manager.config = config;
    Ok(())
}


#[command] 
async fn get_giveaway_config(state: State<'_, AppState>) -> Result<GiveawayConfig, String> {
    let manager = state.giveaway_manager.lock().await;
    Ok(manager.config.clone())
}

#[command]
async fn get_nitro_sniper_config(state: State<'_, AppState>) -> Result<NitroSniperConfig, String> {
    let manager = state.nitro_sniper_manager.lock().await;
    Ok(manager.config.clone())
}

#[command]
async fn get_activity_viewer_config(state: State<'_, AppState>) -> Result<ActivityViewerConfig, String> {
    let viewer = state.activity_viewer.lock().await;
    Ok(viewer.config.clone())
}

#[command]
async fn get_dynamic_variables_config(state: State<'_, AppState>) -> Result<DynamicVariableConfig, String> {
    let manager = state.dynamic_variables.lock().await;
    Ok(manager.config.clone())
}

#[command]
async fn get_unread_notifications_count(state: State<'_, AppState>) -> Result<usize, String> {
    let manager = state.notification_manager.lock().await;
    Ok(manager.get_unread_count())
}

// Commandes pour Auto-Commands
#[command]
async fn get_auto_commands_config(state: State<'_, AppState>) -> Result<AutoCommandsState, String> {
    let auto_commands = state.auto_commands_state.lock().await;
    Ok(auto_commands.clone())
}

#[command]
async fn update_auto_translate_config(
    config: AutoTranslateConfig,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la configuration de traduction
    config.validate()
        .map_err(|e| format!("Validation auto_translate config: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_translate = config;
    
    // Émettre événement de mise à jour
    window.emit("auto-commands-updated", "translate").map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn update_auto_slash_config(
    config: AutoSlashConfig,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la configuration slash
    config.validate()
        .map_err(|e| format!("Validation auto_slash config: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_slash = config;
    
    // Émettre événement de mise à jour
    window.emit("auto-commands-updated", "slash").map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn update_auto_reply_config(
    config: AutoReplyConfig,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la configuration auto-reply
    config.validate()
        .map_err(|e| format!("Validation auto_reply config: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_reply = config;
    
    // Émettre événement de mise à jour
    window.emit("auto-commands-updated", "reply").map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn add_slash_trigger(
    keyword: String,
    trigger: SlashTrigger,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation stricte des entrées
    StringValidator::new()
        .required()
        .min_length(1)
        .max_length(50)
        .forbidden_chars("\0\t\n\r<>\"'&")
        .validate("keyword", &keyword)
        .map_err(|e| format!("Validation keyword: {}", e))?;
    
    // Validation de la structure trigger
    trigger.validate()
        .map_err(|e| format!("Validation trigger: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    
    // Limite le nombre de triggers pour éviter l'abus
    if auto_commands.auto_slash.triggers.len() >= 100 {
        return Err("Limite de 100 triggers atteinte".to_string());
    }
    
    auto_commands.auto_slash.triggers.insert(keyword.clone(), trigger);
    
    // Émettre événement de mise à jour
    window.emit("slash-trigger-added", &keyword).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn remove_slash_trigger(
    keyword: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la clé
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("keyword", &keyword)
        .map_err(|e| format!("Validation keyword: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_slash.triggers.remove(&keyword);
    
    // Émettre événement de mise à jour
    window.emit("slash-trigger-removed", &keyword).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn add_auto_reply(
    keyword: String,
    reply: AutoReply,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation stricte des entrées
    StringValidator::new()
        .required()
        .min_length(1)
        .max_length(50)
        .forbidden_chars("\0\t\n\r<>\"'&")
        .validate("keyword", &keyword)
        .map_err(|e| format!("Validation keyword: {}", e))?;
    
    // Validation de la structure reply
    reply.validate()
        .map_err(|e| format!("Validation reply: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    
    // Limite le nombre de réponses automatiques
    if auto_commands.auto_reply.triggers.len() >= 50 {
        return Err("Limite de 50 réponses automatiques atteinte".to_string());
    }
    
    auto_commands.auto_reply.triggers.insert(keyword.clone(), reply);
    
    // Émettre événement de mise à jour
    window.emit("auto-reply-added", &keyword).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn remove_auto_reply(
    keyword: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la clé
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("keyword", &keyword)
        .map_err(|e| format!("Validation keyword: {}", e))?;
    
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_reply.triggers.remove(&keyword);
    
    // Émettre événement de mise à jour
    window.emit("auto-reply-removed", &keyword).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn toggle_auto_translate(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_translate.enabled = enabled;
    
    // Émettre événement de mise à jour
    window.emit("auto-translate-toggled", enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn toggle_auto_slash(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_slash.enabled = enabled;
    
    // Émettre événement de mise à jour
    window.emit("auto-slash-toggled", enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn toggle_auto_reply(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.auto_reply.enabled = enabled;
    
    // Émettre événement de mise à jour
    window.emit("auto-reply-toggled", enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn clear_auto_commands_cooldowns(state: State<'_, AppState>) -> Result<(), String> {
    let mut auto_commands = state.auto_commands_state.lock().await;
    auto_commands.slash_cooldowns.clear();
    auto_commands.reply_cooldowns.clear();
    Ok(())
}

// Commandes pour le Giveaway Joiner

#[command]
async fn update_giveaway_config(
    config: GiveawayConfig,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la configuration
    config.validate()
        .map_err(|e| format!("Validation giveaway config: {}", e))?;
    
    let mut manager = state.giveaway_manager.lock().await;
    manager.config = config;
    
    // Émettre événement de mise à jour
    window.emit("giveaway-config-updated", true).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn get_giveaway_stats(state: State<'_, AppState>) -> Result<GiveawayStats, String> {
    let manager = state.giveaway_manager.lock().await;
    Ok(manager.stats.clone())
}

#[command]
async fn get_detected_giveaways(state: State<'_, AppState>) -> Result<Vec<GiveawayInfo>, String> {
    let manager = state.giveaway_manager.lock().await;
    Ok(manager.detected_giveaways.clone())
}

#[command]
async fn get_active_giveaways(state: State<'_, AppState>) -> Result<Vec<GiveawayInfo>, String> {
    let manager = state.giveaway_manager.lock().await;
    let active = manager.get_active_giveaways()
        .into_iter()
        .cloned()
        .collect();
    Ok(active)
}

#[command]
async fn get_joined_giveaways(state: State<'_, AppState>) -> Result<Vec<GiveawayInfo>, String> {
    let manager = state.giveaway_manager.lock().await;
    let joined = manager.get_joined_giveaways()
        .into_iter()
        .cloned()
        .collect();
    Ok(joined)
}

#[command]
async fn mark_giveaway_as_joined(
    giveaway_id: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<bool, String> {
    // Validation de l'ID
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("giveaway_id", &giveaway_id)
        .map_err(|e| format!("Validation giveaway_id: {}", e))?;
    
    let mut manager = state.giveaway_manager.lock().await;
    let success = manager.mark_as_joined(&giveaway_id);
    
    if success {
        // Émettre événement de mise à jour
        window.emit("giveaway-joined", &giveaway_id).map_err(|e| e.to_string())?;
    }
    
    Ok(success)
}

#[command]
async fn mark_giveaway_as_skipped(
    giveaway_id: String,
    reason: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<bool, String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("giveaway_id", &giveaway_id)
        .map_err(|e| format!("Validation giveaway_id: {}", e))?;
    
    StringValidator::new()
        .max_length(500)
        .validate("reason", &reason)
        .map_err(|e| format!("Validation reason: {}", e))?;
    
    let mut manager = state.giveaway_manager.lock().await;
    let success = manager.mark_as_skipped(&giveaway_id, reason.clone());
    
    if success {
        // Émettre événement de mise à jour
        window.emit("giveaway-skipped", serde_json::json!({
            "giveaway_id": giveaway_id,
            "reason": reason
        })).map_err(|e| e.to_string())?;
    }
    
    Ok(success)
}

#[command]
async fn record_giveaway_win(
    giveaway_id: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'ID
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("giveaway_id", &giveaway_id)
        .map_err(|e| format!("Validation giveaway_id: {}", e))?;
    
    let mut manager = state.giveaway_manager.lock().await;
    manager.record_win(&giveaway_id);
    
    // Émettre événement de victoire
    window.emit("giveaway-won", &giveaway_id).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn cleanup_old_giveaways(
    max_age_days: u32,
    state: State<'_, AppState>,
) -> Result<(), String> {
    // Validation de l'âge maximum
    if max_age_days == 0 || max_age_days > 365 {
        return Err("L'âge maximum doit être entre 1 et 365 jours".to_string());
    }
    
    let mut manager = state.giveaway_manager.lock().await;
    manager.cleanup_old_giveaways(max_age_days as i64);
    
    Ok(())
}

#[command]
async fn toggle_giveaway_joiner(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut manager = state.giveaway_manager.lock().await;
    manager.config.enabled = enabled;
    
    // Émettre événement de changement d'état
    window.emit("giveaway-joiner-toggled", enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

// Commandes pour le Nitro Sniper

#[command]
async fn update_nitro_sniper_config(
    config: NitroSniperConfig,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de la configuration
    config.validate()
        .map_err(|e| format!("Validation nitro sniper config: {}", e))?;
    
    let mut manager = state.nitro_sniper_manager.lock().await;
    manager.config = config;
    
    // Émettre événement de mise à jour
    window.emit("nitro-sniper-config-updated", true).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn get_nitro_sniper_stats(state: State<'_, AppState>) -> Result<NitroSniperStats, String> {
    let manager = state.nitro_sniper_manager.lock().await;
    Ok(manager.stats.clone())
}

#[command]
async fn get_detected_nitro_codes(state: State<'_, AppState>) -> Result<Vec<NitroCodeInfo>, String> {
    let manager = state.nitro_sniper_manager.lock().await;
    Ok(manager.detected_codes.clone())
}

#[command]
async fn get_claimed_nitro_codes(state: State<'_, AppState>) -> Result<Vec<NitroCodeInfo>, String> {
    let manager = state.nitro_sniper_manager.lock().await;
    let claimed = manager.get_claimed_codes()
        .into_iter()
        .cloned()
        .collect();
    Ok(claimed)
}

#[command]
async fn get_recent_nitro_codes(
    hours: u32,
    state: State<'_, AppState>,
) -> Result<Vec<NitroCodeInfo>, String> {
    // Validation des heures
    if hours == 0 || hours > 168 { // Max 7 jours
        return Err("Les heures doivent être entre 1 et 168".to_string());
    }
    
    let manager = state.nitro_sniper_manager.lock().await;
    let recent = manager.get_recent_codes(hours as i64)
        .into_iter()
        .cloned()
        .collect();
    Ok(recent)
}

#[command]
async fn get_nitro_performance_stats(state: State<'_, AppState>) -> Result<std::collections::HashMap<String, f32>, String> {
    let manager = state.nitro_sniper_manager.lock().await;
    Ok(manager.get_performance_stats())
}

#[command]
async fn cleanup_old_nitro_codes(
    max_age_days: u32,
    state: State<'_, AppState>,
) -> Result<(), String> {
    // Validation de l'âge maximum
    if max_age_days == 0 || max_age_days > 365 {
        return Err("L'âge maximum doit être entre 1 et 365 jours".to_string());
    }
    
    let mut manager = state.nitro_sniper_manager.lock().await;
    manager.cleanup_old_codes(max_age_days as i64);
    
    Ok(())
}

#[command]
async fn toggle_nitro_sniper(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut manager = state.nitro_sniper_manager.lock().await;
    manager.config.enabled = enabled;
    
    // Émettre événement de changement d'état
    window.emit("nitro-sniper-toggled", enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn toggle_nitro_test_mode(
    test_mode: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut manager = state.nitro_sniper_manager.lock().await;
    manager.config.test_mode = test_mode;
    
    // Émettre événement de changement d'état
    window.emit("nitro-test-mode-toggled", test_mode).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn set_nitro_sniper_delays(
    min_delay_ms: u32,
    max_delay_ms: u32,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation des délais
    if min_delay_ms < 10 || min_delay_ms > 5000 {
        return Err("Le délai minimum doit être entre 10 et 5000ms".to_string());
    }
    
    if max_delay_ms < 10 || max_delay_ms > 5000 {
        return Err("Le délai maximum doit être entre 10 et 5000ms".to_string());
    }
    
    if min_delay_ms >= max_delay_ms {
        return Err("Le délai minimum doit être inférieur au maximum".to_string());
    }
    
    let mut manager = state.nitro_sniper_manager.lock().await;
    manager.config.min_delay_ms = min_delay_ms;
    manager.config.max_delay_ms = max_delay_ms;
    
    // Émettre événement de mise à jour
    window.emit("nitro-delays-updated", serde_json::json!({
        "min_delay_ms": min_delay_ms,
        "max_delay_ms": max_delay_ms
    })).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn add_trusted_bot(
    bot_id: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'ID de bot
    DiscordIdValidator::validate("bot_id", &bot_id)
        .map_err(|e| format!("Validation bot_id: {}", e))?;
    
    let bot_id_u64: u64 = bot_id.parse()
        .map_err(|_| "ID de bot invalide".to_string())?;
    
    let mut manager = state.nitro_sniper_manager.lock().await;
    
    if !manager.config.trusted_bot_ids.contains(&bot_id_u64) {
        manager.config.trusted_bot_ids.push(bot_id_u64);
        
        // Émettre événement de mise à jour
        window.emit("trusted-bot-added", &bot_id).map_err(|e| e.to_string())?;
    }
    
    Ok(())
}

#[command]
async fn remove_trusted_bot(
    bot_id: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'ID de bot
    DiscordIdValidator::validate("bot_id", &bot_id)
        .map_err(|e| format!("Validation bot_id: {}", e))?;
    
    let bot_id_u64: u64 = bot_id.parse()
        .map_err(|_| "ID de bot invalide".to_string())?;
    
    let mut manager = state.nitro_sniper_manager.lock().await;
    manager.config.trusted_bot_ids.retain(|&id| id != bot_id_u64);
    
    // Émettre événement de mise à jour
    window.emit("trusted-bot-removed", &bot_id).map_err(|e| e.to_string())?;
    
    Ok(())
}

// =================== COMMANDES IMAGES ===================

#[command]
async fn get_image_config(state: State<'_, AppState>) -> Result<ImageGenerationConfig, String> {
    let config = state.image_config.lock().await;
    Ok((*config).clone())
}

#[command]
async fn update_image_config(
    enabled: Option<bool>,
    width: Option<u32>,
    height: Option<u32>,
    font_size: Option<f32>,
    max_size_mb: Option<u32>,
    quality: Option<u8>,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut config = state.image_config.lock().await;
    
    if let Some(enabled) = enabled {
        config.enabled = enabled;
    }
    
    if let Some(width) = width {
        if width < 100 || width > 4000 {
            return Err("Largeur doit être entre 100 et 4000 pixels".to_string());
        }
        config.default_width = width;
    }
    
    if let Some(height) = height {
        if height < 100 || height > 4000 {
            return Err("Hauteur doit être entre 100 et 4000 pixels".to_string());
        }
        config.default_height = height;
    }
    
    if let Some(font_size) = font_size {
        if font_size < 8.0 || font_size > 200.0 {
            return Err("Taille de police doit être entre 8 et 200".to_string());
        }
        config.default_font_size = font_size;
    }
    
    if let Some(max_size_mb) = max_size_mb {
        if max_size_mb < 1 || max_size_mb > 25 {
            return Err("Taille maximale doit être entre 1 et 25 MB".to_string());
        }
        config.max_image_size_mb = max_size_mb;
    }
    
    if let Some(quality) = quality {
        if quality < 10 || quality > 100 {
            return Err("Qualité doit être entre 10 et 100".to_string());
        }
        config.compression_quality = quality;
    }
    
    // Émettre événement de mise à jour
    window.emit("image-config-updated", (*config).clone()).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn toggle_image_generation(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut config = state.image_config.lock().await;
    config.enabled = enabled;
    
    // Émettre événement de changement
    window.emit("image-generation-toggled", &enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn reset_image_config(
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut config = state.image_config.lock().await;
    *config = ImageGenerationConfig::default();
    
    // Émettre événement de reset
    window.emit("image-config-reset", (*config).clone()).map_err(|e| e.to_string())?;
    
    Ok(())
}

// =================== COMMANDES RATE LIMITER ===================

#[command]
async fn get_rate_limit_config(state: State<'_, AppState>) -> Result<RateLimitConfig, String> {
    let config = state.rate_limiter.get_config().await
        .map_err(|e| format!("Erreur lors de la récupération de la config: {}", e))?;
    Ok(config)
}

#[command]
async fn update_rate_limit_config(
    enabled: Option<bool>,
    global_rate_per_second: Option<u32>,
    min_delay_between_requests: Option<u64>,
    warning_threshold: Option<f32>,
    debug_mode: Option<bool>,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut config = state.rate_limiter.get_config().await
        .map_err(|e| format!("Erreur lors de la récupération de la config: {}", e))?;
    
    if let Some(enabled) = enabled {
        config.enabled = enabled;
    }
    
    if let Some(rate) = global_rate_per_second {
        if rate < 1 || rate > 100 {
            return Err("Le taux global doit être entre 1 et 100 requêtes/seconde".to_string());
        }
        config.global_rate_per_second = rate;
    }
    
    if let Some(delay) = min_delay_between_requests {
        if delay < 50 || delay > 5000 {
            return Err("Le délai minimum doit être entre 50 et 5000ms".to_string());
        }
        config.min_delay_between_requests = delay;
    }
    
    if let Some(threshold) = warning_threshold {
        if threshold < 0.5 || threshold > 0.95 {
            return Err("Le seuil d'alerte doit être entre 0.5 et 0.95".to_string());
        }
        config.warning_threshold = threshold;
    }
    
    if let Some(debug) = debug_mode {
        config.debug_mode = debug;
    }
    
    state.rate_limiter.update_config(config.clone()).await;
    
    // Émettre événement de mise à jour
    window.emit("rate-limit-config-updated", &config).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn get_rate_limit_stats(state: State<'_, AppState>) -> Result<RateLimitStats, String> {
    let stats = state.rate_limiter.get_stats().await;
    Ok(stats)
}

#[command]
async fn reset_rate_limit_bucket(
    endpoint: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'endpoint
    StringValidator::new()
        .required()
        .max_length(100)
        .forbidden_chars("<>\"'")
        .validate("endpoint", &endpoint)
        .map_err(|e| format!("Validation endpoint: {}", e))?;
    
    state.rate_limiter.reset_bucket(&endpoint).await;
    
    // Émettre événement
    window.emit("rate-limit-bucket-reset", &endpoint).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn reset_all_rate_limit_buckets(
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    state.rate_limiter.reset_all_buckets().await;
    
    // Émettre événement
    window.emit("rate-limit-all-buckets-reset", ()).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
async fn toggle_rate_limiter(
    enabled: bool,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let mut config = state.rate_limiter.get_config().await
        .map_err(|e| format!("Erreur lors de la récupération de la config: {}", e))?;
    
    config.enabled = enabled;
    state.rate_limiter.update_config(config).await;
    
    // Émettre événement
    window.emit("rate-limiter-toggled", &enabled).map_err(|e| e.to_string())?;
    
    Ok(())
}

// =================== COMMANDES BASE DE DONNÉES ===================

#[command]
async fn get_database_stats(state: State<'_, AppState>) -> Result<DatabaseStats, String> {
    let stats = state.database.get_database_stats().await
        .map_err(|e| format!("Erreur lors de la récupération des stats DB: {}", e))?;
    Ok(stats)
}

#[command]
async fn get_usage_stats(
    feature: Option<String>,
    state: State<'_, AppState>
) -> Result<Vec<UsageStat>, String> {
    let stats = state.database.get_usage_stats(feature.as_deref()).await
        .map_err(|e| format!("Erreur lors de la récupération des stats d'usage: {}", e))?;
    Ok(stats)
}

#[command]
async fn get_recent_logs(
    limit: i32,
    severity_filter: Option<String>,
    state: State<'_, AppState>
) -> Result<Vec<EventLog>, String> {
    let severity = severity_filter.as_deref().map(|s| match s {
        "DEBUG" => EventSeverity::Debug,
        "INFO" => EventSeverity::Info,
        "WARNING" => EventSeverity::Warning,
        "ERROR" => EventSeverity::Error,
        "CRITICAL" => EventSeverity::Critical,
        _ => EventSeverity::Info,
    });
    
    let logs = state.database.get_recent_logs(limit, severity).await
        .map_err(|e| format!("Erreur lors de la récupération des logs: {}", e))?;
    Ok(logs)
}

#[command]
async fn get_command_history(
    limit: i32,
    user_id: Option<String>,
    state: State<'_, AppState>
) -> Result<Vec<CommandLog>, String> {
    let history = state.database.get_command_history(limit, user_id.as_deref()).await
        .map_err(|e| format!("Erreur lors de la récupération de l'historique: {}", e))?;
    Ok(history)
}

#[command]
async fn log_custom_event(
    event_type: String,
    severity: String,
    message: String,
    context: Option<String>,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(50)
        .forbidden_chars("<>\"'")
        .validate("event_type", &event_type)
        .map_err(|e| format!("Validation event_type: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(1000)
        .validate("message", &message)
        .map_err(|e| format!("Validation message: {}", e))?;
    
    let severity_enum = match severity.as_str() {
        "DEBUG" => EventSeverity::Debug,
        "INFO" => EventSeverity::Info,
        "WARNING" => EventSeverity::Warning,
        "ERROR" => EventSeverity::Error,
        "CRITICAL" => EventSeverity::Critical,
        _ => return Err("Severity invalide".to_string()),
    };
    
    state.database.log_event(&event_type, severity_enum, &message, context.as_deref()).await
        .map_err(|e| format!("Erreur lors du log: {}", e))?;
    
    Ok(())
}

#[command]
async fn increment_feature_usage(
    feature: String,
    action: String,
    metadata: Option<String>,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(50)
        .forbidden_chars("<>\"'")
        .validate("feature", &feature)
        .map_err(|e| format!("Validation feature: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(50)
        .forbidden_chars("<>\"'")
        .validate("action", &action)
        .map_err(|e| format!("Validation action: {}", e))?;
    
    state.database.increment_usage_stat(&feature, &action, metadata.as_deref()).await
        .map_err(|e| format!("Erreur lors de l'incrémentation: {}", e))?;
    
    Ok(())
}

#[command]
async fn save_app_config(
    category: String,
    key: String,
    value: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(50)
        .forbidden_chars("<>\"'")
        .validate("category", &category)
        .map_err(|e| format!("Validation category: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(100)
        .forbidden_chars("<>\"'")
        .validate("key", &key)
        .map_err(|e| format!("Validation key: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(10000)
        .validate("value", &value)
        .map_err(|e| format!("Validation value: {}", e))?;
    
    state.database.save_config(&category, &key, &value).await
        .map_err(|e| format!("Erreur lors de la sauvegarde: {}", e))?;
    
    Ok(())
}

#[command]
async fn get_app_config(
    category: String,
    key: String,
    state: State<'_, AppState>
) -> Result<Option<String>, String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(50)
        .forbidden_chars("<>\"'")
        .validate("category", &category)
        .map_err(|e| format!("Validation category: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(100)
        .forbidden_chars("<>\"'")
        .validate("key", &key)
        .map_err(|e| format!("Validation key: {}", e))?;
    
    let config = state.database.get_config(&category, &key).await
        .map_err(|e| format!("Erreur lors de la récupération: {}", e))?;
    
    Ok(config)
}

#[command]
async fn get_category_configs(
    category: String,
    state: State<'_, AppState>
) -> Result<Vec<(String, String)>, String> {
    // Validation du paramètre
    StringValidator::new()
        .required()
        .max_length(50)
        .forbidden_chars("<>\"'")
        .validate("category", &category)
        .map_err(|e| format!("Validation category: {}", e))?;
    
    let configs = state.database.get_category_configs(&category).await
        .map_err(|e| format!("Erreur lors de la récupération: {}", e))?;
    
    Ok(configs)
}

#[command]
async fn cleanup_old_database_data(
    days_to_keep: i32,
    state: State<'_, AppState>
) -> Result<(), String> {
    if days_to_keep < 1 || days_to_keep > 365 {
        return Err("Le nombre de jours doit être entre 1 et 365".to_string());
    }
    
    state.database.cleanup_old_data(days_to_keep).await
        .map_err(|e| format!("Erreur lors du nettoyage: {}", e))?;
    
    Ok(())
}

// ============= COMMANDES ACTIVITY VIEWER =============


#[command]
async fn update_activity_viewer_config(
    config: ActivityViewerConfig,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut activity_viewer = state.activity_viewer.lock().await;
    activity_viewer.config = config;
    Ok(())
}

#[command]
async fn get_tracked_users(
    state: State<'_, AppState>
) -> Result<Vec<TrackedUser>, String> {
    let activity_viewer = state.activity_viewer.lock().await;
    Ok(activity_viewer.tracked_users.values().cloned().collect())
}

#[command]
async fn add_tracked_user(
    user_id: String,
    username: String,
    discriminator: String,
    avatar: Option<String>,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des entrées
    StringValidator::new()
        .required()
        .max_length(20)
        .validate("user_id", &user_id)
        .map_err(|e| format!("Validation user_id: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(32)
        .validate("username", &username)
        .map_err(|e| format!("Validation username: {}", e))?;
    
    let mut activity_viewer = state.activity_viewer.lock().await;
    activity_viewer.add_tracked_user(user_id, username, discriminator, avatar)?;
    
    Ok(())
}

#[command]
async fn remove_tracked_user(
    user_id: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(20)
        .validate("user_id", &user_id)
        .map_err(|e| format!("Validation user_id: {}", e))?;
    
    let mut activity_viewer = state.activity_viewer.lock().await;
    activity_viewer.remove_tracked_user(&user_id)?;
    
    Ok(())
}

#[command]
async fn get_activity_stats(
    state: State<'_, AppState>
) -> Result<ActivityStats, String> {
    let activity_viewer = state.activity_viewer.lock().await;
    Ok(activity_viewer.stats.clone())
}

#[command]
async fn get_user_activity_history(
    user_id: String,
    limit: Option<usize>,
    state: State<'_, AppState>
) -> Result<Vec<ActivityEvent>, String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(20)
        .validate("user_id", &user_id)
        .map_err(|e| format!("Validation user_id: {}", e))?;
    
    let activity_viewer = state.activity_viewer.lock().await;
    let events = activity_viewer.get_user_activity_history(&user_id, limit);
    Ok(events.into_iter().cloned().collect())
}

#[command]
async fn get_online_users(
    state: State<'_, AppState>
) -> Result<Vec<TrackedUser>, String> {
    let activity_viewer = state.activity_viewer.lock().await;
    let users = activity_viewer.get_online_users();
    Ok(users.into_iter().cloned().collect())
}

#[command]
async fn get_users_with_activity(
    state: State<'_, AppState>
) -> Result<Vec<TrackedUser>, String> {
    let activity_viewer = state.activity_viewer.lock().await;
    let users = activity_viewer.get_users_with_activity();
    Ok(users.into_iter().cloned().collect())
}

#[command]
async fn cleanup_inactive_tracked_users(
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut activity_viewer = state.activity_viewer.lock().await;
    activity_viewer.cleanup_inactive_users();
    Ok(())
}

#[command]
async fn export_activity_data(
    user_id: Option<String>,
    state: State<'_, AppState>
) -> Result<String, String> {
    let activity_viewer = state.activity_viewer.lock().await;
    activity_viewer.export_activity_data(user_id.as_deref())
}

#[command]
async fn toggle_activity_viewer(
    enabled: bool,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut activity_viewer = state.activity_viewer.lock().await;
    activity_viewer.config.enabled = enabled;
    Ok(())
}

// ============= COMMANDES TROLL CONTROL =============

#[command]
async fn get_active_trolls(
    state: State<'_, AppState>
) -> Result<Vec<TrollSession>, String> {
    let trolls = state.troll_sessions.lock().await;
    Ok(trolls.values().cloned().collect())
}

#[command]
async fn start_ghostping_troll(
    target_user_id: String,
    message: Option<String>,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'ID utilisateur
    StringValidator::new()
        .required()
        .max_length(20)
        .validate("target_user_id", &target_user_id)
        .map_err(|e| format!("Validation target_user_id: {}", e))?;

    let troll_id = format!("ghostping_{}", chrono::Utc::now().timestamp());
    let config = TrollConfig {
        target_user: Some(target_user_id.clone()),
        message_content: message,
        delay_ms: 2000,
        max_actions: 10,
        auto_stop_minutes: Some(30),
    };

    let session = TrollSession {
        id: troll_id.clone(),
        troll_type: "GhostPing".to_string(),
        target_user: Some(target_user_id),
        guild_id: None,
        channel_id: None,
        started_at: chrono::Utc::now().to_rfc3339(),
        config,
    };

    {
        let mut trolls = state.troll_sessions.lock().await;
        trolls.insert(troll_id.clone(), session);
    }

    // Émettre événement de démarrage
    window.emit("troll-started", &troll_id).map_err(|e| e.to_string())?;

    Ok(())
}

#[command]
async fn start_spam_troll(
    message: String,
    count: u32,
    delay_seconds: u64,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(500)
        .validate("message", &message)
        .map_err(|e| format!("Validation message: {}", e))?;

    let safe_count = std::cmp::min(count, 20);
    let safe_delay = std::cmp::max(delay_seconds, 3);

    let troll_id = format!("spam_{}", chrono::Utc::now().timestamp());
    let config = TrollConfig {
        target_user: None,
        message_content: Some(message),
        delay_ms: safe_delay * 1000,
        max_actions: safe_count as i32,
        auto_stop_minutes: Some(10),
    };

    let session = TrollSession {
        id: troll_id.clone(),
        troll_type: "Spam".to_string(),
        target_user: None,
        guild_id: None,
        channel_id: None,
        started_at: chrono::Utc::now().to_rfc3339(),
        config,
    };

    {
        let mut trolls = state.troll_sessions.lock().await;
        trolls.insert(troll_id.clone(), session);
    }

    // Émettre événement de démarrage
    window.emit("troll-started", &troll_id).map_err(|e| e.to_string())?;

    Ok(())
}

#[command]
async fn start_fake_typing_troll(
    duration_minutes: u64,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let safe_duration = std::cmp::min(duration_minutes, 30);

    let troll_id = format!("typing_{}", chrono::Utc::now().timestamp());
    let config = TrollConfig {
        target_user: None,
        message_content: None,
        delay_ms: 8000,
        max_actions: (safe_duration * 60 / 8) as i32,
        auto_stop_minutes: Some(safe_duration),
    };

    let session = TrollSession {
        id: troll_id.clone(),
        troll_type: "FakeTyping".to_string(),
        target_user: None,
        guild_id: None,
        channel_id: None,
        started_at: chrono::Utc::now().to_rfc3339(),
        config,
    };

    {
        let mut trolls = state.troll_sessions.lock().await;
        trolls.insert(troll_id.clone(), session);
    }

    // Émettre événement de démarrage
    window.emit("troll-started", &troll_id).map_err(|e| e.to_string())?;

    Ok(())
}

#[command]
async fn start_noleave_troll(
    target_user_id: Option<String>,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'ID utilisateur si fourni
    if let Some(ref user_id) = target_user_id {
        StringValidator::new()
            .required()
            .max_length(20)
            .validate("target_user_id", user_id)
            .map_err(|e| format!("Validation target_user_id: {}", e))?;
    }

    let troll_id = format!("noleave_{}", chrono::Utc::now().timestamp());
    let config = TrollConfig {
        target_user: target_user_id.clone(),
        message_content: None,
        delay_ms: 1000,
        max_actions: -1,
        auto_stop_minutes: Some(60),
    };

    let session = TrollSession {
        id: troll_id.clone(),
        troll_type: "NoLeave".to_string(),
        target_user: target_user_id,
        guild_id: None,
        channel_id: None,
        started_at: chrono::Utc::now().to_rfc3339(),
        config,
    };

    {
        let mut trolls = state.troll_sessions.lock().await;
        trolls.insert(troll_id.clone(), session);
    }

    // Émettre événement de démarrage
    window.emit("troll-started", &troll_id).map_err(|e| e.to_string())?;

    Ok(())
}

#[command]
async fn stop_troll(
    troll_id: String,
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    // Validation de l'ID
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("troll_id", &troll_id)
        .map_err(|e| format!("Validation troll_id: {}", e))?;

    {
        let mut trolls = state.troll_sessions.lock().await;
        trolls.remove(&troll_id);
    }

    // Émettre événement d'arrêt
    window.emit("troll-stopped", &troll_id).map_err(|e| e.to_string())?;

    Ok(())
}

#[command]
async fn stop_all_trolls(
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let stopped_count = {
        let mut trolls = state.troll_sessions.lock().await;
        let count = trolls.len();
        trolls.clear();
        count
    };

    // Émettre événement d'arrêt total
    window.emit("all-trolls-stopped", stopped_count).map_err(|e| e.to_string())?;

    Ok(())
}

// ============= COMMANDES SYSTÈME & DISCORD MONITORING =============

#[command]
async fn enable_auto_startup(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle<tauri::Wry>,
) -> Result<(), String> {
    let app_name = "VoidBot";
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("Erreur récupération chemin exe: {}", e))?;
    
    let auto_launch = AutoLaunchBuilder::new()
        .set_app_name(app_name)
        .set_app_path(&exe_path.to_string_lossy())
        .set_use_launch_agent(true)
        .build()
        .map_err(|e| format!("Erreur création auto-launch: {}", e))?;
    
    auto_launch.enable()
        .map_err(|e| format!("Erreur activation auto-startup: {}", e))?;
    
    // Sauvegarder la référence dans l'état
    {
        let mut launcher = state.auto_launcher.lock().await;
        *launcher = Some(auto_launch);
    }
    
    Ok(())
}

#[command]
async fn disable_auto_startup(
    state: State<'_, AppState>,
) -> Result<(), String> {
    let launcher_guard = state.auto_launcher.lock().await;
    
    if let Some(auto_launch) = launcher_guard.as_ref() {
        auto_launch.disable()
            .map_err(|e| format!("Erreur désactivation auto-startup: {}", e))?;
    }
    
    Ok(())
}

#[command]
async fn get_auto_startup_status(
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let launcher_guard = state.auto_launcher.lock().await;
    
    if let Some(auto_launch) = launcher_guard.as_ref() {
        auto_launch.is_enabled()
            .map_err(|e| format!("Erreur vérification auto-startup: {}", e))
    } else {
        Ok(false)
    }
}

#[command]
async fn scan_discord_processes(
    state: State<'_, AppState>,
) -> Result<Vec<DiscordProcess>, String> {
    let mut system = state.system_monitor.lock().await;
    system.refresh_processes();
    
    let mut discord_processes = Vec::new();
    
    // Noms des processus Discord à rechercher
    let discord_names = [
        "Discord.exe", 
        "DiscordPTB.exe", 
        "DiscordCanary.exe",
        "discord",
        "discord-ptb", 
        "discord-canary",
        "Discord",
        "Discord PTB",
        "Discord Canary"
    ];
    
    for (pid, process) in system.processes() {
        let process_name = process.name();
        
        if discord_names.iter().any(|&name| {
            process_name.to_lowercase().contains(&name.to_lowercase()) ||
            name.to_lowercase().contains(&process_name.to_lowercase())
        }) {
            discord_processes.push(DiscordProcess {
                pid: pid.as_u32(),
                name: process_name.to_string(),
                exe_path: process.exe().map(|p| p.to_string_lossy().to_string()),
                memory_usage: process.memory(),
                cpu_usage: process.cpu_usage(),
                start_time: process.start_time(),
            });
        }
    }
    
    // Sauvegarder les processus trouvés
    {
        let mut processes = state.discord_processes.lock().await;
        *processes = discord_processes.clone();
    }
    
    Ok(discord_processes)
}

#[command]
async fn get_discord_processes(
    state: State<'_, AppState>,
) -> Result<Vec<DiscordProcess>, String> {
    let processes = state.discord_processes.lock().await;
    Ok(processes.clone())
}

#[command]
async fn is_discord_running(
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let processes = scan_discord_processes(state).await?;
    Ok(!processes.is_empty())
}

#[command]
async fn start_discord_monitoring(
    state: State<'_, AppState>,
    window: Window<tauri::Wry>,
) -> Result<(), String> {
    let app_state = state.inner().clone();
    let monitoring_task = start_discord_monitoring_task(app_state, window);
    
    // Spawner la tâche
    tokio::spawn(monitoring_task);
    
    Ok(())
}

async fn start_discord_monitoring_task(
    state: AppState,
    window: Window<tauri::Wry>,
) {
    let mut last_discord_state = false;
    
    loop {
        // Scanner les processus Discord toutes les 5 secondes
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        
        if let Ok(processes) = scan_discord_processes_impl(&state).await {
            let discord_running = !processes.is_empty();
            
            // Émettre un événement si l'état Discord change
            if discord_running != last_discord_state {
                let event_name = if discord_running {
                    "discord-detected"
                } else {
                    "discord-lost"
                };
                
                if let Err(e) = window.emit(event_name, &processes) {
                    eprintln!("Erreur émission événement Discord: {}", e);
                }
                
                last_discord_state = discord_running;
            }
        }
    }
}

// Overlay functions removed - replaced with integrated context menu system
// No more overlay window creation to prevent crashes

#[command]
async fn start_mouse_listener(
    app_handle: tauri::AppHandle<tauri::Wry>,
    state: State<'_, AppState>,
) -> Result<(), String> {
    if RIGHT_CLICK_LISTENER.load(Ordering::Relaxed) {
        return Ok(()); // Déjà actif
    }

    let app_handle_clone = app_handle.clone();
    let state_clone = state.inner().clone();
    
    std::thread::spawn(move || {
        RIGHT_CLICK_LISTENER.store(true, Ordering::Relaxed);
        
        if let Err(error) = listen(move |event| {
            if !RIGHT_CLICK_LISTENER.load(Ordering::Relaxed) {
                return; // Arrêter l'écoute
            }
            
            match event.event_type {
                EventType::ButtonPress(Button::Right) => {
                    // Clic droit détecté
                    let mouse_pos = MousePosition {
                        x: 0.0, // TODO: Get actual coordinates from event
                        y: 0.0, // TODO: Get actual coordinates from event
                    };
                    
                    // Vérifier si Discord est la fenêtre active
                    let discord_active = is_discord_window_active();
                    
                    if discord_active {
                        let context_data = ContextMenuData {
                            mouse_position: mouse_pos.clone(),
                            discord_window_active: true,
                            timestamp: chrono::Utc::now().timestamp() as u64,
                        };
                        
                        // Émettre événement vers le frontend
                        if let Some(main_window) = app_handle_clone.get_webview_window("main") {
                            let _ = main_window.emit("context-menu-requested", &context_data);
                        }
                        
                        // Sauvegarder la position de la souris
                        if let Ok(mut pos) = state_clone.last_mouse_position.try_lock() {
                            *pos = mouse_pos;
                        }
                    }
                }
                _ => {}
            }
        }) {
            eprintln!("Erreur listener souris: {:?}", error);
        }
    });
    
    Ok(())
}

#[command]
async fn stop_mouse_listener() -> Result<(), String> {
    RIGHT_CLICK_LISTENER.store(false, Ordering::Relaxed);
    Ok(())
}

// Context menu functionality moved to React frontend for better stability
// Functions removed to prevent window creation conflicts

// Nouvelle fonction pour traduire du texte
#[command]
async fn translate_text(
    text: String,
    target_lang: String,
) -> Result<String, String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(2000)
        .validate("text", &text)
        .map_err(|e| format!("Validation text: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(10)
        .validate("target_lang", &target_lang)
        .map_err(|e| format!("Validation target_lang: {}", e))?;
    
    // Pour l'instant, utiliser un service de traduction simple
    // Dans une vraie implémentation, on utiliserait Google Translate API ou DeepL
    
    // Simuler la traduction pour l'instant
    let translated = match target_lang.as_str() {
        "fr" => format!("[FR] {}", text),
        "en" => format!("[EN] {}", text),
        "es" => format!("[ES] {}", text),
        "de" => format!("[DE] {}", text),
        _ => format!("[{}] {}", target_lang.to_uppercase(), text),
    };
    
    Ok(translated)
}

// Fonction pour extraire les emojis d'un texte
#[command]
async fn extract_emojis(
    text: String,
) -> Result<Vec<String>, String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(5000)
        .validate("text", &text)
        .map_err(|e| format!("Validation text: {}", e))?;
    
    let mut emojis = Vec::new();
    
    // Regex pour les emojis Discord personnalisés
    let custom_emoji_regex = regex::Regex::new(r"<:(.*?):(\d+)>").unwrap();
    for cap in custom_emoji_regex.captures_iter(&text) {
        let emoji_name = cap.get(1).unwrap().as_str();
        let emoji_id = cap.get(2).unwrap().as_str();
        emojis.push(format!("{}:{}", emoji_name, emoji_id));
    }
    
    // Regex pour les emojis Unicode (basique)
    let unicode_emoji_regex = regex::Regex::new(r"[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]").unwrap();
    for mat in unicode_emoji_regex.find_iter(&text) {
        emojis.push(mat.as_str().to_string());
    }
    
    Ok(emojis)
}

#[command]
async fn get_overlay_config(
    state: State<'_, AppState>,
) -> Result<OverlayConfig, String> {
    let config = state.overlay_config.lock().await;
    Ok((*config).clone())
}

#[command]
async fn update_overlay_config(
    config: OverlayConfig,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut current_config = state.overlay_config.lock().await;
    *current_config = config;
    Ok(())
}

// Fonction utilitaire pour vérifier si Discord est la fenêtre active
fn is_discord_window_active() -> bool {
    // Cette fonction dépend de la plateforme
    // Pour une implémentation basique, on peut supposer true pour l'instant
    // Dans une vraie implémentation, on utiliserait les APIs système pour
    // vérifier quelle fenêtre est active
    true
}

// ============= COMMANDES DYNAMIC VARIABLES =============

#[command]
async fn update_dynamic_variables_config(
    config: DynamicVariableConfig,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut manager = state.dynamic_variables.lock().await;
    manager.config = config;
    Ok(())
}

#[command]
async fn get_available_variables(
    state: State<'_, AppState>
) -> Result<Vec<String>, String> {
    let manager = state.dynamic_variables.lock().await;
    Ok(manager.get_available_variables())
}

#[command]
async fn resolve_variable(
    name: String,
    state: State<'_, AppState>
) -> Result<String, String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("name", &name)
        .map_err(|e| format!("Validation name: {}", e))?;
    
    let mut manager = state.dynamic_variables.lock().await;
    manager.resolve_variable(&name).await
        .map_err(|e| format!("Erreur résolution: {}", e))
}

#[command]
async fn resolve_text(
    text: String,
    state: State<'_, AppState>
) -> Result<String, String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(1000)
        .validate("text", &text)
        .map_err(|e| format!("Validation text: {}", e))?;
    
    let mut manager = state.dynamic_variables.lock().await;
    Ok(manager.resolve_text(&text).await)
}

#[command]
async fn add_custom_variable(
    name: String,
    value: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("name", &name)
        .map_err(|e| format!("Validation name: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(500)
        .validate("value", &value)
        .map_err(|e| format!("Validation value: {}", e))?;
    
    let mut manager = state.dynamic_variables.lock().await;
    manager.add_custom_variable(name, value)
        .map_err(|e| format!("Erreur ajout: {}", e))
}

#[command]
async fn remove_custom_variable(
    name: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("name", &name)
        .map_err(|e| format!("Validation name: {}", e))?;
    
    let mut manager = state.dynamic_variables.lock().await;
    manager.remove_custom_variable(&name);
    Ok(())
}

#[command]
async fn get_cached_variables(
    state: State<'_, AppState>
) -> Result<Vec<CachedVariable>, String> {
    let manager = state.dynamic_variables.lock().await;
    Ok(manager.cache.values().cloned().collect())
}

#[command]
async fn refresh_all_variables(
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut manager = state.dynamic_variables.lock().await;
    manager.refresh_all().await
        .map_err(|e| format!("Erreur refresh: {}", e))
}

#[command]
async fn cleanup_variables_cache(
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut manager = state.dynamic_variables.lock().await;
    manager.cleanup_cache();
    Ok(())
}

#[command]
async fn toggle_dynamic_variables(
    enabled: bool,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut manager = state.dynamic_variables.lock().await;
    manager.config.enabled = enabled;
    Ok(())
}

#[command]
async fn save_configuration(
    category: String,
    key: String,
    value: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("category", &category)
        .map_err(|e| format!("Validation category: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(200)
        .validate("key", &key)
        .map_err(|e| format!("Validation key: {}", e))?;
    
    StringValidator::new()
        .max_length(10000)
        .validate("value", &value)
        .map_err(|e| format!("Validation value: {}", e))?;

    let database = &state.database;
    
    // Gestion d'erreur robuste pour la base de données
    match database.save_config(&category, &key, &value).await {
        Ok(_) => Ok(()),
        Err(e) => {
            eprintln!("Erreur base de données (save_config): {}", e);
            // En mode développement, on ne fait pas échouer l'application
            if cfg!(debug_assertions) {
                eprintln!("⚠️  Mode développement : Erreur base de données ignorée");
                Ok(())
            } else {
                Err(format!("Erreur sauvegarde config: {}", e))
            }
        }
    }
}

#[command]
async fn get_configuration(
    category: String,
    key: String,
    state: State<'_, AppState>
) -> Result<Option<String>, String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("category", &category)
        .map_err(|e| format!("Validation category: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(200)
        .validate("key", &key)
        .map_err(|e| format!("Validation key: {}", e))?;

    let database = &state.database;
    
    // Gestion d'erreur robuste pour la base de données
    match database.get_config(&category, &key).await {
        Ok(result) => Ok(result),
        Err(e) => {
            eprintln!("Erreur base de données (get_config): {}", e);
            // En mode développement, retourner None au lieu de faire échouer
            if cfg!(debug_assertions) {
                eprintln!("⚠️  Mode développement : Erreur base de données ignorée, retour None");
                Ok(None)
            } else {
                Err(format!("Erreur récupération config: {}", e))
            }
        }
    }
}

#[command]
async fn get_category_configurations(
    category: String,
    state: State<'_, AppState>
) -> Result<Vec<(String, String)>, String> {
    // Validation du paramètre
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("category", &category)
        .map_err(|e| format!("Validation category: {}", e))?;

    let database = &state.database;
    database.get_category_configs(&category)
        .await
        .map_err(|e| format!("Erreur récupération configs catégorie: {}", e))
}

#[command]
async fn get_usage_statistics(
    feature: Option<String>,
    state: State<'_, AppState>
) -> Result<Vec<UsageStat>, String> {
    let database = &state.database;
    let feature_ref = feature.as_deref();
    database.get_usage_stats(feature_ref)
        .await
        .map_err(|e| format!("Erreur récupération stats usage: {}", e))
}

#[command]
async fn get_recent_event_logs(
    limit: i32,
    severity_filter: Option<String>,
    state: State<'_, AppState>
) -> Result<Vec<EventLog>, String> {
    // Validation du limit
    if limit < 1 || limit > 1000 {
        return Err("Limite doit être entre 1 et 1000".to_string());
    }

    let severity = severity_filter.as_deref().map(|s| match s {
        "DEBUG" => EventSeverity::Debug,
        "INFO" => EventSeverity::Info,
        "WARNING" => EventSeverity::Warning,
        "ERROR" => EventSeverity::Error,
        "CRITICAL" => EventSeverity::Critical,
        _ => EventSeverity::Info,
    });

    let database = &state.database;
    database.get_recent_logs(limit, severity)
        .await
        .map_err(|e| format!("Erreur récupération logs: {}", e))
}


#[command]
async fn cleanup_database(
    days_to_keep: i32,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    if days_to_keep < 1 || days_to_keep > 365 {
        return Err("Jours à conserver doit être entre 1 et 365".to_string());
    }

    let database = &state.database;
    database.cleanup_old_data(days_to_keep)
        .await
        .map_err(|e| format!("Erreur nettoyage DB: {}", e))
}

#[command]
async fn log_event_manual(
    event_type: String,
    severity: String,
    message: String,
    context: Option<String>,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("event_type", &event_type)
        .map_err(|e| format!("Validation event_type: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(1000)
        .validate("message", &message)
        .map_err(|e| format!("Validation message: {}", e))?;
    
    if let Some(ref ctx) = context {
        StringValidator::new()
            .max_length(5000)
            .validate("context", ctx)
            .map_err(|e| format!("Validation context: {}", e))?;
    }

    let severity_enum = match severity.as_str() {
        "DEBUG" => EventSeverity::Debug,
        "INFO" => EventSeverity::Info,
        "WARNING" => EventSeverity::Warning,
        "ERROR" => EventSeverity::Error,
        "CRITICAL" => EventSeverity::Critical,
        _ => EventSeverity::Info,
    };
    let database = &state.database;
    database.log_event(&event_type, severity_enum, &message, context.as_deref())
        .await
        .map_err(|e| format!("Erreur log événement: {}", e))
}

#[command]
async fn log_command_manual(
    command_log: CommandLog,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des champs requis
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("command_name", &command_log.command_name)
        .map_err(|e| format!("Validation command_name: {}", e))?;

    // Validation des IDs Discord si fournis
    if let Some(ref user_id) = command_log.user_id {
        DiscordIdValidator::validate("user_id", user_id)
            .map_err(|e| format!("Validation user_id: {}", e))?;
    }
    
    if let Some(ref guild_id) = command_log.guild_id {
        DiscordIdValidator::validate("guild_id", guild_id)
            .map_err(|e| format!("Validation guild_id: {}", e))?;
    }
    
    if let Some(ref channel_id) = command_log.channel_id {
        DiscordIdValidator::validate("channel_id", channel_id)
            .map_err(|e| format!("Validation channel_id: {}", e))?;
    }

    let database = &state.database;
    database.log_command(command_log)
        .await
        .map_err(|e| format!("Erreur log commande: {}", e))
}

#[command]
async fn increment_usage(
    feature: String,
    action: String,
    metadata: Option<String>,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des paramètres
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("feature", &feature)
        .map_err(|e| format!("Validation feature: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("action", &action)
        .map_err(|e| format!("Validation action: {}", e))?;
    
    if let Some(ref meta) = metadata {
        StringValidator::new()
            .max_length(1000)
            .validate("metadata", meta)
            .map_err(|e| format!("Validation metadata: {}", e))?;
    }

    let database = &state.database;
    database.increment_usage_stat(&feature, &action, metadata.as_deref())
        .await
        .map_err(|e| format!("Erreur incrémentation usage: {}", e))
}

// ============= COMMANDES PERSISTANCE SESSION =============

#[derive(Debug, Clone, Serialize, Deserialize)]
struct SavedSession {
    pub user_id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub encrypted_token: String,
    pub last_login: String,
    pub auto_login: bool,
}

#[command]
async fn save_user_session(
    user_data: DiscordUser,
    encrypted_token: String,
    auto_login: bool,
    state: State<'_, AppState>
) -> Result<(), String> {
    // Validation des données utilisateur
    DiscordIdValidator::validate("user_id", &user_data.id)
        .map_err(|e| format!("Validation user_id: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("username", &user_data.username)
        .map_err(|e| format!("Validation username: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(1000)
        .validate("encrypted_token", &encrypted_token)
        .map_err(|e| format!("Validation token: {}", e))?;

    // Sauvegarder dans la base de données
    let database = &state.database;
    
    // Sauvegarder le token chiffré dans la configuration
    // Dans une version future, on pourrait étendre la Database pour exposer des méthodes spécifiques
    database.save_config("secure_tokens", &format!("discord_user_{}", user_data.username), &encrypted_token)
        .await
        .map_err(|e| format!("Erreur sauvegarde token: {}", e))?;
    
    // Sauvegarder les données de session
    let session_data = SavedSession {
        user_id: user_data.id,
        username: user_data.username,
        discriminator: user_data.discriminator,
        avatar: user_data.avatar,
        email: user_data.email,
        encrypted_token: encrypted_token.clone(),
        last_login: chrono::Utc::now().to_rfc3339(),
        auto_login,
    };
    
    let session_json = serde_json::to_string(&session_data)
        .map_err(|e| format!("Erreur sérialisation session: {}", e))?;
    
    database.save_config("session", "current_user", &session_json)
        .await
        .map_err(|e| format!("Erreur sauvegarde session: {}", e))?;
    
    // Log de l'événement
    database.log_event("session", EventSeverity::Info, "Session utilisateur sauvegardée", Some(&format!("user: {}", session_data.username)))
        .await
        .map_err(|e| format!("Erreur log session: {}", e))?;
    
    Ok(())
}

#[command]
async fn get_saved_session(state: State<'_, AppState>) -> Result<Option<SavedSession>, String> {
    let database = &state.database;
    
    let session_json = database.get_config("session", "current_user")
        .await
        .map_err(|e| format!("Erreur récupération session: {}", e))?;
    
    if let Some(json) = session_json {
        let session: SavedSession = serde_json::from_str(&json)
            .map_err(|e| format!("Erreur désérialisation session: {}", e))?;
        
        // Vérifier que la session n'est pas trop ancienne (30 jours max)
        let last_login = chrono::DateTime::parse_from_rfc3339(&session.last_login)
            .map_err(|e| format!("Erreur parsing date: {}", e))?;
        
        let now = chrono::Utc::now();
        let age_days = (now - last_login.with_timezone(&chrono::Utc)).num_days();
        
        if age_days > 30 {
            // Session trop ancienne, on la supprime
            clear_saved_session(state).await?;
            return Ok(None);
        }
        
        Ok(Some(session))
    } else {
        Ok(None)
    }
}

#[command]
async fn clear_saved_session(state: State<'_, AppState>) -> Result<(), String> {
    let database = &state.database;
    
    // Supprimer la session de la base
    database.save_config("session", "current_user", "")
        .await
        .map_err(|e| format!("Erreur suppression session: {}", e))?;
    
    // Supprimer les tokens associés (optionnel - on peut les garder pour les autres comptes)
    // Pour l'instant on garde les tokens pour permettre le multi-compte
    
    // Log de l'événement
    database.log_event("session", EventSeverity::Info, "Session utilisateur supprimée", None)
        .await
        .map_err(|e| format!("Erreur log session: {}", e))?;
    
    Ok(())
}

#[command]
async fn has_saved_session(state: State<'_, AppState>) -> Result<bool, String> {
    let session = get_saved_session(state).await?;
    Ok(session.is_some())
}

#[command]
async fn auto_connect_from_session(state: State<'_, AppState>) -> Result<Option<DiscordUser>, String> {
    // Récupérer la session sauvegardée
    let session_data = {
        let database = &state.database;
        let session_json = database.get_config("session", "current_user")
            .await
            .map_err(|e| format!("Erreur récupération session: {}", e))?;
        
        if let Some(json) = session_json {
            let session: SavedSession = serde_json::from_str(&json)
                .map_err(|e| format!("Erreur désérialisation session: {}", e))?;
            Some(session)
        } else {
            None
        }
    };
    
    if let Some(session_data) = session_data {
        if session_data.auto_login {
            // Récupérer le token chiffré et tenter la connexion automatique
            let token_result = {
                let secure_storage = state.secure_storage.lock().await;
                if let Some(ref storage) = *secure_storage {
                    storage.load_token().await
                } else {
                    Ok(None)
                }
            };
            
            match token_result {
                Ok(Some(token)) => {
                    // Valider le token
                    if !is_valid_user_token(token.as_str()) {
                        // Token invalide, supprimer la session
                        let _ = clear_saved_session(state).await;
                        return Ok(None);
                    }
                    
                    // Le token est valide, retourner les données utilisateur
                    let user = DiscordUser {
                        id: session_data.user_id,
                        username: session_data.username,
                        discriminator: session_data.discriminator,
                        avatar: session_data.avatar,
                        email: session_data.email,
                    };
                    
                    return Ok(Some(user));
                },
                Ok(None) => {
                    // Pas de token stocké
                    let _ = clear_saved_session(state).await;
                    return Ok(None);
                },
                Err(e) => {
                    eprintln!("Erreur chargement token: {}", e);
                    let _ = clear_saved_session(state).await;
                    return Ok(None);
                }
            }
        }
    }
    
    Ok(None)
}

// ============================================================================
// Commandes Theme Builder
// ============================================================================

#[command]
async fn get_current_theme(state: State<'_, AppState>) -> Result<ThemeConfig, String> {
    let theme = state.current_theme.lock().await;
    Ok(theme.clone())
}

#[command]
async fn save_theme(theme: ThemeConfig, state: State<'_, AppState>) -> Result<(), String> {
    // Validation des données du thème
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("name", &theme.name)
        .map_err(|e| format!("Validation nom thème: {}", e))?;
    
    // Sauvegarde dans la base de données
    let database = &state.database;
    let theme_json = serde_json::to_string(&theme)
        .map_err(|e| format!("Erreur sérialisation thème: {}", e))?;
    
    database.save_config("themes", "current", &theme_json)
        .await
        .map_err(|e| format!("Erreur sauvegarde thème: {}", e))?;
    
    // Mettre à jour le thème actuel en mémoire
    let mut current_theme = state.current_theme.lock().await;
    *current_theme = theme;
    
    Ok(())
}

#[command]
async fn apply_theme(theme: ThemeConfig, state: State<'_, AppState>) -> Result<(), String> {
    // Appliquer le thème (mettre à jour en mémoire et sauvegarder)
    let mut current_theme = state.current_theme.lock().await;
    *current_theme = theme.clone();
    
    // Sauvegarder comme thème actuel
    let database = &state.database;
    let theme_json = serde_json::to_string(&theme)
        .map_err(|e| format!("Erreur sérialisation thème: {}", e))?;
    
    database.save_config("themes", "current", &theme_json)
        .await
        .map_err(|e| format!("Erreur sauvegarde thème appliqué: {}", e))?;
    
    Ok(())
}

#[command]
async fn get_custom_themes(state: State<'_, AppState>) -> Result<Vec<PresetTheme>, String> {
    let custom_themes = state.custom_themes.lock().await;
    Ok(custom_themes.clone())
}

#[command]
async fn save_custom_theme(preset: PresetTheme, state: State<'_, AppState>) -> Result<(), String> {
    // Validation des données du preset
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("id", &preset.id)
        .map_err(|e| format!("Validation ID preset: {}", e))?;
    
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("name", &preset.name)
        .map_err(|e| format!("Validation nom preset: {}", e))?;
    
    // Ajouter à la liste des thèmes custom
    let mut custom_themes = state.custom_themes.lock().await;
    
    // Vérifier si le thème existe déjà et le remplacer
    if let Some(index) = custom_themes.iter().position(|t| t.id == preset.id) {
        custom_themes[index] = preset.clone();
    } else {
        custom_themes.push(preset.clone());
    }
    
    // Sauvegarder dans la base de données
    let database = &state.database;
    let themes_json = serde_json::to_string(&*custom_themes)
        .map_err(|e| format!("Erreur sérialisation thèmes custom: {}", e))?;
    
    database.save_config("themes", "custom_themes", &themes_json)
        .await
        .map_err(|e| format!("Erreur sauvegarde thèmes custom: {}", e))?;
    
    Ok(())
}

#[command]
async fn delete_custom_theme(theme_id: String, state: State<'_, AppState>) -> Result<(), String> {
    // Validation de l'ID
    StringValidator::new()
        .required()
        .max_length(50)
        .validate("theme_id", &theme_id)
        .map_err(|e| format!("Validation ID thème: {}", e))?;
    
    // Supprimer de la liste
    let mut custom_themes = state.custom_themes.lock().await;
    custom_themes.retain(|t| t.id != theme_id);
    
    // Sauvegarder dans la base de données
    let database = &state.database;
    let themes_json = serde_json::to_string(&*custom_themes)
        .map_err(|e| format!("Erreur sérialisation thèmes custom: {}", e))?;
    
    database.save_config("themes", "custom_themes", &themes_json)
        .await
        .map_err(|e| format!("Erreur sauvegarde après suppression: {}", e))?;
    
    Ok(())
}

#[command]
async fn export_theme(theme: ThemeConfig, _state: State<'_, AppState>) -> Result<String, String> {
    // Exporter le thème en JSON formaté
    serde_json::to_string_pretty(&theme)
        .map_err(|e| format!("Erreur export thème: {}", e))
}

#[command]
async fn import_theme(theme_data: String, _state: State<'_, AppState>) -> Result<ThemeConfig, String> {
    // Importer un thème depuis JSON
    let theme: ThemeConfig = serde_json::from_str(&theme_data)
        .map_err(|e| format!("Erreur import thème - format invalide: {}", e))?;
    
    // Validation des données importées
    StringValidator::new()
        .required()
        .max_length(100)
        .validate("name", &theme.name)
        .map_err(|e| format!("Validation thème importé: {}", e))?;
    
    // Mettre à jour les timestamps
    let now = chrono::Utc::now().to_rfc3339();
    let mut updated_theme = theme;
    updated_theme.last_modified = now;
    
    Ok(updated_theme)
}

#[command]
async fn reset_theme_to_default(state: State<'_, AppState>) -> Result<ThemeConfig, String> {
    let default_theme = ThemeConfig::default();
    
    // Mettre à jour le thème actuel
    let mut current_theme = state.current_theme.lock().await;
    *current_theme = default_theme.clone();
    
    // Sauvegarder dans la base de données
    let database = &state.database;
    let theme_json = serde_json::to_string(&default_theme)
        .map_err(|e| format!("Erreur sérialisation thème par défaut: {}", e))?;
    
    database.save_config("themes", "current", &theme_json)
        .await
        .map_err(|e| format!("Erreur sauvegarde thème par défaut: {}", e))?;
    
    Ok(default_theme)
}

// ============= COMMANDES MENU CONTEXTUEL =============

#[command]
async fn copy_user_id(user_id: String) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("user_id", &user_id)
        .map_err(|e| format!("ID utilisateur invalide: {}", e))?;
    
    // Dans une vraie implémentation, on récupérerait l'ID depuis le contexte Discord
    // Pour l'instant, on retourne un ID fictif
    Ok("123456789012345678".to_string())
}

#[command]
async fn get_user_creation_date(user_id: String) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("user_id", &user_id)
        .map_err(|e| format!("ID utilisateur invalide: {}", e))?;
    
    // Calcul de la date de création depuis l'ID Discord (timestamp)
    if let Ok(id_num) = user_id.parse::<u64>() {
        let discord_epoch = 1420070400000u64; // 1er janvier 2015
        let timestamp_ms = (id_num >> 22) + discord_epoch;
        let datetime = chrono::DateTime::from_timestamp_millis(timestamp_ms as i64)
            .unwrap_or_else(chrono::Utc::now);
        
        Ok(datetime.format("%d/%m/%Y à %H:%M:%S UTC").to_string())
    } else {
        Err("ID Discord invalide".to_string())
    }
}

#[command]
async fn download_user_avatar(user_id: String, avatar_hash: Option<String>) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("user_id", &user_id)
        .map_err(|e| format!("ID utilisateur invalide: {}", e))?;
    
    if let Some(hash) = avatar_hash {
        let avatar_url = format!("https://cdn.discordapp.com/avatars/{}/{}.png?size=1024", user_id, hash);
        Ok(avatar_url)
    } else {
        // Avatar par défaut Discord
        let default_avatar = (user_id.parse::<u64>().unwrap_or(0) % 5).to_string();
        let avatar_url = format!("https://cdn.discordapp.com/embed/avatars/{}.png", default_avatar);
        Ok(avatar_url)
    }
}

#[command]
async fn get_user_nitro_status(user_id: String) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("user_id", &user_id)
        .map_err(|e| format!("ID utilisateur invalide: {}", e))?;
    
    // Dans une vraie implémentation, on vérifierait le statut Nitro via l'API Discord
    // Pour l'instant, on retourne un statut fictif
    Ok("Nitro Basic".to_string())
}

#[command]
async fn copy_message_id(message_id: String) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("message_id", &message_id)
        .map_err(|e| format!("ID message invalide: {}", e))?;
    
    Ok(message_id)
}

#[command]
async fn get_message_timestamp(message_id: String) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("message_id", &message_id)
        .map_err(|e| format!("ID message invalide: {}", e))?;
    
    // Calcul de la date de création depuis l'ID Discord (timestamp)
    if let Ok(id_num) = message_id.parse::<u64>() {
        let discord_epoch = 1420070400000u64; // 1er janvier 2015
        let timestamp_ms = (id_num >> 22) + discord_epoch;
        let datetime = chrono::DateTime::from_timestamp_millis(timestamp_ms as i64)
            .unwrap_or_else(chrono::Utc::now);
        
        Ok(datetime.format("%d/%m/%Y à %H:%M:%S.%3f UTC").to_string())
    } else {
        Err("ID Discord invalide".to_string())
    }
}

#[command]
async fn get_message_link(guild_id: String, channel_id: String, message_id: String) -> Result<String, String> {
    // Validation des IDs Discord
    DiscordIdValidator::validate("guild_id", &guild_id)
        .map_err(|e| format!("ID serveur invalide: {}", e))?;
    
    DiscordIdValidator::validate("channel_id", &channel_id)
        .map_err(|e| format!("ID canal invalide: {}", e))?;
    
    DiscordIdValidator::validate("message_id", &message_id)
        .map_err(|e| format!("ID message invalide: {}", e))?;
    
    let link = format!("https://discord.com/channels/{}/{}/{}", guild_id, channel_id, message_id);
    Ok(link)
}

#[command]
async fn copy_message_content(content: String) -> Result<String, String> {
    // Validation du contenu
    StringValidator::new()
        .max_length(4000) // Limite Discord pour les messages
        .validate("content", &content)
        .map_err(|e| format!("Contenu invalide: {}", e))?;
    
    Ok(content)
}

// === AUTO-UPDATER COMMANDS ===

#[command]
async fn check_for_updates(
    app_handle: AppHandle,
    state: State<'_, AppState>
) -> Result<UpdateStatus, String> {
    let mut status = state.update_status.lock().await;
    
    // Si déjà en train de vérifier, retourner le statut actuel
    if status.checking {
        return Ok(status.clone());
    }
    
    // Marquer comme en cours de vérification
    status.checking = true;
    status.error = None;
    drop(status);
    
    // Émettre l'événement de début de vérification
    app_handle.emit("update-check-started", ()).ok();
    
    match app_handle.updater() {
        Ok(updater) => {
            match updater.check().await {
                Ok(Some(update)) => {
                    let update_info = UpdateInfo {
                        version: update.version.clone(),
                        release_date: update.date.map(|d| d.to_string()).unwrap_or_default(),
                        notes: update.body.unwrap_or_default(),
                        download_url: "".to_string(), // Géré par Tauri
                        signature: "".to_string(), // Géré par Tauri
                        size: 0, // Non disponible dans l'API Tauri
                    };
                    
                    let mut status = state.update_status.lock().await;
                    status.checking = false;
                    status.available = true;
                    status.update_info = Some(update_info.clone());
                    
                    // Mettre à jour la configuration avec la dernière vérification
                    let mut config = state.update_config.lock().await;
                    config.last_check = Some(chrono::Utc::now().to_rfc3339());
                    drop(config);
                    
                    app_handle.emit("update-available", &update_info).ok();
                    Ok(status.clone())
                }
                Ok(None) => {
                    let mut status = state.update_status.lock().await;
                    status.checking = false;
                    status.available = false;
                    status.update_info = None;
                    
                    // Mettre à jour la configuration
                    let mut config = state.update_config.lock().await;
                    config.last_check = Some(chrono::Utc::now().to_rfc3339());
                    drop(config);
                    
                    app_handle.emit("update-not-available", ()).ok();
                    Ok(status.clone())
                }
                Err(e) => {
                    let mut status = state.update_status.lock().await;
                    status.checking = false;
                    status.error = Some(format!("Erreur lors de la vérification: {}", e));
                    
                    app_handle.emit("update-error", &status.error).ok();
                    Err(format!("Erreur lors de la vérification des mises à jour: {}", e))
                }
            }
        }
        Err(e) => {
            let mut status = state.update_status.lock().await;
            status.checking = false;
            status.error = Some(format!("Updater non disponible: {}", e));
            
            Err("Service de mise à jour non disponible".to_string())
        }
    }
}

#[command]
async fn download_and_install_update(
    app_handle: AppHandle,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut status = state.update_status.lock().await;
    
    // Vérifier qu'une mise à jour est disponible
    if !status.available || status.update_info.is_none() {
        return Err("Aucune mise à jour disponible".to_string());
    }
    
    // Si déjà en cours de téléchargement/installation
    if status.downloading || status.installing {
        return Err("Mise à jour déjà en cours".to_string());
    }
    
    status.downloading = true;
    status.progress = 0.0;
    status.error = None;
    drop(status);
    
    app_handle.emit("update-download-started", ()).ok();
    
    match app_handle.updater() {
        Ok(updater) => {
            match updater.check().await {
                Ok(Some(update)) => {
                    // Télécharger la mise à jour
                    match update.download(
                        |chunk_length, content_length| {
                            if let Some(total) = content_length {
                                let progress = (chunk_length as f64 / total as f64) * 100.0;
                                app_handle.emit("update-download-progress", progress).ok();
                                
                                // Mettre à jour le statut
                                tauri::async_runtime::spawn({
                                    let state = state.inner().clone();
                                    async move {
                                        if let Ok(mut status) = state.update_status.try_lock() {
                                            status.progress = progress;
                                        }
                                    }
                                });
                            }
                        },
                        || {
                            app_handle.emit("download-cancelled", ()).ok();
                        }
                    ).await {
                        Ok(_) => {
                            let mut status = state.update_status.lock().await;
                            status.downloading = false;
                            status.installing = true;
                            status.progress = 100.0;
                            drop(status);
                            
                            app_handle.emit("update-download-finished", ()).ok();
                            app_handle.emit("update-install-started", ()).ok();
                            
                            // Installer la mise à jour
                            match update.download_and_install(
                                |_chunk_length, _content_length| {
                                    // On peut ajouter ici un système de progression si nécessaire
                                },
                                || {
                                    // Callback appelé quand le téléchargement est terminé
                                }
                            ).await {
                                Ok(_) => {
                                    app_handle.emit("update-installed", ()).ok();
                                    // L'application va redémarrer automatiquement
                                    Ok(())
                                }
                                Err(e) => {
                                    let mut status = state.update_status.lock().await;
                                    status.installing = false;
                                    status.error = Some(format!("Erreur d'installation: {}", e));
                                    
                                    app_handle.emit("update-install-error", &status.error).ok();
                                    Err(format!("Erreur lors de l'installation: {}", e))
                                }
                            }
                        }
                        Err(e) => {
                            let mut status = state.update_status.lock().await;
                            status.downloading = false;
                            status.error = Some(format!("Erreur de téléchargement: {}", e));
                            
                            app_handle.emit("update-download-error", &status.error).ok();
                            Err(format!("Erreur lors du téléchargement: {}", e))
                        }
                    }
                }
                Ok(None) => {
                    let mut status = state.update_status.lock().await;
                    status.downloading = false;
                    status.available = false;
                    status.update_info = None;
                    
                    Err("Aucune mise à jour disponible".to_string())
                }
                Err(e) => {
                    let mut status = state.update_status.lock().await;
                    status.downloading = false;
                    status.error = Some(format!("Erreur de vérification: {}", e));
                    
                    Err(format!("Erreur lors de la vérification: {}", e))
                }
            }
        }
        Err(e) => {
            let mut status = state.update_status.lock().await;
            status.downloading = false;
            status.error = Some(format!("Updater non disponible: {}", e));
            
            Err("Service de mise à jour non disponible".to_string())
        }
    }
}

#[command]
async fn get_update_config(state: State<'_, AppState>) -> Result<UpdateConfig, String> {
    let config = state.update_config.lock().await;
    Ok(config.clone())
}

#[command]
async fn set_update_config(
    config: UpdateConfig,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut current_config = state.update_config.lock().await;
    *current_config = config;
    Ok(())
}

#[command]
async fn get_update_status(state: State<'_, AppState>) -> Result<UpdateStatus, String> {
    let status = state.update_status.lock().await;
    Ok(status.clone())
}

#[command]
async fn get_current_version() -> Result<String, String> {
    Ok(env!("CARGO_PKG_VERSION").to_string())
}

#[command]
async fn restart_application(app_handle: AppHandle) -> Result<(), String> {
    app_handle.restart();
}

#[command]
async fn cancel_update(state: State<'_, AppState>) -> Result<(), String> {
    let mut status = state.update_status.lock().await;
    
    if status.downloading || status.installing {
        status.downloading = false;
        status.installing = false;
        status.progress = 0.0;
        status.error = Some("Mise à jour annulée par l'utilisateur".to_string());
        Ok(())
    } else {
        Err("Aucune mise à jour en cours".to_string())
    }
}

#[command]
async fn clone_server_emojis(guild_id: String) -> Result<Vec<String>, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("guild_id", &guild_id)
        .map_err(|e| format!("ID serveur invalide: {}", e))?;
    
    // Dans une vraie implémentation, on récupérerait les emojis du serveur
    // Pour l'instant, on retourne une liste fictive
    Ok(vec![
        "https://cdn.discordapp.com/emojis/123456789012345678.png".to_string(),
        "https://cdn.discordapp.com/emojis/876543210987654321.png".to_string(),
    ])
}

#[command]
async fn download_server_emojis(guild_id: String) -> Result<String, String> {
    // Validation de l'ID Discord
    DiscordIdValidator::validate("guild_id", &guild_id)
        .map_err(|e| format!("ID serveur invalide: {}", e))?;
    
    // Dans une vraie implémentation, on téléchargerait tous les emojis
    // Pour l'instant, on retourne un message de succès
    Ok(format!("Emojis du serveur {} téléchargés avec succès", guild_id))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_updater::Builder::new().build())
        .invoke_handler(tauri::generate_handler![
            set_stealth_mode,
            get_stealth_mode,
            connect_discord_bot,
            disconnect_discord_bot,
            get_bot_status,
            minimize_window,
            maximize_window,
            close_window,
            open_discord_login_webview,
            get_notifications,
            get_notification_config,
            mark_notification_as_read,
            mark_all_notifications_as_read,
            delete_notification,
            clear_all_notifications,
            update_notification_config,
            get_unread_notifications_count,
            get_auto_commands_config,
            update_auto_translate_config,
            update_auto_slash_config,
            update_auto_reply_config,
            add_slash_trigger,
            remove_slash_trigger,
            add_auto_reply,
            remove_auto_reply,
            toggle_auto_translate,
            toggle_auto_slash,
            toggle_auto_reply,
            clear_auto_commands_cooldowns,
            get_giveaway_config,
            update_giveaway_config,
            get_giveaway_stats,
            get_detected_giveaways,
            get_active_giveaways,
            get_joined_giveaways,
            mark_giveaway_as_joined,
            mark_giveaway_as_skipped,
            record_giveaway_win,
            cleanup_old_giveaways,
            toggle_giveaway_joiner,
            get_nitro_sniper_config,
            update_nitro_sniper_config,
            get_nitro_sniper_stats,
            get_detected_nitro_codes,
            get_claimed_nitro_codes,
            get_recent_nitro_codes,
            get_nitro_performance_stats,
            cleanup_old_nitro_codes,
            toggle_nitro_sniper,
            toggle_nitro_test_mode,
            set_nitro_sniper_delays,
            add_trusted_bot,
            remove_trusted_bot,
            get_image_config,
            update_image_config,
            toggle_image_generation,
            reset_image_config,
            get_rate_limit_config,
            update_rate_limit_config,
            get_rate_limit_stats,
            reset_rate_limit_bucket,
            reset_all_rate_limit_buckets,
            toggle_rate_limiter,
            get_database_stats,
            get_usage_stats,
            get_recent_logs,
            get_command_history,
            log_custom_event,
            increment_feature_usage,
            save_app_config,
            get_app_config,
            get_category_configs,
            cleanup_old_database_data,
            get_activity_viewer_config,
            update_activity_viewer_config,
            get_tracked_users,
            add_tracked_user,
            remove_tracked_user,
            get_activity_stats,
            get_user_activity_history,
            get_online_users,
            get_users_with_activity,
            cleanup_inactive_tracked_users,
            export_activity_data,
            toggle_activity_viewer,
            get_active_trolls,
            start_ghostping_troll,
            start_spam_troll,
            start_fake_typing_troll,
            start_noleave_troll,
            stop_troll,
            stop_all_trolls,
            enable_auto_startup,
            disable_auto_startup,
            get_auto_startup_status,
            scan_discord_processes,
            get_discord_processes,
            is_discord_running,
            start_discord_monitoring,
            start_mouse_listener,
            stop_mouse_listener,
            translate_text,
            extract_emojis,
            get_overlay_config,
            update_overlay_config,
            get_dynamic_variables_config,
            update_dynamic_variables_config,
            get_available_variables,
            resolve_variable,
            resolve_text,
            add_custom_variable,
            remove_custom_variable,
            get_cached_variables,
            refresh_all_variables,
            cleanup_variables_cache,
            toggle_dynamic_variables,
            // Commandes base de données
            save_configuration,
            get_configuration,
            get_category_configurations,
            get_usage_statistics,
            get_recent_event_logs,
            cleanup_database,
            log_event_manual,
            log_command_manual,
            increment_usage,
            // Commandes persistance session
            save_user_session,
            get_saved_session,
            clear_saved_session,
            has_saved_session,
            auto_connect_from_session,
            // Commandes Theme Builder
            get_current_theme,
            apply_theme,
            get_custom_themes,
            save_custom_theme,
            delete_custom_theme,
            export_theme,
            import_theme,
            reset_theme_to_default,
            copy_user_id,
            get_user_creation_date,
            download_user_avatar,
            get_user_nitro_status,
            copy_message_id,
            get_message_timestamp,
            get_message_link,
            copy_message_content,
            clone_server_emojis,
            download_server_emojis,
            // Commandes Discord WebAuth
            start_discord_webauth,
            verify_discord_token,
            // Commandes Auto-Updater
            check_for_updates,
            download_and_install_update,
            get_update_config,
            set_update_config,
            get_update_status,
            get_current_version,
            restart_application,
            cancel_update,
        ])
        .setup(|app| {
            // Initialiser l'AppState et IPC de manière asynchrone
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                let (app_state, ipc_receiver) = AppState::new().await;
                let database = app_state.database.clone();
                
                // Démarrer le processeur IPC en arrière-plan
                tauri::async_runtime::spawn(async move {
                    start_ipc_processor(database, ipc_receiver).await;
                });
                
                app_handle.manage(app_state);
            });
            
            // Configuration de la fenêtre en mode debug
            #[cfg(debug_assertions)]
            if let Some(window) = app.get_webview_window("main") {
                window.open_devtools();
            }
            
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("Erreur lors du lancement de l'application Tauri");
}

