#!/bin/bash

# Script de build VoidBot Desktop en exécutable
echo "🏗️ Build VoidBot Desktop..."

cd voidbot-desktop

# Installer les dépendances npm
echo "📦 Installation des dépendances frontend..."
npm install

# Build du frontend React
echo "⚛️ Build du frontend React..."
npm run build

# Build de l'application Tauri
echo "🦀 Build de l'application Tauri..."
npm run tauri build

echo "✅ Build terminé !"
echo "📂 Exécutables disponibles dans src-tauri/target/release/bundle/"
echo "🪟 Windows: voidbot-desktop.exe"
echo "🐧 Linux: voidbot-desktop.AppImage"
echo "🍎 macOS: VoidBot.app"