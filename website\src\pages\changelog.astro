---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const versions = [
  {
    version: "1.0.0",
    date: "2024-07-06",
    status: "latest",
    changes: [
      { type: "feature", text: "Interface desktop native avec Tauri v2.6.2" },
      { type: "feature", text: "Plus de 100 commandes Discord disponibles" },
      { type: "feature", text: "Système de mode furtif Normal/Fantôme" },
      { type: "feature", text: "Nitro Sniper ultra-rapide (50-200ms)" },
      { type: "feature", text: "Giveaway Joiner intelligent" },
      { type: "feature", text: "Notification Center avec 14 types d'événements" },
      { type: "feature", text: "Auto-Commands (Translate, Slash, Reply)" },
      { type: "feature", text: "5 types de trolls sécurisés" },
      { type: "feature", text: "Générateur d'images complet" },
      { type: "feature", text: "Rate limiting robuste anti-ban" },
      { type: "security", text: "Chiffrement AES-256-GCM pour les tokens" },
      { type: "security", text: "Architecture 100% locale" },
      { type: "security", text: "CSP Tauri strict activé" }
    ]
  },
  {
    version: "0.9.0-beta",
    date: "2024-06-15", 
    status: "beta",
    changes: [
      { type: "feature", text: "Version bêta avec fonctionnalités de base" },
      { type: "feature", text: "Login web Discord intégré" },
      { type: "feature", text: "Commandes Discord principales" },
      { type: "improvement", text: "Tests et optimisations" }
    ]
  }
];

const changeTypeColors = {
  feature: "text-green-400",
  improvement: "text-blue-400", 
  security: "text-purple-400",
  fix: "text-yellow-400",
  breaking: "text-red-400"
};

const changeTypeIcons = {
  feature: "✨",
  improvement: "⚡",
  security: "🔒",
  fix: "🐛", 
  breaking: "⚠️"
};
---

<Layout title="Changelog VoidBot - Historique des Versions" description="Découvrez toutes les nouveautés, améliorations et corrections de chaque version de VoidBot.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl sm:text-5xl font-bold mb-6">
          <span class="text-white">Historique des</span>
          <br>
          <span class="gradient-text">Versions</span>
        </h1>
        <p class="text-xl text-gray-300 mb-8">
          Découvrez toutes les nouveautés, améliorations et corrections apportées à VoidBot au fil des versions.
        </p>
      </div>
    </section>

    <!-- Changelog -->
    <section class="py-12">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="space-y-12">
          {versions.map((version, index) => (
            <div class="relative">
              <!-- Timeline line -->
              {index < versions.length - 1 && (
                <div class="absolute left-6 top-16 w-0.5 h-full bg-gray-800"></div>
              )}
              
              <!-- Version card -->
              <div class="card-hover relative">
                <!-- Version badge -->
                <div class="flex items-center gap-4 mb-6">
                  <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center z-10">
                    <span class="text-white font-bold text-lg">V</span>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center gap-3 mb-1">
                      <h2 class="text-2xl font-bold text-white">Version {version.version}</h2>
                      {version.status === 'latest' && (
                        <span class="px-3 py-1 bg-green-500/20 border border-green-500/30 rounded-full text-green-400 text-sm font-medium">
                          Dernière
                        </span>
                      )}
                      {version.status === 'beta' && (
                        <span class="px-3 py-1 bg-orange-500/20 border border-orange-500/30 rounded-full text-orange-400 text-sm font-medium">
                          Bêta
                        </span>
                      )}
                    </div>
                    <p class="text-gray-400">Publiée le {new Date(version.date).toLocaleDateString('fr-FR', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}</p>
                  </div>
                </div>
                
                <!-- Changes list -->
                <div class="ml-16">
                  <div class="space-y-3">
                    {version.changes.map((change) => (
                      <div class="flex items-start gap-3 p-3 bg-gray-900/50 rounded-lg border border-gray-800 hover:border-gray-700 transition-colors duration-200">
                        <span class="text-lg" title={change.type}>
                          {changeTypeIcons[change.type]}
                        </span>
                        <div class="flex-1">
                          <span class={`text-sm font-medium ${changeTypeColors[change.type]} capitalize`}>
                            {change.type === 'feature' ? 'Nouveau' : 
                             change.type === 'improvement' ? 'Amélioration' :
                             change.type === 'security' ? 'Sécurité' :
                             change.type === 'fix' ? 'Correction' : 'Breaking'}
                          </span>
                          <p class="text-gray-300 mt-1">{change.text}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Download latest -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-6">
          <span class="text-white">Profitez de la</span>
          <span class="gradient-text"> Dernière Version</span>
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          Téléchargez VoidBot v{versions[0].version} et découvrez toutes les dernières fonctionnalités.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/download" class="btn btn-primary text-lg px-8 py-4 glow">
            Télécharger v{versions[0].version}
          </a>
          <a href="/docs" class="btn btn-secondary text-lg px-8 py-4">
            Guide de Migration
          </a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>