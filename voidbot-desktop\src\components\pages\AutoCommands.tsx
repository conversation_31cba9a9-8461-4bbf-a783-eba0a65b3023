import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bot, 
  MessageSquare, 
  Languages, 
  Command,
  Plus,
  Trash2,
  Power,
  Clock,
  Target,
  AlertTriangle,
  Info
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';

interface AutoTranslateConfig {
  enabled: boolean;
  source_lang: string;
  target_lang: string;
  channels: number[];
  exclude_bots: boolean;
  exclude_self: boolean;
  min_length: number;
  service: 'Google' | 'DeepL' | 'Bing';
}

interface SlashTrigger {
  command: string;
  options: Record<string, string>;
  delay_ms: number;
  enabled: boolean;
  case_sensitive: boolean;
  exact_match: boolean;
}

interface AutoSlashConfig {
  enabled: boolean;
  triggers: Record<string, SlashTrigger>;
  global_enabled: boolean;
  guild_settings: Record<string, any>;
}

interface AutoReply {
  response: string;
  enabled: boolean;
  random_responses: string[];
  probability: number;
  delete_trigger: boolean;
  case_sensitive: boolean;
  dm_only: boolean;
}

interface AutoReplyConfig {
  enabled: boolean;
  triggers: Record<string, AutoReply>;
  global_cooldown_seconds: number;
  per_user_cooldown_seconds: number;
}

interface AutoCommandsState {
  auto_translate: AutoTranslateConfig;
  auto_slash: AutoSlashConfig;
  auto_reply: AutoReplyConfig;
  last_translation_cache: Record<string, string>;
  slash_cooldowns: Record<string, number>;
  reply_cooldowns: Record<string, number>;
}

export function AutoCommands() {
  const [config, setConfig] = useState<AutoCommandsState | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'translate' | 'slash' | 'reply'>('translate');
  
  // États pour les formulaires
  const [newSlashKeyword, setNewSlashKeyword] = useState('');
  const [newSlashCommand, setNewSlashCommand] = useState('');
  const [newSlashDelay, setNewSlashDelay] = useState(1000);
  const [newReplyKeyword, setNewReplyKeyword] = useState('');
  const [newReplyResponse, setNewReplyResponse] = useState('');
  
  // État d'édition (futures fonctionnalités)
  // const [editingSlash, setEditingSlash] = useState<string | null>(null);
  // const [editingReply, setEditingReply] = useState<string | null>(null);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const autoConfig = await invoke<AutoCommandsState>('get_auto_commands_config');
      setConfig(autoConfig);
    } catch (error) {
      console.error('Erreur chargement config auto-commands:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateTranslateConfig = async (newConfig: AutoTranslateConfig) => {
    try {
      await invoke('update_auto_translate_config', { config: newConfig });
      if (config) {
        setConfig({ ...config, auto_translate: newConfig });
      }
    } catch (error) {
      console.error('Erreur mise à jour config traduction:', error);
      alert('Erreur lors de la mise à jour de la configuration');
    }
  };

  const updateSlashConfig = async (newConfig: AutoSlashConfig) => {
    try {
      await invoke('update_auto_slash_config', { config: newConfig });
      if (config) {
        setConfig({ ...config, auto_slash: newConfig });
      }
    } catch (error) {
      console.error('Erreur mise à jour config slash:', error);
      alert('Erreur lors de la mise à jour de la configuration');
    }
  };

  const updateReplyConfig = async (newConfig: AutoReplyConfig) => {
    try {
      await invoke('update_auto_reply_config', { config: newConfig });
      if (config) {
        setConfig({ ...config, auto_reply: newConfig });
      }
    } catch (error) {
      console.error('Erreur mise à jour config réponses:', error);
      alert('Erreur lors de la mise à jour de la configuration');
    }
  };

  const addSlashTrigger = async () => {
    if (!newSlashKeyword || !newSlashCommand) {
      alert('Mot-clé et commande requis');
      return;
    }

    const trigger: SlashTrigger = {
      command: newSlashCommand,
      options: {},
      delay_ms: newSlashDelay,
      enabled: true,
      case_sensitive: false,
      exact_match: false,
    };

    try {
      await invoke('add_slash_trigger', { keyword: newSlashKeyword, trigger });
      loadConfig();
      setNewSlashKeyword('');
      setNewSlashCommand('');
      setNewSlashDelay(1000);
    } catch (error) {
      console.error('Erreur ajout trigger slash:', error);
      alert('Erreur lors de l\'ajout du trigger');
    }
  };

  const removeSlashTrigger = async (keyword: string) => {
    try {
      await invoke('remove_slash_trigger', { keyword });
      loadConfig();
    } catch (error) {
      console.error('Erreur suppression trigger slash:', error);
    }
  };

  const addAutoReply = async () => {
    if (!newReplyKeyword || !newReplyResponse) {
      alert('Mot-clé et réponse requis');
      return;
    }

    const reply: AutoReply = {
      response: newReplyResponse,
      enabled: true,
      random_responses: [],
      probability: 1.0,
      delete_trigger: false,
      case_sensitive: false,
      dm_only: false,
    };

    try {
      await invoke('add_auto_reply', { keyword: newReplyKeyword, reply });
      loadConfig();
      setNewReplyKeyword('');
      setNewReplyResponse('');
    } catch (error) {
      console.error('Erreur ajout auto-reply:', error);
      alert('Erreur lors de l\'ajout de la réponse automatique');
    }
  };

  const removeAutoReply = async (keyword: string) => {
    try {
      await invoke('remove_auto_reply', { keyword });
      loadConfig();
    } catch (error) {
      console.error('Erreur suppression auto-reply:', error);
    }
  };

  const toggleAutoTranslate = async () => {
    if (!config) return;
    const enabled = !config.auto_translate.enabled;
    
    try {
      await invoke('toggle_auto_translate', { enabled });
      updateTranslateConfig({ ...config.auto_translate, enabled });
    } catch (error) {
      console.error('Erreur toggle auto-translate:', error);
    }
  };

  const toggleAutoSlash = async () => {
    if (!config) return;
    const enabled = !config.auto_slash.enabled;
    
    try {
      await invoke('toggle_auto_slash', { enabled });
      updateSlashConfig({ ...config.auto_slash, enabled });
    } catch (error) {
      console.error('Erreur toggle auto-slash:', error);
    }
  };

  const toggleAutoReply = async () => {
    if (!config) return;
    const enabled = !config.auto_reply.enabled;
    
    try {
      await invoke('toggle_auto_reply', { enabled });
      updateReplyConfig({ ...config.auto_reply, enabled });
    } catch (error) {
      console.error('Erreur toggle auto-reply:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400">Chargement des auto-commandes...</p>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-3" />
          <p className="text-red-400">Impossible de charger la configuration</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'translate', label: 'Auto-Translate', icon: Languages, enabled: config.auto_translate.enabled },
    { id: 'slash', label: 'Auto-Slash', icon: Command, enabled: config.auto_slash.enabled },
    { id: 'reply', label: 'Auto-Reply', icon: MessageSquare, enabled: config.auto_reply.enabled },
  ];

  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Bot className="w-8 h-8 text-blue-500" />
          <div>
            <h1 className="text-2xl font-bold text-white">Auto-Commands</h1>
            <p className="text-gray-400">Automatisez vos interactions Discord</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => invoke('clear_auto_commands_cooldowns')}
            className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Clock className="w-4 h-4" />
            <span>Reset Cooldowns</span>
          </button>
        </div>
      </div>

      {/* Onglets */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`
                flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all
                ${activeTab === tab.id 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }
              `}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
              {tab.enabled && (
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              )}
            </button>
          );
        })}
      </div>

      {/* Contenu des onglets */}
      <AnimatePresence mode="wait">
        {activeTab === 'translate' && (
          <motion.div
            key="translate"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            {/* Auto-Translate */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
                  <Languages className="w-5 h-5 text-blue-500" />
                  <span>Traduction Automatique</span>
                </h2>
                <button
                  onClick={toggleAutoTranslate}
                  className={`
                    px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors
                    ${config.auto_translate.enabled 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-600 hover:bg-gray-700 text-gray-300'
                    }
                  `}
                >
                  <Power className="w-4 h-4" />
                  <span>{config.auto_translate.enabled ? 'Activé' : 'Désactivé'}</span>
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-white font-medium mb-2">Service de traduction</label>
                    <select
                      value={config.auto_translate.service}
                      onChange={(e) => updateTranslateConfig({
                        ...config.auto_translate,
                        service: e.target.value as any
                      })}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                    >
                      <option value="Google">Google Translate</option>
                      <option value="DeepL">DeepL (Pro)</option>
                      <option value="Bing">Bing Translator</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-white font-medium mb-2">Langue source</label>
                      <select
                        value={config.auto_translate.source_lang}
                        onChange={(e) => updateTranslateConfig({
                          ...config.auto_translate,
                          source_lang: e.target.value
                        })}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                      >
                        <option value="auto">Détection auto</option>
                        <option value="en">Anglais</option>
                        <option value="fr">Français</option>
                        <option value="es">Espagnol</option>
                        <option value="de">Allemand</option>
                        <option value="it">Italien</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">Langue cible</label>
                      <select
                        value={config.auto_translate.target_lang}
                        onChange={(e) => updateTranslateConfig({
                          ...config.auto_translate,
                          target_lang: e.target.value
                        })}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                      >
                        <option value="fr">Français</option>
                        <option value="en">Anglais</option>
                        <option value="es">Espagnol</option>
                        <option value="de">Allemand</option>
                        <option value="it">Italien</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">
                      Longueur minimum ({config.auto_translate.min_length} caractères)
                    </label>
                    <input
                      type="range"
                      min="5"
                      max="50"
                      value={config.auto_translate.min_length}
                      onChange={(e) => updateTranslateConfig({
                        ...config.auto_translate,
                        min_length: parseInt(e.target.value)
                      })}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.auto_translate.exclude_bots}
                      onChange={(e) => updateTranslateConfig({
                        ...config.auto_translate,
                        exclude_bots: e.target.checked
                      })}
                      className="w-4 h-4"
                    />
                    <label className="text-white">Exclure les bots</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.auto_translate.exclude_self}
                      onChange={(e) => updateTranslateConfig({
                        ...config.auto_translate,
                        exclude_self: e.target.checked
                      })}
                      className="w-4 h-4"
                    />
                    <label className="text-white">Exclure ses propres messages</label>
                  </div>

                  <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-blue-200">
                        <p className="font-medium">Configuration recommandée</p>
                        <p>Source: Détection auto, Cible: Français, Min: 10 caractères</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'slash' && (
          <motion.div
            key="slash"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            {/* Auto-Slash */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
                  <Command className="w-5 h-5 text-green-500" />
                  <span>Commandes Slash Automatiques</span>
                </h2>
                <button
                  onClick={toggleAutoSlash}
                  className={`
                    px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors
                    ${config.auto_slash.enabled 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-600 hover:bg-gray-700 text-gray-300'
                    }
                  `}
                >
                  <Power className="w-4 h-4" />
                  <span>{config.auto_slash.enabled ? 'Activé' : 'Désactivé'}</span>
                </button>
              </div>

              {/* Nouveau trigger */}
              <div className="bg-gray-700 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-medium text-white mb-4">Ajouter un trigger</h3>
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                  <input
                    type="text"
                    placeholder="Mot-clé déclencheur"
                    value={newSlashKeyword}
                    onChange={(e) => setNewSlashKeyword(e.target.value)}
                    className="bg-gray-600 border border-gray-500 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                  <input
                    type="text"
                    placeholder="Commande à exécuter"
                    value={newSlashCommand}
                    onChange={(e) => setNewSlashCommand(e.target.value)}
                    className="bg-gray-600 border border-gray-500 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                  <input
                    type="number"
                    placeholder="Délai (ms)"
                    value={newSlashDelay}
                    onChange={(e) => setNewSlashDelay(parseInt(e.target.value) || 1000)}
                    className="bg-gray-600 border border-gray-500 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                  <button
                    onClick={addSlashTrigger}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center space-x-2 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Ajouter</span>
                  </button>
                </div>
              </div>

              {/* Liste des triggers */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-white">
                  Triggers configurés ({Object.keys(config.auto_slash.triggers).length})
                </h3>
                
                {Object.keys(config.auto_slash.triggers).length === 0 ? (
                  <div className="text-center py-8 text-gray-400">
                    <Target className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>Aucun trigger configuré</p>
                    <p className="text-sm">Ajoutez votre premier trigger ci-dessus</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(config.auto_slash.triggers).map(([keyword, trigger]) => (
                      <div key={keyword} className="bg-gray-700 rounded-lg p-4 flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className={`w-3 h-3 rounded-full ${trigger.enabled ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                            <span className="font-medium text-white">"{keyword}"</span>
                            <span className="text-gray-400">→</span>
                            <span className="text-blue-400">{trigger.command}</span>
                          </div>
                          <div className="text-sm text-gray-400 mt-1">
                            Délai: {trigger.delay_ms}ms | 
                            {trigger.case_sensitive ? ' Sensible casse' : ' Insensible casse'} | 
                            {trigger.exact_match ? ' Correspondance exacte' : ' Contient le mot'}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => removeSlashTrigger(keyword)}
                            className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                            title="Supprimer"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'reply' && (
          <motion.div
            key="reply"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            {/* Auto-Reply */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
                  <MessageSquare className="w-5 h-5 text-purple-500" />
                  <span>Réponses Automatiques</span>
                </h2>
                <button
                  onClick={toggleAutoReply}
                  className={`
                    px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors
                    ${config.auto_reply.enabled 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-600 hover:bg-gray-700 text-gray-300'
                    }
                  `}
                >
                  <Power className="w-4 h-4" />
                  <span>{config.auto_reply.enabled ? 'Activé' : 'Désactivé'}</span>
                </button>
              </div>

              {/* Configuration globale */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-white font-medium mb-2">
                    Cooldown global ({config.auto_reply.global_cooldown_seconds}s)
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="60"
                    value={config.auto_reply.global_cooldown_seconds}
                    onChange={(e) => updateReplyConfig({
                      ...config.auto_reply,
                      global_cooldown_seconds: parseInt(e.target.value)
                    })}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-white font-medium mb-2">
                    Cooldown par utilisateur ({config.auto_reply.per_user_cooldown_seconds}s)
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="300"
                    value={config.auto_reply.per_user_cooldown_seconds}
                    onChange={(e) => updateReplyConfig({
                      ...config.auto_reply,
                      per_user_cooldown_seconds: parseInt(e.target.value)
                    })}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Nouvelle réponse */}
              <div className="bg-gray-700 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-medium text-white mb-4">Ajouter une réponse automatique</h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <input
                    type="text"
                    placeholder="Mot-clé déclencheur"
                    value={newReplyKeyword}
                    onChange={(e) => setNewReplyKeyword(e.target.value)}
                    className="bg-gray-600 border border-gray-500 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                  <input
                    type="text"
                    placeholder="Réponse automatique"
                    value={newReplyResponse}
                    onChange={(e) => setNewReplyResponse(e.target.value)}
                    className="bg-gray-600 border border-gray-500 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                  <button
                    onClick={addAutoReply}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg flex items-center justify-center space-x-2 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Ajouter</span>
                  </button>
                </div>
              </div>

              {/* Liste des réponses */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-white">
                  Réponses configurées ({Object.keys(config.auto_reply.triggers).length})
                </h3>
                
                {Object.keys(config.auto_reply.triggers).length === 0 ? (
                  <div className="text-center py-8 text-gray-400">
                    <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>Aucune réponse automatique configurée</p>
                    <p className="text-sm">Ajoutez votre première réponse ci-dessus</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(config.auto_reply.triggers).map(([keyword, reply]) => (
                      <div key={keyword} className="bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <div className={`w-3 h-3 rounded-full ${reply.enabled ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                              <span className="font-medium text-white">"{keyword}"</span>
                              <span className="text-gray-400">→</span>
                              <span className="text-purple-400">"{reply.response}"</span>
                            </div>
                            <div className="text-sm text-gray-400">
                              Probabilité: {(reply.probability * 100).toFixed(0)}% | 
                              {reply.case_sensitive ? ' Sensible casse' : ' Insensible casse'} | 
                              {reply.dm_only ? ' DM uniquement' : ' Tous canaux'} |
                              {reply.delete_trigger ? ' Supprime trigger' : ' Garde trigger'}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => removeAutoReply(keyword)}
                              className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                              title="Supprimer"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Avertissement de sécurité */}
      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-6 h-6 text-yellow-500 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-yellow-200">
            <p className="font-medium mb-2">⚠️ Utilisation responsable des auto-commands</p>
            <ul className="space-y-1 text-xs">
              <li>• Les auto-commands peuvent déclencher des actions automatiques visibles par les autres utilisateurs</li>
              <li>• Configurez des cooldowns appropriés pour éviter le spam</li>
              <li>• Respectez les règles des serveurs Discord que vous fréquentez</li>
              <li>• L'abus d'auto-commands peut résulter en sanctions Discord</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}