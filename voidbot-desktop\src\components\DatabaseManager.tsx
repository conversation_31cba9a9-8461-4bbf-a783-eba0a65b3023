import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';

interface DatabaseStats {
  total_configs: number;
  total_commands: number;
  total_events: number;
  total_giveaways: number;
  total_nitro_codes: number;
  database_size_kb: number;
}

interface UsageStat {
  feature: string;
  action: string;
  count: number;
  last_used: string;
  metadata?: string;
}

interface EventLog {
  id: number;
  event_type: string;
  severity: string;
  message: string;
  context?: string;
  created_at: string;
}

interface CommandLog {
  command_name: string;
  parameters?: string;
  user_id?: string;
  guild_id?: string;
  channel_id?: string;
  success: boolean;
  error_message?: string;
  execution_time_ms?: number;
  created_at: string;
}

export default function DatabaseManager() {
  const [stats, setStats] = useState<DatabaseStats>({
    total_configs: 0,
    total_commands: 0,
    total_events: 0,
    total_giveaways: 0,
    total_nitro_codes: 0,
    database_size_kb: 0,
  });

  const [usageStats, setUsageStats] = useState<UsageStat[]>([]);
  const [eventLogs, setEventLogs] = useState<EventLog[]>([]);
  const [commandHistory, setCommandHistory] = useState<CommandLog[]>([]);
  
  const [activeTab, setActiveTab] = useState<'overview' | 'usage' | 'logs' | 'commands' | 'config'>('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);
  // const [isLoading, setIsLoading] = useState(false); // Future: loading states

  // Config management
  const [configCategory, setConfigCategory] = useState('app');
  const [configKey, setConfigKey] = useState('');
  const [configValue, setConfigValue] = useState('');
  const [categoryConfigs, setCategoryConfigs] = useState<[string, string][]>([]);

  // Event logging
  const [logEventType, setLogEventType] = useState('user_action');
  const [logSeverity, setLogSeverity] = useState('INFO');
  const [logMessage, setLogMessage] = useState('');
  const [logContext, setLogContext] = useState('');

  useEffect(() => {
    loadDatabaseStats();
    loadUsageStats();
    loadEventLogs();
    loadCommandHistory();

    // Auto-refresh
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadDatabaseStats();
        if (activeTab === 'usage') loadUsageStats();
        if (activeTab === 'logs') loadEventLogs();
        if (activeTab === 'commands') loadCommandHistory();
      }
    }, 5000); // Toutes les 5 secondes

    return () => clearInterval(interval);
  }, [autoRefresh, activeTab]);

  const loadDatabaseStats = async () => {
    try {
      const dbStats = await invoke<DatabaseStats>('get_database_stats');
      setStats(dbStats);
    } catch (error) {
      console.error('Erreur lors du chargement des stats DB:', error);
    }
  };

  const loadUsageStats = async () => {
    try {
      const usage = await invoke<UsageStat[]>('get_usage_stats', { feature: null });
      setUsageStats(usage);
    } catch (error) {
      console.error('Erreur lors du chargement des stats d\'usage:', error);
    }
  };

  const loadEventLogs = async () => {
    try {
      const logs = await invoke<EventLog[]>('get_recent_logs', { 
        limit: 100, 
        severityFilter: null 
      });
      setEventLogs(logs);
    } catch (error) {
      console.error('Erreur lors du chargement des logs:', error);
    }
  };

  const loadCommandHistory = async () => {
    try {
      const history = await invoke<CommandLog[]>('get_command_history', { 
        limit: 100, 
        userId: null 
      });
      setCommandHistory(history);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
    }
  };

  const loadCategoryConfigs = async () => {
    try {
      const configs = await invoke<[string, string][]>('get_category_configs', { 
        category: configCategory 
      });
      setCategoryConfigs(configs);
    } catch (error) {
      console.error('Erreur lors du chargement des configs:', error);
      toast.error('Erreur lors du chargement des configurations');
    }
  };

  const saveConfig = async () => {
    if (!configKey || !configValue) {
      toast.error('Clé et valeur requises');
      return;
    }

    try {
      await invoke('save_app_config', {
        category: configCategory,
        key: configKey,
        value: configValue,
      });
      
      toast.success('Configuration sauvegardée');
      setConfigKey('');
      setConfigValue('');
      await loadCategoryConfigs();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast.error('Erreur lors de la sauvegarde');
    }
  };

  const logEvent = async () => {
    if (!logMessage) {
      toast.error('Message requis');
      return;
    }

    try {
      await invoke('log_custom_event', {
        eventType: logEventType,
        severity: logSeverity,
        message: logMessage,
        context: logContext || null,
      });
      
      toast.success('Événement enregistré');
      setLogMessage('');
      setLogContext('');
      await loadEventLogs();
    } catch (error) {
      console.error('Erreur lors du log:', error);
      toast.error('Erreur lors de l\'enregistrement');
    }
  };

  const incrementUsage = async (feature: string, action: string) => {
    try {
      await invoke('increment_feature_usage', {
        feature,
        action,
        metadata: null,
      });
      
      toast.success(`Usage incrémenté: ${feature}/${action}`);
      await loadUsageStats();
    } catch (error) {
      console.error('Erreur lors de l\'incrémentation:', error);
      toast.error('Erreur lors de l\'incrémentation');
    }
  };

  const cleanupOldData = async () => {
    try {
      await invoke('cleanup_old_database_data', { daysToKeep: 30 });
      toast.success('Nettoyage effectué (30 jours conservés)');
      await loadDatabaseStats();
    } catch (error) {
      console.error('Erreur lors du nettoyage:', error);
      toast.error('Erreur lors du nettoyage');
    }
  };

  const formatBytes = (kb: number) => {
    if (kb < 1024) return `${kb} KB`;
    const mb = kb / 1024;
    if (mb < 1024) return `${mb.toFixed(1)} MB`;
    const gb = mb / 1024;
    return `${gb.toFixed(2)} GB`;
  };

  // Future: severity color styling
  // const getSeverityColor = (severity: string) => {
  //   switch (severity) {
  //     case 'DEBUG': return 'text-gray-400';
  //     case 'INFO': return 'text-blue-400';
  //     case 'WARNING': return 'text-yellow-400';
  //     case 'ERROR': return 'text-red-400';
  //     case 'CRITICAL': return 'text-red-600';
  //     default: return 'text-gray-400';
  //   }
  // };

  const getSeverityBg = (severity: string) => {
    switch (severity) {
      case 'DEBUG': return 'bg-gray-500';
      case 'INFO': return 'bg-blue-500';
      case 'WARNING': return 'bg-yellow-500';
      case 'ERROR': return 'bg-red-500';
      case 'CRITICAL': return 'bg-red-600';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Base de Données</h1>
          <p className="text-gray-400 mt-1">Gestion et monitoring de la base de données SQLite</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Auto-refresh:</span>
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                autoRefresh ? 'bg-indigo-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  autoRefresh ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
          
          <button
            onClick={cleanupOldData}
            className="px-3 py-1.5 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Nettoyer
          </button>
        </div>
      </div>

      {/* Stats rapides */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.total_configs}</div>
            <div className="text-sm text-gray-400">Configs</div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.total_commands}</div>
            <div className="text-sm text-gray-400">Commandes</div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.total_events}</div>
            <div className="text-sm text-gray-400">Événements</div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.total_giveaways}</div>
            <div className="text-sm text-gray-400">Giveaways</div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.total_nitro_codes}</div>
            <div className="text-sm text-gray-400">Codes Nitro</div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{formatBytes(stats.database_size_kb)}</div>
            <div className="text-sm text-gray-400">Taille DB</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Vue d\'ensemble', icon: '📊' },
            { id: 'usage', label: 'Utilisation', icon: '📈' },
            { id: 'logs', label: 'Logs', icon: '📝' },
            { id: 'commands', label: 'Commandes', icon: '⚡' },
            { id: 'config', label: 'Configuration', icon: '⚙️' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Informations de la base</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Type:</span>
                  <span className="text-white">SQLite</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Taille:</span>
                  <span className="text-white">{formatBytes(stats.database_size_kb)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total entrées:</span>
                  <span className="text-white">
                    {stats.total_configs + stats.total_commands + stats.total_events + stats.total_giveaways + stats.total_nitro_codes}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Actions rapides</h3>
              <div className="space-y-3">
                <button
                  onClick={loadDatabaseStats}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Actualiser les statistiques
                </button>
                
                <button
                  onClick={() => incrementUsage('database', 'manual_test')}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Test d'incrémentation
                </button>
                
                <button
                  onClick={cleanupOldData}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Nettoyer anciennes données
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Usage Tab */}
        {activeTab === 'usage' && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Statistiques d'utilisation</h3>
            
            {usageStats.length === 0 ? (
              <p className="text-gray-400 text-center py-8">Aucune statistique d'utilisation disponible</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-2 text-gray-400">Feature</th>
                      <th className="text-left py-2 text-gray-400">Action</th>
                      <th className="text-right py-2 text-gray-400">Utilisations</th>
                      <th className="text-left py-2 text-gray-400">Dernière utilisation</th>
                    </tr>
                  </thead>
                  <tbody>
                    {usageStats.map((stat, index) => (
                      <tr key={index} className="border-b border-gray-700/50">
                        <td className="py-2 text-white font-medium">{stat.feature}</td>
                        <td className="py-2 text-gray-300">{stat.action}</td>
                        <td className="py-2 text-right text-white">{stat.count}</td>
                        <td className="py-2 text-gray-400">
                          {new Date(stat.last_used).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-6">
            {/* Log Event Form */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Enregistrer un événement</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Type d'événement</label>
                  <input
                    type="text"
                    value={logEventType}
                    onChange={(e) => setLogEventType(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="user_action, system_event, etc."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Sévérité</label>
                  <select
                    value={logSeverity}
                    onChange={(e) => setLogSeverity(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  >
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                    <option value="CRITICAL">CRITICAL</option>
                  </select>
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">Message</label>
                  <input
                    type="text"
                    value={logMessage}
                    onChange={(e) => setLogMessage(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="Description de l'événement"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">Contexte (optionnel)</label>
                  <input
                    type="text"
                    value={logContext}
                    onChange={(e) => setLogContext(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="Contexte additionnel"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <button
                    onClick={logEvent}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  >
                    Enregistrer l'événement
                  </button>
                </div>
              </div>
            </div>

            {/* Event Logs List */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Logs récents</h3>
              
              {eventLogs.length === 0 ? (
                <p className="text-gray-400 text-center py-8">Aucun log disponible</p>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {eventLogs.map((log) => (
                    <div key={log.id} className="bg-gray-700 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded ${getSeverityBg(log.severity)} text-white`}>
                            {log.severity}
                          </span>
                          <span className="text-sm text-gray-400">{log.event_type}</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {new Date(log.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-white text-sm">{log.message}</p>
                      {log.context && (
                        <p className="text-gray-400 text-xs mt-1">Contexte: {log.context}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Commands Tab */}
        {activeTab === 'commands' && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Historique des commandes</h3>
            
            {commandHistory.length === 0 ? (
              <p className="text-gray-400 text-center py-8">Aucun historique de commandes disponible</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-2 text-gray-400">Commande</th>
                      <th className="text-center py-2 text-gray-400">Succès</th>
                      <th className="text-right py-2 text-gray-400">Temps (ms)</th>
                      <th className="text-left py-2 text-gray-400">Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {commandHistory.map((cmd, index) => (
                      <tr key={index} className="border-b border-gray-700/50">
                        <td className="py-2 text-white font-medium">{cmd.command_name}</td>
                        <td className="py-2 text-center">
                          <span className={`px-2 py-1 text-xs rounded ${cmd.success ? 'bg-green-500' : 'bg-red-500'} text-white`}>
                            {cmd.success ? '✓' : '✗'}
                          </span>
                        </td>
                        <td className="py-2 text-right text-gray-300">{cmd.execution_time_ms || 'N/A'}</td>
                        <td className="py-2 text-gray-400">
                          {new Date(cmd.created_at).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Config Tab */}
        {activeTab === 'config' && (
          <div className="space-y-6">
            {/* Save Config Form */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Sauvegarder une configuration</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Catégorie</label>
                  <input
                    type="text"
                    value={configCategory}
                    onChange={(e) => setConfigCategory(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="app, user, etc."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Clé</label>
                  <input
                    type="text"
                    value={configKey}
                    onChange={(e) => setConfigKey(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="theme, language, etc."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Valeur</label>
                  <input
                    type="text"
                    value={configValue}
                    onChange={(e) => setConfigValue(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="dark, fr, etc."
                  />
                </div>
                
                <div className="md:col-span-3 flex space-x-2">
                  <button
                    onClick={saveConfig}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  >
                    Sauvegarder
                  </button>
                  
                  <button
                    onClick={loadCategoryConfigs}
                    className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Charger catégorie
                  </button>
                </div>
              </div>
            </div>

            {/* Category Configs */}
            {categoryConfigs.length > 0 && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  Configurations de la catégorie "{configCategory}"
                </h3>
                <div className="space-y-2">
                  {categoryConfigs.map(([key, value], index) => (
                    <div key={index} className="bg-gray-700 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-white font-medium">{key}</span>
                        <span className="text-gray-300 text-sm">{value}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}