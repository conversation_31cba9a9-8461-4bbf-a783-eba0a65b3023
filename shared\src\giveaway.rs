use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Configuration du Giveaway Joiner
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GiveawayConfig {
    /// Activer/désactiver le joiner
    pub enabled: bool,
    /// <PERSON><PERSON><PERSON> minimum avant de rejoindre (en secondes)
    pub min_delay_seconds: u32,
    /// D<PERSON>lai maximum avant de rejoindre (en secondes)  
    pub max_delay_seconds: u32,
    /// Serveurs à surveiller (vide = tous)
    pub monitored_guilds: Vec<u64>,
    /// Serveurs à ignorer
    pub blacklisted_guilds: Vec<u64>,
    /// Mots-clés requis dans le giveaway
    pub required_keywords: Vec<String>,
    /// Mots-clés à éviter
    pub forbidden_keywords: Vec<String>,
    /// Rejoindre automatiquement sans confirmation
    pub auto_join: bool,
    /// Notification quand un giveaway est détecté
    pub notify_on_detect: bool,
    /// Utiliser des comptes alternatifs si disponibles
    pub use_alt_accounts: bool,
}

impl Default for GiveawayConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            min_delay_seconds: 5,
            max_delay_seconds: 30,
            monitored_guilds: Vec::new(),
            blacklisted_guilds: Vec::new(),
            required_keywords: Vec::new(),
            forbidden_keywords: vec![
                "fake".to_string(),
                "scam".to_string(),
                "nitro generator".to_string(),
            ],
            auto_join: false,
            notify_on_detect: true,
            use_alt_accounts: false,
        }
    }
}

/// Informations sur un giveaway détecté
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GiveawayInfo {
    /// ID unique du giveaway
    pub id: String,
    /// ID du message Discord
    pub message_id: u64,
    /// ID du canal Discord
    pub channel_id: u64,
    /// ID du serveur Discord
    pub guild_id: u64,
    /// Nom du serveur
    pub guild_name: String,
    /// Nom du canal
    pub channel_name: String,
    /// Titre/prix du giveaway
    pub prize: String,
    /// Description complète
    pub description: String,
    /// Émoji de réaction requis
    pub reaction_emoji: String,
    /// Nombre de gagnants
    pub winner_count: u32,
    /// Date de fin
    pub end_time: Option<DateTime<Utc>>,
    /// Organisateur du giveaway
    pub host_user_id: u64,
    /// Nom de l'organisateur
    pub host_username: String,
    /// Statut du giveaway
    pub status: GiveawayStatus,
    /// Timestamp de détection
    pub detected_at: DateTime<Utc>,
    /// Si on a rejoint ce giveaway
    pub joined: bool,
    /// Timestamp de participation
    pub joined_at: Option<DateTime<Utc>>,
    /// Raison si non rejoint
    pub skip_reason: Option<String>,
}

/// Statut d'un giveaway
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum GiveawayStatus {
    /// Giveaway actif
    Active,
    /// Giveaway terminé
    Ended,
    /// Giveaway annulé
    Cancelled,
    /// Giveaway invalide/détection échouée
    Invalid,
}

/// Résultat d'une tentative de participation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JoinResult {
    /// Succès de la participation
    pub success: bool,
    /// Message de résultat
    pub message: String,
    /// ID du giveaway
    pub giveaway_id: String,
    /// Timestamp de la tentative
    pub attempted_at: DateTime<Utc>,
    /// Délai utilisé (en millisecondes)
    pub delay_used_ms: u64,
}

/// Gestionnaire de giveaways
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GiveawayManager {
    /// Configuration actuelle
    pub config: GiveawayConfig,
    /// Giveaways détectés
    pub detected_giveaways: Vec<GiveawayInfo>,
    /// Statistiques
    pub stats: GiveawayStats,
}

/// Statistiques du Giveaway Joiner
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GiveawayStats {
    /// Nombre total de giveaways détectés
    pub total_detected: u32,
    /// Nombre de participations réussies
    pub total_joined: u32,
    /// Nombre de giveaways ignorés
    pub total_skipped: u32,
    /// Nombre de victoires
    pub total_wins: u32,
    /// Dernière activité
    pub last_activity: Option<DateTime<Utc>>,
    /// Taux de succès (%)
    pub success_rate: f32,
}

impl Default for GiveawayStats {
    fn default() -> Self {
        Self {
            total_detected: 0,
            total_joined: 0,
            total_skipped: 0,
            total_wins: 0,
            last_activity: None,
            success_rate: 0.0,
        }
    }
}

impl GiveawayManager {
    pub fn new() -> Self {
        Self {
            config: GiveawayConfig::default(),
            detected_giveaways: Vec::new(),
            stats: GiveawayStats::default(),
        }
    }

    /// Ajouter un nouveau giveaway détecté
    pub fn add_detected_giveaway(&mut self, giveaway: GiveawayInfo) {
        self.detected_giveaways.push(giveaway);
        self.stats.total_detected += 1;
        self.stats.last_activity = Some(Utc::now());
        self.update_success_rate();
        
        // Garder seulement les 100 derniers giveaways
        if self.detected_giveaways.len() > 100 {
            self.detected_giveaways.remove(0);
        }
    }

    /// Marquer un giveaway comme rejoint
    pub fn mark_as_joined(&mut self, giveaway_id: &str) -> bool {
        if let Some(giveaway) = self.detected_giveaways.iter_mut()
            .find(|g| g.id == giveaway_id) {
            giveaway.joined = true;
            giveaway.joined_at = Some(Utc::now());
            self.stats.total_joined += 1;
            self.update_success_rate();
            true
        } else {
            false
        }
    }

    /// Marquer un giveaway comme ignoré
    pub fn mark_as_skipped(&mut self, giveaway_id: &str, reason: String) -> bool {
        if let Some(giveaway) = self.detected_giveaways.iter_mut()
            .find(|g| g.id == giveaway_id) {
            giveaway.skip_reason = Some(reason);
            self.stats.total_skipped += 1;
            true
        } else {
            false
        }
    }

    /// Enregistrer une victoire
    pub fn record_win(&mut self, giveaway_id: &str) {
        if self.detected_giveaways.iter().any(|g| g.id == giveaway_id) {
            self.stats.total_wins += 1;
        }
    }

    /// Mettre à jour le taux de succès
    fn update_success_rate(&mut self) {
        if self.stats.total_detected > 0 {
            self.stats.success_rate = 
                (self.stats.total_joined as f32 / self.stats.total_detected as f32) * 100.0;
        }
    }

    /// Obtenir les giveaways actifs
    pub fn get_active_giveaways(&self) -> Vec<&GiveawayInfo> {
        self.detected_giveaways.iter()
            .filter(|g| g.status == GiveawayStatus::Active)
            .collect()
    }

    /// Obtenir les giveaways rejoints
    pub fn get_joined_giveaways(&self) -> Vec<&GiveawayInfo> {
        self.detected_giveaways.iter()
            .filter(|g| g.joined)
            .collect()
    }

    /// Nettoyer les anciens giveaways
    pub fn cleanup_old_giveaways(&mut self, max_age_days: i64) {
        let cutoff = Utc::now() - chrono::Duration::days(max_age_days);
        self.detected_giveaways.retain(|g| g.detected_at > cutoff);
    }
}

/// Événements du Giveaway Joiner pour le frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GiveawayEvent {
    /// Nouveau giveaway détecté
    GiveawayDetected {
        giveaway: GiveawayInfo,
    },
    /// Participation réussie
    JoinedGiveaway {
        giveaway_id: String,
        delay_ms: u64,
    },
    /// Participation échouée
    JoinFailed {
        giveaway_id: String,
        reason: String,
    },
    /// Giveaway ignoré
    GiveawaySkipped {
        giveaway_id: String,
        reason: String,
    },
    /// Victoire détectée
    GiveawayWon {
        giveaway_id: String,
        prize: String,
    },
    /// Configuration mise à jour
    ConfigUpdated,
}

/// Validations pour le Giveaway Joiner
impl crate::validation::Validate for GiveawayConfig {
    fn validate(&self) -> Result<(), crate::validation::ValidationError> {
        use crate::validation::{IntValidator, StringValidator, DiscordIdValidator};

        // Validation des délais
        IntValidator::new().min(1).max(3600)
            .validate("min_delay_seconds", self.min_delay_seconds as i64)?;
        
        IntValidator::new().min(1).max(3600)
            .validate("max_delay_seconds", self.max_delay_seconds as i64)?;

        if self.min_delay_seconds >= self.max_delay_seconds {
            return Err(crate::validation::ValidationError::InvalidFormat {
                field: "delay_range".to_string(),
                reason: "Le délai minimum doit être inférieur au maximum".to_string(),
            });
        }

        // Validation des guildes surveillées
        for (i, guild_id) in self.monitored_guilds.iter().enumerate() {
            DiscordIdValidator::validate(&format!("monitored_guild_{}", i), &guild_id.to_string())?;
        }

        // Validation des guildes blacklistées
        for (i, guild_id) in self.blacklisted_guilds.iter().enumerate() {
            DiscordIdValidator::validate(&format!("blacklisted_guild_{}", i), &guild_id.to_string())?;
        }

        // Validation des mots-clés requis
        for (i, keyword) in self.required_keywords.iter().enumerate() {
            StringValidator::new()
                .required()
                .max_length(100)
                .forbidden_chars("\0\t\n\r<>\"'&")
                .validate(&format!("required_keyword_{}", i), keyword)?;
        }

        // Validation des mots-clés interdits
        for (i, keyword) in self.forbidden_keywords.iter().enumerate() {
            StringValidator::new()
                .required()
                .max_length(100)
                .forbidden_chars("\0\t\n\r<>\"'&")
                .validate(&format!("forbidden_keyword_{}", i), keyword)?;
        }

        Ok(())
    }
}