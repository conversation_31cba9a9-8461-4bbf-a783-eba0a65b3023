import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Palette,
  Eye,
  Download,
  Upload,
  RefreshCw,
  Save,
  Undo,
  Copy,
  Check,
  Star,
} from 'lucide-react';

/**
 * Configuration complète d'un thème VoidBot
 * Contient tous les paramètres visuels pour personnaliser l'apparence de l'application
 */
interface ThemeConfig {
  /** Nom d'affichage du thème */
  name: string;
  
  /** Palette de couleurs principales et d'état */
  colors: {
    /** Couleur primaire pour les éléments principaux (boutons, liens) */
    primary: string;
    /** Couleur secondaire pour les éléments de support */
    secondary: string;
    /** Couleur d'accent pour mettre en évidence des éléments */
    accent: string;
    /** Couleur d'arrière-plan principal de l'application */
    background: string;
    /** Couleur des surfaces (cartes, modales, panneaux) */
    surface: string;
    /** Couleur du texte principal */
    text: string;
    /** Couleur du texte secondaire (descriptions, sous-titres) */
    textSecondary: string;
    /** Couleur pour les indicateurs de succès */
    success: string;
    /** Couleur pour les avertissements */
    warning: string;
    /** Couleur pour les erreurs */
    error: string;
    /** Couleur des bordures et séparateurs */
    border: string;
  };
  
  /** Gradients CSS pour les éléments avec dégradés */
  gradients: {
    /** Gradient primaire pour les boutons et éléments principaux */
    primary: string;
    /** Gradient secondaire pour les éléments de support */
    secondary: string;
    /** Gradient d'accent pour les éléments mis en évidence */
    accent: string;
    /** Gradient d'arrière-plan pour les grandes surfaces */
    background: string;
  };
  
  /** Ombres CSS pour la profondeur et l'élévation */
  shadows: {
    /** Ombre légère pour les éléments peu élevés */
    small: string;
    /** Ombre moyenne pour les cartes et modales */
    medium: string;
    /** Ombre importante pour les éléments flottants */
    large: string;
    /** Ombre lumineuse pour les effets de glow */
    glow: string;
  };
  
  /** Rayons de bordure pour la cohérence des formes arrondies */
  borderRadius: {
    /** Petit rayon pour les petits éléments */
    small: string;
    /** Rayon moyen pour la plupart des éléments */
    medium: string;
    /** Grand rayon pour les grandes cartes et conteneurs */
    large: string;
  };
  
  /** Paramètres d'animation pour les transitions */
  animations: {
    /** Durée des transitions CSS (ex: "200ms") */
    duration: string;
    /** Fonction d'accélération CSS (ex: "ease-in-out") */
    easing: string;
    /** Facteur d'échelle pour les effets hover */
    scale: number;
  };
  
  /** Indique si c'est un thème par défaut (non modifiable) */
  isDefault: boolean;
  /** Date de création au format ISO */
  createdAt: string;
  /** Date de dernière modification au format ISO */
  lastModified: string;
}

/**
 * Thème prédéfini avec métadonnées d'affichage
 * Utilisé pour organiser et présenter les thèmes dans l'interface
 */
interface PresetTheme {
  /** Identifiant unique du thème */
  id: string;
  /** Nom d'affichage du thème */
  name: string;
  /** Description courte du thème et de son style */
  description: string;
  /** CSS gradient pour l'aperçu visuel dans la liste */
  preview: string;
  /** Catégorie du thème pour l'organisation */
  category: 'builtin' | 'community' | 'custom';
  /** Configuration complète du thème */
  config: ThemeConfig;
}

const defaultThemes: PresetTheme[] = [
  {
    id: 'cyberpunk',
    name: 'Cyberpunk',
    description: 'Thème néon futuriste avec gradients violets et cyan',
    preview: 'linear-gradient(135deg, #6366f1, #8b5cf6, #22d3ee)',
    category: 'builtin',
    config: {
      name: 'Cyberpunk',
      colors: {
        primary: '#6366f1',
        secondary: '#8b5cf6',
        accent: '#22d3ee',
        background: '#0a0a0a',
        surface: '#1a1a1a',
        text: '#ffffff',
        textSecondary: '#a3a3a3',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        border: '#374151'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #6366f1, #8b5cf6)',
        secondary: 'linear-gradient(135deg, #8b5cf6, #ec4899)',
        accent: 'linear-gradient(135deg, #22d3ee, #06b6d4)',
        background: 'linear-gradient(135deg, #0a0a0a, #1a1a1a)'
      },
      shadows: {
        small: '0 1px 3px rgba(99, 102, 241, 0.12)',
        medium: '0 4px 6px rgba(99, 102, 241, 0.15)',
        large: '0 10px 25px rgba(99, 102, 241, 0.2)',
        glow: '0 0 20px rgba(99, 102, 241, 0.4)'
      },
      borderRadius: {
        small: '0.375rem',
        medium: '0.5rem',
        large: '0.75rem'
      },
      animations: {
        duration: '200ms',
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        scale: 1.05
      },
      isDefault: false,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }
  },
  {
    id: 'minimal',
    name: 'Minimal',
    description: 'Design épuré avec tons neutres et contrastes subtils',
    preview: 'linear-gradient(135deg, #f8fafc, #e2e8f0, #64748b)',
    category: 'builtin',
    config: {
      name: 'Minimal',
      colors: {
        primary: '#64748b',
        secondary: '#475569',
        accent: '#0ea5e9',
        background: '#f8fafc',
        surface: '#ffffff',
        text: '#1e293b',
        textSecondary: '#64748b',
        success: '#059669',
        warning: '#d97706',
        error: '#dc2626',
        border: '#e2e8f0'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #64748b, #475569)',
        secondary: 'linear-gradient(135deg, #475569, #334155)',
        accent: 'linear-gradient(135deg, #0ea5e9, #0284c7)',
        background: 'linear-gradient(135deg, #f8fafc, #f1f5f9)'
      },
      shadows: {
        small: '0 1px 3px rgba(0, 0, 0, 0.1)',
        medium: '0 4px 6px rgba(0, 0, 0, 0.1)',
        large: '0 10px 25px rgba(0, 0, 0, 0.15)',
        glow: '0 0 20px rgba(14, 165, 233, 0.3)'
      },
      borderRadius: {
        small: '0.25rem',
        medium: '0.375rem',
        large: '0.5rem'
      },
      animations: {
        duration: '150ms',
        easing: 'ease-in-out',
        scale: 1.02
      },
      isDefault: false,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Thème inspiré de Discord avec bleu/violet familier',
    preview: 'linear-gradient(135deg, #5865f2, #7289da, #99aab5)',
    category: 'builtin',
    config: {
      name: 'Discord',
      colors: {
        primary: '#5865f2',
        secondary: '#7289da',
        accent: '#00aff4',
        background: '#2c2f33',
        surface: '#36393f',
        text: '#ffffff',
        textSecondary: '#99aab5',
        success: '#43b581',
        warning: '#faa61a',
        error: '#f04747',
        border: '#40444b'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #5865f2, #7289da)',
        secondary: 'linear-gradient(135deg, #7289da, #99aab5)',
        accent: 'linear-gradient(135deg, #00aff4, #0099e1)',
        background: 'linear-gradient(135deg, #2c2f33, #36393f)'
      },
      shadows: {
        small: '0 1px 3px rgba(0, 0, 0, 0.3)',
        medium: '0 4px 6px rgba(0, 0, 0, 0.3)',
        large: '0 10px 25px rgba(0, 0, 0, 0.4)',
        glow: '0 0 20px rgba(88, 101, 242, 0.4)'
      },
      borderRadius: {
        small: '0.25rem',
        medium: '0.375rem',
        large: '0.5rem'
      },
      animations: {
        duration: '200ms',
        easing: 'ease-in-out',
        scale: 1.05
      },
      isDefault: false,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }
  },
  {
    id: 'nighty',
    name: 'Nighty',
    description: 'Thème inspiré de Nighty avec violet sombre et accents roses',
    preview: 'linear-gradient(135deg, #4c1d95, #7c3aed, #ec4899)',
    category: 'builtin',
    config: {
      name: 'Nighty',
      colors: {
        primary: '#7c3aed',
        secondary: '#4c1d95',
        accent: '#ec4899',
        background: '#0f0f23',
        surface: '#1a1a2e',
        text: '#ffffff',
        textSecondary: '#a78bfa',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        border: '#374151'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #7c3aed, #4c1d95)',
        secondary: 'linear-gradient(135deg, #4c1d95, #312e81)',
        accent: 'linear-gradient(135deg, #ec4899, #be185d)',
        background: 'linear-gradient(135deg, #0f0f23, #1a1a2e)'
      },
      shadows: {
        small: '0 1px 3px rgba(124, 58, 237, 0.12)',
        medium: '0 4px 6px rgba(124, 58, 237, 0.15)',
        large: '0 10px 25px rgba(124, 58, 237, 0.2)',
        glow: '0 0 20px rgba(124, 58, 237, 0.4)'
      },
      borderRadius: {
        small: '0.5rem',
        medium: '0.75rem',
        large: '1rem'
      },
      animations: {
        duration: '250ms',
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        scale: 1.1
      },
      isDefault: false,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }
  },
  {
    id: 'custom',
    name: 'Custom',
    description: 'Thème personnalisé - commencez ici pour créer le vôtre',
    preview: 'linear-gradient(135deg, #6b7280, #9ca3af, #d1d5db)',
    category: 'custom',
    config: {
      name: 'Custom',
      colors: {
        primary: '#6b7280',
        secondary: '#4b5563',
        accent: '#06b6d4',
        background: '#111827',
        surface: '#1f2937',
        text: '#f9fafb',
        textSecondary: '#9ca3af',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        border: '#374151'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #6b7280, #4b5563)',
        secondary: 'linear-gradient(135deg, #4b5563, #374151)',
        accent: 'linear-gradient(135deg, #06b6d4, #0891b2)',
        background: 'linear-gradient(135deg, #111827, #1f2937)'
      },
      shadows: {
        small: '0 1px 3px rgba(0, 0, 0, 0.12)',
        medium: '0 4px 6px rgba(0, 0, 0, 0.15)',
        large: '0 10px 25px rgba(0, 0, 0, 0.2)',
        glow: '0 0 20px rgba(6, 182, 212, 0.3)'
      },
      borderRadius: {
        small: '0.375rem',
        medium: '0.5rem',
        large: '0.75rem'
      },
      animations: {
        duration: '200ms',
        easing: 'ease-in-out',
        scale: 1.05
      },
      isDefault: false,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }
  }
];

/**
 * Composant de sélection de couleur avec validation
 * Prend en charge les formats hex, rgb et rgba avec aperçu en temps réel
 * 
 * @param props.label - Libellé affiché au-dessus du sélecteur
 * @param props.value - Valeur actuelle de la couleur (hex, rgb, rgba)
 * @param props.onChange - Callback appelé quand la couleur change (seulement si valide)
 * @param props.className - Classes CSS supplémentaires
 */
const ColorPicker = ({ label, value, onChange, className = '' }: {
  label: string;
  value: string;
  onChange: (color: string) => void;
  className?: string;
}) => {
  const [tempValue, setTempValue] = useState(value);
  const [isValid, setIsValid] = useState(true);

  /**
   * Valide un format de couleur CSS
   * Supporte les formats hex (#fff, #ffffff), rgb() et rgba()
   * 
   * @param color - Chaîne de couleur à valider
   * @returns true si le format est valide
   */
  const validateColor = (color: string) => {
    if (!color || typeof color !== 'string') return false;
    
    // Validation HEX
    const isValidHex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
    if (isValidHex) return true;
    
    // Validation RGB avec valeurs 0-255
    const rgbMatch = color.match(/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/);
    if (rgbMatch) {
      const isValidRgb = rgbMatch.slice(1).every(val => {
        const num = parseInt(val);
        return num >= 0 && num <= 255;
      });
      if (isValidRgb) return true;
    }
    
    // Validation RGBA avec valeurs 0-255 pour RGB et 0-1 pour alpha
    const rgbaMatch = color.match(/^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([01]?\.?\d*)\s*\)$/);
    if (rgbaMatch) {
      const rgbValid = rgbaMatch.slice(1, 4).every(val => {
        const num = parseInt(val);
        return num >= 0 && num <= 255;
      });
      const alpha = parseFloat(rgbaMatch[4]);
      const alphaValid = !isNaN(alpha) && alpha >= 0 && alpha <= 1;
      
      if (rgbValid && alphaValid) return true;
    }
    
    return false;
  };

  const handleChange = (newValue: string) => {
    setTempValue(newValue);
    const valid = validateColor(newValue);
    setIsValid(valid);
    if (valid) {
      onChange(newValue);
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="text-sm font-medium text-gray-300">{label}</label>
      <div className="flex items-center space-x-3">
        <div 
          className="w-10 h-10 rounded-lg border-2 border-gray-600 cursor-pointer transition-all hover:scale-105"
          style={{ backgroundColor: isValid ? tempValue : '#ef4444' }}
          title={isValid ? tempValue : 'Couleur invalide'}
        />
        <input
          type="text"
          value={tempValue}
          onChange={(e) => handleChange(e.target.value)}
          className={`flex-1 px-3 py-2 bg-gray-700 border rounded-lg text-white placeholder-gray-400 transition-colors ${
            isValid ? 'border-gray-600 focus:border-blue-500' : 'border-red-500 focus:border-red-400'
          }`}
          placeholder="#6366f1 ou rgb(99, 102, 241)"
        />
        <input
          type="color"
          value={value.startsWith('#') ? value : '#6366f1'}
          onChange={(e) => handleChange(e.target.value)}
          className="w-10 h-10 bg-transparent border-none cursor-pointer"
          title="Sélecteur de couleur"
        />
      </div>
    </div>
  );
};

/**
 * Carte de thème prédéfini avec aperçu et actions
 * Affiche un aperçu visuel du thème avec ses métadonnées
 * 
 * @param props.preset - Configuration du thème à afficher
 * @param props.isSelected - Indique si ce thème est actuellement sélectionné
 * @param props.onSelect - Callback appelé lors de la sélection
 * @param props.onApply - Callback appelé pour appliquer le thème
 */
const PresetCard = ({ preset, isSelected, onSelect, onApply }: {
  preset: PresetTheme;
  isSelected: boolean;
  onSelect: () => void;
  onApply: () => void;
}) => {
  /**
   * Retourne l'icône emoji correspondant à une catégorie de thème
   * 
   * @param category - Catégorie du thème (builtin, community, custom)
   * @returns Emoji représentant la catégorie
   */
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'builtin': return '⭐'; // Thèmes intégrés officiels
      case 'community': return '👥'; // Thèmes créés par la communauté
      case 'custom': return '🎨'; // Thèmes personnalisés utilisateur
      default: return '📦'; // Fallback pour catégories inconnues
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      className={`relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-4 border cursor-pointer transition-all ${
        isSelected 
          ? 'border-blue-500 ring-2 ring-blue-500/20 shadow-lg shadow-blue-500/10' 
          : 'border-gray-700 hover:border-gray-600'
      }`}
      onClick={onSelect}
    >
      {/* Preview */}
      <div 
        className="w-full h-20 rounded-lg mb-3 border border-gray-600"
        style={{ background: preset.preview }}
      />
      
      {/* Info */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-semibold text-white flex items-center space-x-2">
            <span>{getCategoryIcon(preset.category)}</span>
            <span>{preset.name}</span>
          </h4>
          {isSelected && <Check className="w-5 h-5 text-blue-500" />}
        </div>
        
        <p className="text-sm text-gray-400 line-clamp-2">{preset.description}</p>
        
        <div className="flex items-center justify-between pt-2">
          <span className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded-full capitalize">
            {preset.category}
          </span>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={(e) => {
              e.stopPropagation();
              onApply();
            }}
            className="text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-full hover:shadow-lg transition-all"
          >
            Appliquer
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

/**
 * Panneau de prévisualisation en temps réel d'un thème
 * Affiche des composants d'exemple avec le thème appliqué
 * 
 * @param props.config - Configuration du thème à prévisualiser
 */
const PreviewPanel = ({ config }: { config: ThemeConfig }) => {
  return (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold text-white mb-2">Aperçu du Thème</h3>
        <p className="text-sm text-gray-400">Prévisualisation des composants avec votre thème</p>
      </div>
      
      {/* Background Preview */}
      <div 
        className="w-full h-32 rounded-xl border flex items-center justify-center"
        style={{ 
          background: config.gradients.background,
          borderColor: config.colors.border
        }}
      >
        <span style={{ color: config.colors.text }} className="font-medium">
          Arrière-plan Principal
        </span>
      </div>
      
      {/* Component Previews */}
      <div className="grid grid-cols-2 gap-4">
        {/* Primary Button */}
        <div 
          className="h-12 rounded-lg flex items-center justify-center font-medium transition-all hover:scale-105"
          style={{ 
            background: config.gradients.primary,
            color: config.colors.text,
            boxShadow: config.shadows.medium
          }}
        >
          Bouton Principal
        </div>
        
        {/* Secondary Button */}
        <div 
          className="h-12 rounded-lg flex items-center justify-center font-medium transition-all hover:scale-105"
          style={{ 
            background: config.gradients.secondary,
            color: config.colors.text,
            boxShadow: config.shadows.medium
          }}
        >
          Bouton Secondaire
        </div>
        
        {/* Surface Card */}
        <div 
          className="col-span-2 p-4 rounded-lg border"
          style={{ 
            backgroundColor: config.colors.surface,
            borderColor: config.colors.border,
            boxShadow: config.shadows.small
          }}
        >
          <h4 style={{ color: config.colors.text }} className="font-semibold mb-2">
            Carte Surface
          </h4>
          <p style={{ color: config.colors.textSecondary }} className="text-sm">
            Texte secondaire dans une surface avec bordures arrondies
          </p>
        </div>
        
        {/* Status Colors */}
        <div className="col-span-2 grid grid-cols-3 gap-2">
          <div 
            className="h-10 rounded-lg flex items-center justify-center text-white text-sm font-medium"
            style={{ backgroundColor: config.colors.success }}
          >
            Succès
          </div>
          <div 
            className="h-10 rounded-lg flex items-center justify-center text-white text-sm font-medium"
            style={{ backgroundColor: config.colors.warning }}
          >
            Attention
          </div>
          <div 
            className="h-10 rounded-lg flex items-center justify-center text-white text-sm font-medium"
            style={{ backgroundColor: config.colors.error }}
          >
            Erreur
          </div>
        </div>
        
        {/* Accent Elements */}
        <div 
          className="col-span-2 h-16 rounded-lg flex items-center justify-center font-medium"
          style={{ 
            background: config.gradients.accent,
            color: config.colors.text,
            boxShadow: config.shadows.glow
          }}
        >
          Élément Accent avec Glow
        </div>
      </div>
    </div>
  );
};

/**
 * Constructeur de thèmes VoidBot - Interface complète de personnalisation
 * 
 * Fonctionnalités principales :
 * - Sélection de thèmes prédéfinis (Cyberpunk, Discord, Nighty, etc.)
 * - Éditeur de couleurs avec validation en temps réel
 * - Configuration des gradients et ombres CSS
 * - Aperçu instantané des modifications
 * - Export/Import de thèmes
 * - Sauvegarde et application des thèmes
 * 
 * Architecture :
 * - Interface à onglets pour organiser les paramètres
 * - Prévisualisation en temps réel sur la droite
 * - Validation des couleurs et formats CSS
 * - Gestion des états de modification non sauvegardés
 * 
 * @returns Composant React du constructeur de thèmes
 */
export function ThemeBuilder() {
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(defaultThemes[0].config);
  const [selectedPreset, setSelectedPreset] = useState<string>(defaultThemes[0].id);
  const [customThemes, setCustomThemes] = useState<PresetTheme[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<'presets' | 'colors' | 'gradients' | 'shadows' | 'preview'>('presets');
  
  useEffect(() => {
    loadCustomThemes();
    loadCurrentTheme();
  }, []);
  
  const loadCustomThemes = async () => {
    try {
      const themes = await invoke<PresetTheme[]>('get_custom_themes');
      setCustomThemes(themes);
    } catch (error) {
      console.error('Erreur chargement thèmes custom:', error);
    }
  };
  
  const loadCurrentTheme = async () => {
    try {
      const theme = await invoke<ThemeConfig>('get_current_theme');
      if (theme) {
        setCurrentTheme(theme);
      }
    } catch (error) {
      console.error('Erreur chargement thème actuel:', error);
    }
  };
  
  const saveTheme = async () => {
    setIsLoading(true);
    try {
      await invoke('save_theme', { theme: currentTheme });
      setHasUnsavedChanges(false);
      toast.success('Thème sauvegardé avec succès!');
    } catch (error) {
      console.error('Erreur sauvegarde thème:', error);
      toast.error('Erreur lors de la sauvegarde');
    } finally {
      setIsLoading(false);
    }
  };
  
  const applyTheme = async () => {
    setIsLoading(true);
    try {
      await invoke('apply_theme', { theme: currentTheme });
      toast.success('Thème appliqué avec succès!');
    } catch (error) {
      console.error('Erreur application thème:', error);
      toast.error('Erreur lors de l\'application');
    } finally {
      setIsLoading(false);
    }
  };
  
  const exportTheme = async () => {
    try {
      const exported = await invoke<string>('export_theme', { theme: currentTheme });
      navigator.clipboard.writeText(exported);
      toast.success('Thème copié dans le presse-papiers!');
    } catch (error) {
      console.error('Erreur export thème:', error);
      toast.error('Erreur lors de l\'export');
    }
  };
  
  const importTheme = async () => {
    try {
      const text = await navigator.clipboard.readText();
      const imported = await invoke<ThemeConfig>('import_theme', { themeData: text });
      setCurrentTheme(imported);
      setHasUnsavedChanges(true);
      toast.success('Thème importé avec succès!');
    } catch (error) {
      console.error('Erreur import thème:', error);
      toast.error('Erreur lors de l\'import - vérifiez le format');
    }
  };
  
  const resetToDefault = () => {
    setCurrentTheme(defaultThemes[0].config);
    setSelectedPreset(defaultThemes[0].id);
    setHasUnsavedChanges(true);
    toast.success('Thème réinitialisé!');
  };
  
  /**
   * Met à jour une propriété spécifique du thème en utilisant un chemin en notation pointée
   * Supporte les chemins imbriqués comme "colors.primary" ou "shadows.glow"
   * 
   * @param path - Chemin vers la propriété (ex: "colors.primary", "animations.duration")
   * @param value - Nouvelle valeur à assigner
   */
  const updateThemeProperty = (path: string, value: any) => {
    setCurrentTheme(prev => {
      const newTheme = { ...prev };
      const keys = path.split('.');
      let current: any = newTheme;
      
      // Naviguer jusqu'au parent de la propriété finale
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      // Assigner la nouvelle valeur
      current[keys[keys.length - 1]] = value;
      newTheme.lastModified = new Date().toISOString();
      
      return newTheme;
    });
    setHasUnsavedChanges(true);
  };
  
  const applyPreset = (preset: PresetTheme) => {
    setCurrentTheme(preset.config);
    setSelectedPreset(preset.id);
    setHasUnsavedChanges(true);
    toast.success(`Thème "${preset.name}" appliqué!`);
  };
  
  const allPresets = [...defaultThemes, ...customThemes];
  
  const tabButtons = [
    { id: 'presets', label: 'Préréglages', icon: Star },
    { id: 'colors', label: 'Couleurs', icon: Palette },
    { id: 'gradients', label: 'Gradients', icon: Copy },
    { id: 'shadows', label: 'Ombres', icon: Eye },
    { id: 'preview', label: 'Aperçu', icon: Eye }
  ];

  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Palette className="w-8 h-8 text-purple-500" />
          <div>
            <h1 className="text-2xl font-bold text-white">Theme Builder</h1>
            <p className="text-gray-400">Créez et personnalisez l'apparence de VoidBot</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {hasUnsavedChanges && (
            <div className="flex items-center space-x-2 text-yellow-400">
              <RefreshCw className="w-4 h-4" />
              <span className="text-sm">Modifications non sauvegardées</span>
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <button
              onClick={exportTheme}
              className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              title="Exporter le thème"
            >
              <Download className="w-4 h-4" />
            </button>
            
            <button
              onClick={importTheme}
              className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              title="Importer un thème"
            >
              <Upload className="w-4 h-4" />
            </button>
            
            <button
              onClick={resetToDefault}
              className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              title="Réinitialiser"
            >
              <Undo className="w-4 h-4" />
            </button>
            
            <button
              onClick={saveTheme}
              disabled={isLoading || !hasUnsavedChanges}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-lg font-medium hover:shadow-lg transition-all disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4 mr-2 inline" />
              Sauvegarder
            </button>
            
            <button
              onClick={applyTheme}
              disabled={isLoading}
              className="px-4 py-2 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-lg font-medium hover:shadow-lg transition-all disabled:cursor-not-allowed"
            >
              <Check className="w-4 h-4 mr-2 inline" />
              Appliquer
            </button>
          </div>
        </div>
      </div>
      
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-xl">
        {tabButtons.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>
      
      {/* Tab Content */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <AnimatePresence mode="wait">
            {activeTab === 'presets' && (
              <motion.div
                key="presets"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Thèmes Prédéfinis</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                    {allPresets.map((preset) => (
                      <PresetCard
                        key={preset.id}
                        preset={preset}
                        isSelected={selectedPreset === preset.id}
                        onSelect={() => setSelectedPreset(preset.id)}
                        onApply={() => applyPreset(preset)}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
            
            {activeTab === 'colors' && (
              <motion.div
                key="colors"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Couleurs Principales</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ColorPicker
                      label="Couleur Primaire"
                      value={currentTheme.colors.primary}
                      onChange={(color) => updateThemeProperty('colors.primary', color)}
                    />
                    <ColorPicker
                      label="Couleur Secondaire"
                      value={currentTheme.colors.secondary}
                      onChange={(color) => updateThemeProperty('colors.secondary', color)}
                    />
                    <ColorPicker
                      label="Couleur d'Accent"
                      value={currentTheme.colors.accent}
                      onChange={(color) => updateThemeProperty('colors.accent', color)}
                    />
                    <ColorPicker
                      label="Arrière-plan"
                      value={currentTheme.colors.background}
                      onChange={(color) => updateThemeProperty('colors.background', color)}
                    />
                    <ColorPicker
                      label="Surface"
                      value={currentTheme.colors.surface}
                      onChange={(color) => updateThemeProperty('colors.surface', color)}
                    />
                    <ColorPicker
                      label="Texte Principal"
                      value={currentTheme.colors.text}
                      onChange={(color) => updateThemeProperty('colors.text', color)}
                    />
                    <ColorPicker
                      label="Texte Secondaire"
                      value={currentTheme.colors.textSecondary}
                      onChange={(color) => updateThemeProperty('colors.textSecondary', color)}
                    />
                    <ColorPicker
                      label="Bordures"
                      value={currentTheme.colors.border}
                      onChange={(color) => updateThemeProperty('colors.border', color)}
                    />
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Couleurs d'État</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <ColorPicker
                      label="Succès"
                      value={currentTheme.colors.success}
                      onChange={(color) => updateThemeProperty('colors.success', color)}
                    />
                    <ColorPicker
                      label="Attention"
                      value={currentTheme.colors.warning}
                      onChange={(color) => updateThemeProperty('colors.warning', color)}
                    />
                    <ColorPicker
                      label="Erreur"
                      value={currentTheme.colors.error}
                      onChange={(color) => updateThemeProperty('colors.error', color)}
                    />
                  </div>
                </div>
              </motion.div>
            )}
            
            {activeTab === 'gradients' && (
              <motion.div
                key="gradients"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Gradients</h3>
                  <div className="space-y-4">
                    {Object.entries(currentTheme.gradients).map(([key, value]) => (
                      <div key={key} className="space-y-2">
                        <label className="text-sm font-medium text-gray-300 capitalize">
                          Gradient {key}
                        </label>
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-16 h-10 rounded-lg border-2 border-gray-600"
                            style={{ background: value }}
                          />
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => updateThemeProperty(`gradients.${key}`, e.target.value)}
                            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 transition-colors"
                            placeholder="linear-gradient(135deg, #6366f1, #8b5cf6)"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
            
            {activeTab === 'shadows' && (
              <motion.div
                key="shadows"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Ombres et Effets</h3>
                  <div className="space-y-4">
                    {Object.entries(currentTheme.shadows).map(([key, value]) => (
                      <div key={key} className="space-y-2">
                        <label className="text-sm font-medium text-gray-300 capitalize">
                          Ombre {key}
                        </label>
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-16 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg"
                            style={{ boxShadow: value }}
                          />
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => updateThemeProperty(`shadows.${key}`, e.target.value)}
                            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 transition-colors"
                            placeholder="0 4px 6px rgba(0, 0, 0, 0.1)"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Bordures Arrondies</h3>
                  <div className="grid grid-cols-3 gap-4">
                    {Object.entries(currentTheme.borderRadius).map(([key, value]) => (
                      <div key={key} className="space-y-2">
                        <label className="text-sm font-medium text-gray-300 capitalize">
                          {key}
                        </label>
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => updateThemeProperty(`borderRadius.${key}`, e.target.value)}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 transition-colors"
                          placeholder="0.5rem"
                        />
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Animations</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300">Durée</label>
                      <input
                        type="text"
                        value={currentTheme.animations.duration}
                        onChange={(e) => updateThemeProperty('animations.duration', e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 transition-colors"
                        placeholder="200ms"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300">Easing</label>
                      <input
                        type="text"
                        value={currentTheme.animations.easing}
                        onChange={(e) => updateThemeProperty('animations.easing', e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 transition-colors"
                        placeholder="ease-in-out"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300">Scale (hover)</label>
                      <input
                        type="number"
                        step="0.01"
                        min="1"
                        max="2"
                        value={currentTheme.animations.scale}
                        onChange={(e) => updateThemeProperty('animations.scale', parseFloat(e.target.value))}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 transition-colors"
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
            
            {activeTab === 'preview' && (
              <motion.div
                key="preview"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <PreviewPanel config={currentTheme} />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        {/* Right Panel - Always show preview */}
        <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700">
          <PreviewPanel config={currentTheme} />
        </div>
      </div>
    </div>
  );
}