#!/usr/bin/env python3
"""
Script simple de génération d'assets pour VoidBot
Génère des fichiers BMP basiques sans dépendances externes
"""

import os
import struct

def create_bmp_header(width, height):
    """Crée un en-tête BMP 24-bit"""
    # Calcul de la taille
    row_size = ((width * 3 + 3) // 4) * 4  # Padding pour alignement 32-bit
    pixel_data_size = row_size * height
    file_size = 54 + pixel_data_size  # 54 = taille en-tête BMP
    
    # En-tête fichier BMP (14 bytes)
    file_header = struct.pack('<2sI2H',
        b'BM',          # Signature
        file_size,      # Taille fichier
        0,              # Réservé
        54              # Offset données pixels
    )
    
    # En-tête info DIB (40 bytes)
    info_header = struct.pack('<IIIHHIIIIII',
        40,             # Taille en-tête info
        width,          # Largeur
        height,         # Hauteur
        1,              # Plans de couleur
        24,             # Bits par pixel
        0,              # Compression (aucune)
        pixel_data_size, # Taille données pixels
        2835,           # Résolution X (72 DPI)
        2835,           # Résolution Y (72 DPI)
        0,              # Couleurs dans palette
        0               # Couleurs importantes
    )
    
    return file_header + info_header, row_size

def create_gradient_bmp(width, height, color1, color2, filename):
    """Crée un fichier BMP avec dégradé vertical"""
    header, row_size = create_bmp_header(width, height)
    
    with open(filename, 'wb') as f:
        f.write(header)
        
        # Générer les pixels (de bas en haut pour BMP)
        for y in range(height - 1, -1, -1):
            row_data = bytearray()
            
            # Interpolation couleur
            ratio = y / height if height > 1 else 0
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            
            # Ajouter pixels pour cette ligne (BGR format)
            for x in range(width):
                row_data.extend([b, g, r])
            
            # Padding pour alignement 32-bit
            while len(row_data) < row_size:
                row_data.append(0)
                
            f.write(row_data)

def create_solid_bmp(width, height, color, filename):
    """Crée un fichier BMP couleur unie"""
    create_gradient_bmp(width, height, color, color, filename)

def main():
    assets_dir = "assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    print("🎨 Génération d'assets basiques pour VoidBot...")
    
    # Couleurs VoidBot
    dark_gray = (17, 24, 39)      # gray-900
    medium_gray = (31, 41, 55)    # gray-800
    light_gray = (55, 65, 81)     # gray-700
    indigo = (139, 92, 246)       # indigo-500
    
    try:
        # Header image NSIS (497x58)
        print("📄 Création header-image.bmp (497x58)...")
        create_gradient_bmp(497, 58, dark_gray, medium_gray, 
                          os.path.join(assets_dir, "header-image.bmp"))
        
        # Sidebar image NSIS (164x314)
        print("📄 Création sidebar-image.bmp (164x314)...")
        create_gradient_bmp(164, 314, dark_gray, light_gray,
                          os.path.join(assets_dir, "sidebar-image.bmp"))
        
        # Banner WiX (493x58)
        print("📄 Création banner.bmp (493x58)...")
        create_gradient_bmp(493, 58, dark_gray, medium_gray,
                          os.path.join(assets_dir, "banner.bmp"))
        
        # Dialog WiX (493x312)
        print("📄 Création dialog.bmp (493x312)...")  
        create_gradient_bmp(493, 312, dark_gray, medium_gray,
                          os.path.join(assets_dir, "dialog.bmp"))
        
        print("✅ Assets basiques générés avec succès!")
        print(f"📁 Fichiers créés dans {assets_dir}/:")
        
        for file in os.listdir(assets_dir):
            if file.endswith('.bmp'):
                size = os.path.getsize(os.path.join(assets_dir, file))
                print(f"   - {file} ({size:,} bytes)")
                
        print("\n💡 Note: Assets basiques générés sans texte.")
        print("   Pour des assets avec logo/texte, installer Pillow et utiliser build-assets.py")
        
    except Exception as e:
        print(f"❌ Erreur lors de la génération: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())