<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoidBot Overlay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent;
            font-family: 'Segoe UI', system-ui, sans-serif;
            cursor: none;
            pointer-events: none;
            overflow: hidden;
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
            background: transparent;
        }

        /* Indicateur de statut VoidBot dans un coin */
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(15, 15, 20, 0.9);
            color: #a78bfa;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(99, 102, 241, 0.3);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .status-indicator.visible {
            opacity: 1;
        }

        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #22d3ee;
            border-radius: 50%;
            margin-right: 6px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Message de debug (visible uniquement en développement) */
        .debug-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(15, 15, 20, 0.8);
            color: #9ca3af;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 999;
        }

        .debug-info.visible {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <!-- Indicateur de statut -->
        <div class="status-indicator" id="statusIndicator">
            <span class="status-dot"></span>
            VoidBot Overlay Actif
        </div>

        <!-- Informations de debug -->
        <div class="debug-info" id="debugInfo">
            Overlay prêt - En attente de clic droit...
        </div>
    </div>

    <script>
        console.log('VoidBot Overlay initialisé');

        // Variables globales
        let overlayActive = true;
        let debugMode = false;

        // Éléments DOM
        const statusIndicator = document.getElementById('statusIndicator');
        const debugInfo = document.getElementById('debugInfo');

        // Afficher l'indicateur pendant 3 secondes au démarrage
        function showStatusIndicator() {
            statusIndicator.classList.add('visible');
            setTimeout(() => {
                statusIndicator.classList.remove('visible');
            }, 3000);
        }

        // Debug: afficher/masquer les infos de debug
        function toggleDebugMode() {
            debugMode = !debugMode;
            if (debugMode) {
                debugInfo.classList.add('visible');
                debugInfo.textContent = `Overlay: ${overlayActive ? 'Actif' : 'Inactif'} | Debug: ON`;
            } else {
                debugInfo.classList.remove('visible');
            }
        }

        // Gestion des événements clavier pour debug
        document.addEventListener('keydown', (e) => {
            // Ctrl + Shift + D pour toggle debug
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                toggleDebugMode();
            }
            
            // Escape pour masquer l'overlay
            if (e.key === 'Escape') {
                hideOverlay();
            }
        });

        // Communication avec Tauri
        async function hideOverlay() {
            try {
                if (window.__TAURI__) {
                    await window.__TAURI__.tauri.invoke('destroy_overlay_window');
                }
            } catch (error) {
                console.error('Erreur masquage overlay:', error);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Overlay DOM chargé');
            showStatusIndicator();
            
            // Message de confirmation après 1 seconde
            setTimeout(() => {
                console.log('VoidBot Overlay complètement initialisé et prêt à détecter les clics droits');
            }, 1000);
        });

        // Gestion des erreurs
        window.addEventListener('error', (e) => {
            console.error('Erreur overlay:', e.error);
        });

        // Nettoyage avant fermeture
        window.addEventListener('beforeunload', () => {
            console.log('Overlay en cours de fermeture');
        });
    </script>
</body>
</html>