import { useAppStore } from '../stores/appStore';

const navigationItems = [
  { id: 'dashboard', label: 'Dashboard', icon: '🏠' },
  { id: 'stealth', label: 'Mode Furtif', icon: '👻' },
  { id: 'notifications', label: 'Notifications', icon: '🔔' },
  { id: 'activity', label: 'Activity Viewer', icon: '🔍' },
  { id: 'login', label: 'Login Manager', icon: '👥' },
  { id: 'variables', label: 'Dynamic Variables', icon: '✨' },
  { id: 'rich-presence', label: 'Rich Presence', icon: '🎮' },
  { id: 'troll', label: 'Troll Control', icon: '💀' },
  { id: 'auto', label: 'Auto-Commands', icon: '🤖' },
  { id: 'system', label: 'Système', icon: '🔧' },
  { id: 'themes', label: 'Theme Builder', icon: '🎨' },
  { id: 'settings', label: 'Paramètres', icon: '⚙️' },
];

export function SimpleSidebar() {
  const { currentPage, setCurrentPage, botStatus, disconnectBot, discordUser, clearSession } = useAppStore();

  return (
    <div className="w-64 bg-voidbot-darker h-full border-r border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h1 className="text-2xl font-bold bg-gradient-to-r from-voidbot-primary to-voidbot-secondary bg-clip-text text-transparent">
          VoidBot
        </h1>
        <div className="mt-2">
          <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
            botStatus === 'online' ? 'bg-green-500/20 text-green-400' :
            botStatus === 'connecting' ? 'bg-yellow-500/20 text-yellow-400' :
            'bg-red-500/20 text-red-400'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              botStatus === 'online' ? 'bg-green-400' :
              botStatus === 'connecting' ? 'bg-yellow-400' :
              'bg-red-400'
            }`} />
            {botStatus === 'online' ? 'En ligne' :
             botStatus === 'connecting' ? 'Connexion...' :
             'Hors ligne'}
          </span>
        </div>
        
        {/* User info si connecté */}
        {discordUser && (
          <div className="px-4 py-2 text-xs text-voidbot-secondary">
            Connecté en tant que <span className="text-voidbot-primary font-medium">{discordUser.username}</span>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2 flex-1">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => setCurrentPage(item.id)}
            className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 ${
              currentPage === item.id
                ? 'bg-voidbot-primary/20 text-voidbot-primary border border-voidbot-primary/30'
                : 'text-voidbot-secondary hover:bg-voidbot-primary/10 hover:text-voidbot-primary'
            }`}
          >
            <span className="text-lg">{item.icon}</span>
            <span className="font-medium">{item.label}</span>
          </button>
        ))}
      </nav>

      {/* Footer avec déconnexion */}
      <div className="p-4 border-t border-gray-700 space-y-2">
        <button
          onClick={() => disconnectBot()}
          className="w-full flex items-center gap-3 p-2 rounded-lg text-red-400 hover:bg-red-500/10 transition-all duration-200"
        >
          <span className="text-lg">🚪</span>
          <span className="font-medium">Déconnexion</span>
        </button>
        
        <button
          onClick={async () => {
            if (confirm('Supprimer la session sauvegardée ? Vous devrez vous reconnecter manuellement au prochain démarrage.')) {
              await clearSession();
              alert('Session supprimée avec succès');
            }
          }}
          className="w-full flex items-center gap-3 p-2 rounded-lg text-orange-400 hover:bg-orange-500/10 transition-all duration-200 text-sm"
        >
          <span className="text-sm">🗑️</span>
          <span className="font-medium">Oublier session</span>
        </button>
      </div>
    </div>
  );
}