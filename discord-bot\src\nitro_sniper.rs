use serenity::all::*;
use voidbot_shared::{NitroSniperManager, NitroCodeInfo, NitroCodeType, NitroCodeStatus, NitroSniperEvent, ClaimResult};
use std::sync::Arc;
use tokio::sync::Mutex;
use regex::Regex;
use rand::Rng;
use chrono::Utc;
use std::time::Instant;

/// Gestionnaire principal du Nitro Sniper ultra-rapide
pub struct NitroSniper {
    manager: Arc<Mutex<NitroSniperManager>>,
    regex_patterns: NitroPatterns,
    claim_client: reqwest::Client,
}

/// Patterns regex ultra-optimisés pour la détection de codes Nitro
struct NitroPatterns {
    /// Pattern principal pour codes Nitro (ultra-optimisé)
    nitro_code_pattern: Regex,
    /// Pattern pour URLs complètes
    nitro_url_pattern: Regex,
    /// Pattern pour codes dans texte
    code_in_text_pattern: Regex,
}

impl NitroPatterns {
    pub fn new() -> Result<Self, regex::Error> {
        Ok(Self {
            // Pattern ultra-optimisé pour détecter codes Nitro rapidement
            nitro_code_pattern: Regex::new(r"(?i)(?:discord\.(?:gift|com/gifts)/|nitro\.gift/)([a-zA-Z0-9]{16,24})")?,
            // Pattern pour URLs complètes
            nitro_url_pattern: Regex::new(r"https?://(?:discord\.(?:gift|com/gifts)/|nitro\.gift/)([a-zA-Z0-9]{16,24})")?,
            // Pattern pour codes bruts dans le texte
            code_in_text_pattern: Regex::new(r"\b([a-zA-Z0-9]{16,24})\b")?,
        })
    }
}

impl NitroSniper {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        // Client HTTP optimisé pour la vitesse
        let claim_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(5))
            .pool_max_idle_per_host(10)
            .pool_idle_timeout(std::time::Duration::from_secs(30))
            .build()?;

        Ok(Self {
            manager: Arc::new(Mutex::new(NitroSniperManager::new())),
            regex_patterns: NitroPatterns::new()?,
            claim_client,
        })
    }

    /// Obtenir le gestionnaire du sniper
    pub fn get_manager(&self) -> Arc<Mutex<NitroSniperManager>> {
        self.manager.clone()
    }

    /// Analyser un message ultra-rapidement pour détecter des codes Nitro
    pub async fn analyze_message_fast(&self, ctx: &Context, msg: &Message) -> Option<Vec<NitroCodeInfo>> {
        let start_time = Instant::now();
        let manager = self.manager.lock().await;
        
        // Vérification ultra-rapide si le sniper est activé
        if !manager.config.enabled {
            return None;
        }

        // Vérifications de filtrage rapides
        if let Some(guild_id) = msg.guild_id {
            let guild_id_u64 = guild_id.get();
            
            // Vérifier blacklist (ultra-rapide)
            if manager.config.blacklisted_guilds.contains(&guild_id_u64) {
                return None;
            }
            
            // Vérifier whitelist si définie
            if !manager.config.monitored_guilds.is_empty() 
                && !manager.config.monitored_guilds.contains(&guild_id_u64) {
                return None;
            }
        }

        // Vérifier blacklist/whitelist des canaux
        let channel_id_u64 = msg.channel_id.get();
        if manager.config.blacklisted_channels.contains(&channel_id_u64) {
            return None;
        }
        
        if !manager.config.monitored_channels.is_empty() 
            && !manager.config.monitored_channels.contains(&channel_id_u64) {
            return None;
        }

        // Vérifier si c'est un bot de confiance
        if manager.config.verified_bots_only && !msg.author.bot {
            return None;
        }

        if !manager.config.trusted_bot_ids.is_empty() 
            && !manager.config.trusted_bot_ids.contains(&msg.author.id.get()) {
            return None;
        }

        // Vérifier l'âge du message (éviter les codes trop anciens)
        if let Some(timestamp) = msg.timestamp.checked_sub_signed(chrono::Duration::minutes(
            manager.config.ignore_old_codes_minutes as i64
        )) {
            if Utc::now() > timestamp {
                return None;
            }
        }

        drop(manager); // Libérer le lock rapidement

        // Analyser le contenu pour les codes (ULTRA-RAPIDE)
        let mut detected_codes = Vec::new();
        let content = &msg.content;

        // Recherche ultra-optimisée des codes Nitro
        self.extract_nitro_codes_fast(ctx, msg, content, &mut detected_codes).await;

        // Analyser les embeds si présents
        for embed in &msg.embeds {
            if let Some(description) = &embed.description {
                self.extract_nitro_codes_fast(ctx, msg, description, &mut detected_codes).await;
            }
            if let Some(title) = &embed.title {
                self.extract_nitro_codes_fast(ctx, msg, title, &mut detected_codes).await;
            }
        }

        let detection_time = start_time.elapsed();
        tracing::debug!("Nitro detection took: {:?}", detection_time);

        if detected_codes.is_empty() { None } else { Some(detected_codes) }
    }

    /// Extraction ultra-rapide des codes Nitro
    async fn extract_nitro_codes_fast(
        &self, 
        ctx: &Context, 
        msg: &Message, 
        text: &str, 
        detected_codes: &mut Vec<NitroCodeInfo>
    ) {
        // Recherche de codes avec URL complète (le plus commun)
        for cap in self.regex_patterns.nitro_url_pattern.captures_iter(text) {
            if let Some(code_match) = cap.get(1) {
                let code = code_match.as_str().to_string();
                let full_url = cap.get(0).unwrap().as_str().to_string();
                
                if let Some(code_info) = self.create_code_info(ctx, msg, code, full_url).await {
                    detected_codes.push(code_info);
                }
            }
        }

        // Recherche de codes avec domaine Discord (gift, etc.)
        for cap in self.regex_patterns.nitro_code_pattern.captures_iter(text) {
            if let Some(code_match) = cap.get(1) {
                let code = code_match.as_str().to_string();
                let full_url = format!("https://discord.gift/{}", code);
                
                if let Some(code_info) = self.create_code_info(ctx, msg, code, full_url).await {
                    detected_codes.push(code_info);
                }
            }
        }

        // Recherche de codes bruts dans le texte (patterns spéciaux)
        if text.to_lowercase().contains("nitro") || text.to_lowercase().contains("gift") {
            for cap in self.regex_patterns.code_in_text_pattern.captures_iter(text) {
                if let Some(code_match) = cap.get(1) {
                    let code = code_match.as_str().to_string();
                    // Valider que c'est probablement un code Nitro
                    if self.is_likely_nitro_code(&code) {
                        let full_url = format!("https://discord.gift/{}", code);
                        
                        if let Some(code_info) = self.create_code_info(ctx, msg, code, full_url).await {
                            detected_codes.push(code_info);
                        }
                    }
                }
            }
        }
    }

    /// Créer les informations du code détecté ultra-rapidement
    async fn create_code_info(&self, ctx: &Context, msg: &Message, code: String, full_url: String) -> Option<NitroCodeInfo> {
        let guild_name = if let Some(guild_id) = msg.guild_id {
            guild_id.to_guild_cached(&ctx.cache)
                .map(|g| g.name.clone())
        } else {
            None
        };

        let channel_name = match msg.channel_id.to_channel(&ctx.http).await {
            Ok(Channel::Guild(gc)) => gc.name.clone(),
            Ok(Channel::Private(_)) => "DM".to_string(),
            _ => "Unknown".to_string(),
        };

        Some(NitroCodeInfo {
            id: format!("{}_{}", code, msg.id.get()),
            code: code.clone(),
            code_type: self.detect_code_type(&full_url),
            full_url,
            message_id: msg.id.get(),
            channel_id: msg.channel_id.get(),
            guild_id: msg.guild_id.map(|g| g.get()),
            guild_name,
            channel_name,
            author_id: msg.author.id.get(),
            author_username: msg.author.name.clone(),
            author_is_bot: msg.author.bot,
            message_content: msg.content.clone(),
            status: NitroCodeStatus::Detected,
            detected_at: Utc::now(),
            claimed_at: None,
            claim_delay_ms: None,
            reaction_time_ms: None,
            error_message: None,
            claimed_by_account: None,
        })
    }

    /// Détecter le type de code Nitro
    fn detect_code_type(&self, url: &str) -> NitroCodeType {
        let url_lower = url.to_lowercase();
        
        if url_lower.contains("nitro") {
            if url_lower.contains("boost") {
                NitroCodeType::NitroBoost
            } else {
                NitroCodeType::NitroClassic
            }
        } else if url_lower.contains("boost") {
            NitroCodeType::ServerBoost
        } else {
            NitroCodeType::Unknown
        }
    }

    /// Vérifier si c'est probablement un code Nitro valide
    fn is_likely_nitro_code(&self, code: &str) -> bool {
        // Vérifications basiques pour éviter les faux positifs
        if code.len() < 16 || code.len() > 24 {
            return false;
        }

        // Éviter les codes avec patterns suspects
        if code.chars().all(|c| c == code.chars().next().unwrap()) {
            return false; // Tous les caractères identiques
        }

        if code.chars().all(|c| c.is_numeric()) {
            return false; // Que des chiffres
        }

        true
    }

    /// Récupérer un code Nitro ultra-rapidement
    pub async fn claim_code_fast(&self, ctx: &Context, code_info: &NitroCodeInfo) -> ClaimResult {
        let start_time = Instant::now();
        
        // Calculer le délai aléatoire ultra-rapide
        let delay_ms = {
            let manager = self.manager.lock().await;
            let min_delay = manager.config.min_delay_ms;
            let max_delay = manager.config.max_delay_ms;
            rand::thread_rng().gen_range(min_delay..=max_delay)
        };

        // Attendre le délai ultra-court
        tokio::time::sleep(tokio::time::Duration::from_millis(delay_ms as u64)).await;

        // Vérifier le mode test
        let test_mode = {
            let manager = self.manager.lock().await;
            manager.config.test_mode
        };

        if test_mode {
            // Mode test : simuler une récupération
            let total_time = start_time.elapsed().as_millis() as u32;
            return ClaimResult {
                success: true,
                message: "Mode test : code détecté mais non récupéré".to_string(),
                code: code_info.code.clone(),
                code_type: code_info.code_type.clone(),
                attempted_at: Utc::now(),
                delay_used_ms: delay_ms,
                total_time_ms: total_time,
                account_used: "Mode Test".to_string(),
            };
        }

        // Tentative de récupération réelle
        let result = self.try_claim_code_real(ctx, code_info).await;
        let total_time = start_time.elapsed().as_millis() as u32;

        ClaimResult {
            success: result.is_ok(),
            message: result.unwrap_or_else(|e| e),
            code: code_info.code.clone(),
            code_type: code_info.code_type.clone(),
            attempted_at: Utc::now(),
            delay_used_ms: delay_ms,
            total_time_ms: total_time,
            account_used: ctx.cache.current_user().name.clone(),
        }
    }

    /// Tentative de récupération réelle ultra-optimisée
    async fn try_claim_code_real(&self, ctx: &Context, code_info: &NitroCodeInfo) -> Result<String, String> {
        // Construire l'URL de récupération Discord
        let claim_url = format!("https://discord.com/api/v10/entitlements/gift-codes/{}/redeem", code_info.code);
        
        // Obtenir le token de l'utilisateur actuel
        let token = ctx.http.token();
        
        // Requête ultra-rapide à l'API Discord
        let response = self.claim_client
            .post(&claim_url)
            .header("Authorization", token)
            .header("Content-Type", "application/json")
            .header("User-Agent", "VoidBot Nitro Sniper")
            .json(&serde_json::json!({}))
            .send()
            .await
            .map_err(|e| format!("Erreur réseau: {}", e))?;

        let status = response.status();
        let response_text = response.text().await.unwrap_or_default();

        match status.as_u16() {
            200 | 201 => {
                // Succès ! Code récupéré
                if let Ok(json_response) = serde_json::from_str::<serde_json::Value>(&response_text) {
                    if let Some(subscription_plan) = json_response.get("subscription_plan") {
                        let plan_name = subscription_plan.get("name")
                            .and_then(|n| n.as_str())
                            .unwrap_or("Nitro");
                        return Ok(format!("✅ Code récupéré avec succès: {}", plan_name));
                    }
                }
                Ok("✅ Code Nitro récupéré avec succès !".to_string())
            },
            400 => {
                if response_text.to_lowercase().contains("already") || 
                   response_text.to_lowercase().contains("redeemed") {
                    Err("❌ Code déjà utilisé".to_string())
                } else if response_text.to_lowercase().contains("invalid") {
                    Err("❌ Code invalide".to_string())
                } else {
                    Err("❌ Erreur de validation".to_string())
                }
            },
            401 => Err("❌ Non autorisé (token invalide)".to_string()),
            404 => Err("❌ Code introuvable ou expiré".to_string()),
            429 => Err("❌ Rate limited - trop de tentatives".to_string()),
            _ => Err(format!("❌ Erreur API Discord: {}", status)),
        }
    }

    /// Traiter un code détecté ultra-rapidement
    pub async fn handle_detected_codes(&self, ctx: &Context, codes: Vec<NitroCodeInfo>) -> Vec<NitroSniperEvent> {
        let mut events = Vec::new();
        
        for code_info in codes {
            // Ajouter à la liste des codes détectés
            {
                let mut manager = self.manager.lock().await;
                manager.add_detected_code(code_info.clone());
            }

            // Event de détection
            events.push(NitroSniperEvent::CodeDetected { 
                code_info: code_info.clone() 
            });

            // Si auto-claim est activé, tenter de récupérer immédiatement
            let auto_claim = {
                let manager = self.manager.lock().await;
                !manager.config.test_mode // En mode test, on ne récupère pas automatiquement
            };

            if auto_claim {
                // Récupération en arrière-plan ultra-rapide
                let ctx_clone = ctx.clone();
                let code_clone = code_info.clone();
                let sniper_clone = self.clone();
                
                tokio::spawn(async move {
                    let result = sniper_clone.claim_code_fast(&ctx_clone, &code_clone).await;
                    
                    // Mettre à jour le gestionnaire avec le résultat
                    {
                        let mut manager = sniper_clone.manager.lock().await;
                        manager.mark_as_claimed(&code_clone.id, &result);
                    }
                    
                    tracing::info!("Nitro claim result: {} - {}", result.success, result.message);
                });
            }
        }

        events
    }
}

// Implémentation de Clone pour permettre le partage entre threads
impl Clone for NitroSniper {
    fn clone(&self) -> Self {
        Self {
            manager: self.manager.clone(),
            regex_patterns: NitroPatterns::new().expect("Failed to create regex patterns"),
            claim_client: self.claim_client.clone(),
        }
    }
}