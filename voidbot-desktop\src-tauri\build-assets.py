#!/usr/bin/env python3
"""
Script de génération d'assets pour les installeurs VoidBot
Génère les images BMP nécessaires pour Windows MSI/NSIS
"""

import os
from PIL import Image, ImageDraw, ImageFont
import sys

def create_gradient_bg(width, height, start_color, end_color):
    """Crée un dégradé entre deux couleurs"""
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    for y in range(height):
        # Interpolation linéaire entre les couleurs
        ratio = y / height
        r = int(start_color[0] * (1 - ratio) + end_color[0] * ratio)
        g = int(start_color[1] * (1 - ratio) + end_color[1] * ratio)
        b = int(start_color[2] * (1 - ratio) + end_color[2] * ratio)
        
        draw.line([(0, y), (width, y)], fill=(r, g, b))
    
    return image

def create_header_image():
    """Crée l'image d'en-tête pour l'installeur (497x58)"""
    width, height = 497, 58
    
    # Couleurs VoidBot (dégradé sombre vers plus clair)
    start_color = (17, 24, 39)   # gray-900
    end_color = (31, 41, 55)     # gray-800
    
    image = create_gradient_bg(width, height, start_color, end_color)
    draw = ImageDraw.Draw(image)
    
    # Ajouter le texte VoidBot
    try:
        # Essayer d'utiliser une police moderne si disponible
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    text = "VoidBot"
    # Centrer le texte
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Texte avec dégradé simulé (couleur principale VoidBot)
    draw.text((x, y), text, fill=(139, 92, 246), font=font)  # indigo-500
    
    return image

def create_sidebar_image():
    """Crée l'image de sidebar pour l'installeur (164x314)"""
    width, height = 164, 314
    
    # Couleurs VoidBot
    start_color = (17, 24, 39)   # gray-900
    end_color = (55, 65, 81)     # gray-700
    
    image = create_gradient_bg(width, height, start_color, end_color)
    draw = ImageDraw.Draw(image)
    
    # Ajouter des éléments décoratifs
    # Cercles décoratifs
    for i in range(3):
        y_pos = 80 + i * 60
        draw.ellipse([50, y_pos, 114, y_pos + 64], 
                    outline=(139, 92, 246, 100), width=2)  # indigo-500 semi-transparent
    
    # Logo/Text VoidBot en bas
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    text = "VoidBot"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    x = (width - text_width) // 2
    y = height - 40
    
    draw.text((x, y), text, fill=(139, 92, 246), font=font)
    
    # Sous-titre
    try:
        small_font = ImageFont.truetype("arial.ttf", 10)
    except:
        small_font = ImageFont.load_default()
    
    subtitle = "Ultimate Toolkit"
    bbox = draw.textbbox((0, 0), subtitle, font=small_font)
    text_width = bbox[2] - bbox[0]
    x = (width - text_width) // 2
    y = height - 20
    
    draw.text((x, y), subtitle, fill=(156, 163, 175), font=small_font)  # gray-400
    
    return image

def create_banner_wix():
    """Crée l'image banner pour WiX (493x58)"""
    return create_header_image()

def create_dialog_wix():
    """Crée l'image dialog pour WiX (493x312)"""
    width, height = 493, 312
    
    start_color = (17, 24, 39)
    end_color = (31, 41, 55)
    
    image = create_gradient_bg(width, height, start_color, end_color)
    draw = ImageDraw.Draw(image)
    
    # Logo central
    center_x, center_y = width // 2, height // 2
    
    # Cercle principal
    draw.ellipse([center_x - 60, center_y - 60, center_x + 60, center_y + 60], 
                outline=(139, 92, 246), width=4)
    
    # Texte VoidBot
    try:
        font = ImageFont.truetype("arial.ttf", 32)
    except:
        font = ImageFont.load_default()
    
    text = "VoidBot"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    x = center_x - text_width // 2
    y = center_y - 16
    
    draw.text((x, y), text, fill=(139, 92, 246), font=font)
    
    return image

def main():
    assets_dir = "assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    print("🎨 Génération des assets pour les installeurs...")
    
    try:
        # Image d'en-tête NSIS
        print("📄 Création header-image.bmp...")
        header = create_header_image()
        header.save(os.path.join(assets_dir, "header-image.bmp"), "BMP")
        
        # Image sidebar NSIS  
        print("📄 Création sidebar-image.bmp...")
        sidebar = create_sidebar_image()
        sidebar.save(os.path.join(assets_dir, "sidebar-image.bmp"), "BMP")
        
        # Images WiX
        print("📄 Création banner.bmp...")
        banner = create_banner_wix()
        banner.save(os.path.join(assets_dir, "banner.bmp"), "BMP")
        
        print("📄 Création dialog.bmp...")
        dialog = create_dialog_wix()
        dialog.save(os.path.join(assets_dir, "dialog.bmp"), "BMP")
        
        print("✅ Assets générés avec succès!")
        print(f"📁 Fichiers créés dans {assets_dir}/:")
        for file in os.listdir(assets_dir):
            if file.endswith('.bmp'):
                print(f"   - {file}")
                
    except Exception as e:
        print(f"❌ Erreur lors de la génération: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()