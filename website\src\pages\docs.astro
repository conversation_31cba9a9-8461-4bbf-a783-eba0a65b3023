---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const docSections = [
  {
    title: "Installation & Configuration",
    icon: "⚙️",
    items: [
      { name: "Installation rapide", href: "/docs/installation", description: "Guide d'installation pour Windows, macOS et Linux" },
      { name: "Premier lancement", href: "/docs/setup", description: "Configuration initiale et connexion Discord" },
      { name: "Configuration avancée", href: "/docs/config", description: "Paramètres et personnalisation complète" },
      { name: "Résolution problèmes", href: "/docs/troubleshooting", description: "Solutions aux problèmes courants" }
    ]
  },
  {
    title: "Fonctionnalités Core",
    icon: "🚀",
    items: [
      { name: "Mode Furtif", href: "/docs/stealth-mode", description: "Maîtriser les modes Normal et Fantôme" },
      { name: "Notification Center", href: "/docs/notifications", description: "Configuration des 14 types d'événements" },
      { name: "Auto-Commands", href: "/docs/auto-commands", description: "Translate, Slash et Reply automatiques" },
      { name: "Commandes Troll", href: "/docs/trolls", description: "5 types de trolls avec sécurité" }
    ]
  },
  {
    title: "Snipers & Joiners",
    icon: "🎯",
    items: [
      { name: "Giveaway Joiner", href: "/docs/giveaway-joiner", description: "Participation automatique intelligente" },
      { name: "Nitro Sniper", href: "/docs/nitro-sniper", description: "Capture ultra-rapide en 50-200ms" },
      { name: "Multi-comptes", href: "/docs/multi-accounts", description: "Gestion et configuration des alts" },
      { name: "Sécurité & Limites", href: "/docs/security", description: "Bonnes pratiques et protection" }
    ]
  },
  {
    title: "Interface & Outils",
    icon: "🎨",
    items: [
      { name: "Interface Desktop", href: "/docs/interface", description: "Navigation et utilisation de l'app" },
      { name: "Générateur Images", href: "/docs/image-generator", description: "Memes, overlays, bannières et stats" },
      { name: "Rate Limiting", href: "/docs/rate-limiting", description: "Protection et monitoring avancé" },
      { name: "Base de données", href: "/docs/database", description: "Gestion des données et statistiques" }
    ]
  },
  {
    title: "Avancé & Développement",
    icon: "🔧",
    items: [
      { name: "API Documentation", href: "/docs/api", description: "Tauri commands et architecture" },
      { name: "Compiler from Source", href: "/docs/build", description: "Build et développement local" },
      { name: "Contributions", href: "/docs/contributing", description: "Contribuer au projet open-source" },
      { name: "Changelog", href: "/docs/changelog", description: "Historique des versions" }
    ]
  }
];

const quickLinks = [
  { name: "Guide d'installation", href: "javascript:void(0)", icon: "📥", onClick: "showDocAlert('Guide d\'installation', 'Instructions détaillées pour installer VoidBot sur Windows, macOS et Linux')" },
  { name: "Configuration Discord", href: "javascript:void(0)", icon: "🔗", onClick: "showDocAlert('Configuration Discord', 'Guide pour connecter votre compte Discord et configurer VoidBot')" },
  { name: "Commandes principales", href: "javascript:void(0)", icon: "⌨️", onClick: "showDocAlert('Commandes principales', 'Liste complète des 100+ commandes Discord disponibles dans VoidBot')" },
  { name: "Support Discord", href: "/support", icon: "💬" }
];
---

<Layout title="Documentation VoidBot - Guide Complet" description="Documentation complète de VoidBot : installation, configuration, fonctionnalités et guides avancés.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl sm:text-5xl font-bold mb-6">
          <span class="text-white">Documentation</span>
          <br>
          <span class="gradient-text">VoidBot</span>
        </h1>
        <p class="text-xl text-gray-300 mb-8">
          Guide complet pour installer, configurer et maîtriser toutes les fonctionnalités de VoidBot.
          <br class="hidden sm:block">
          De l'installation à l'utilisation avancée.
        </p>
        
        <!-- Status Notice -->
        <div class="max-w-2xl mx-auto mb-8">
          <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div class="flex items-center gap-3 mb-2">
              <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              <span class="font-semibold text-blue-400">Documentation en Construction</span>
            </div>
            <p class="text-gray-400 text-sm">
              Les guides détaillés sont en cours de rédaction. Cliquez sur les sections ci-dessous pour en savoir plus !
            </p>
          </div>
        </div>
        
        <!-- Search -->
        <div class="max-w-md mx-auto mb-8">
          <div class="relative">
            <input 
              type="text" 
              placeholder="Rechercher dans la documentation..."
              class="w-full bg-gray-900/50 border border-gray-700 rounded-lg px-4 py-3 pl-12 text-white placeholder-gray-400 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              id="doc-search"
            >
            <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Links -->
    <section class="py-12 bg-gray-900/30">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-center mb-8">
          <span class="text-white">Liens</span>
          <span class="gradient-text"> Rapides</span>
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickLinks.map((link) => (
            <a href={link.href} onclick={link.onClick || null} class="card-hover group text-center">
              <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">
                {link.icon}
              </div>
              <h3 class="font-semibold text-white group-hover:text-indigo-400 transition-colors duration-200">
                {link.name}
              </h3>
            </a>
          ))}
        </div>
      </div>
    </section>

    <!-- Documentation Sections -->
    <section class="py-20">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="space-y-12">
          {docSections.map((section, sectionIndex) => (
            <div class="fade-in" style={`animation-delay: ${sectionIndex * 0.1}s`}>
              <!-- Section Header -->
              <div class="flex items-center gap-4 mb-8">
                <div class="text-3xl">{section.icon}</div>
                <h2 class="text-2xl font-bold text-white">{section.title}</h2>
              </div>
              
              <!-- Section Items -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {section.items.map((item, itemIndex) => (
                  <a 
                    href="javascript:void(0)" 
                    onclick={`showDocAlert('${item.name}', '${item.description}')`}
                    class="card-hover group block slide-up"
                    style={`animation-delay: ${(sectionIndex * 0.1) + (itemIndex * 0.05)}s`}
                  >
                    <div class="flex items-start gap-4">
                      <div class="flex-shrink-0 w-8 h-8 bg-indigo-500/20 rounded-lg flex items-center justify-center group-hover:bg-indigo-500/30 transition-colors duration-200">
                        <svg class="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h3 class="font-semibold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-200">
                          {item.name}
                        </h3>
                        <p class="text-gray-400 text-sm group-hover:text-gray-300 transition-colors duration-200">
                          {item.description}
                        </p>
                      </div>
                      <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-400 group-hover:translate-x-1 transition-all duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Support Section -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-6">
          <span class="text-white">Besoin d'</span>
          <span class="gradient-text">Aide ?</span>
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          Notre communauté est là pour vous aider 24/7
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="card-hover text-center">
            <div class="text-3xl mb-3">💬</div>
            <h3 class="font-semibold text-white mb-2">Discord Support</h3>
            <p class="text-gray-400 text-sm mb-4">Support en temps réel</p>
            <a href="/support" class="btn btn-primary">
              Rejoindre Discord
            </a>
          </div>
          
          <div class="card-hover text-center">
            <div class="text-3xl mb-3">📚</div>
            <h3 class="font-semibold text-white mb-2">Wiki Communauté</h3>
            <p class="text-gray-400 text-sm mb-4">Guides par la communauté</p>
            <a href="/wiki" class="btn btn-secondary">
              Accéder au Wiki
            </a>
          </div>
          
          <div class="card-hover text-center">
            <div class="text-3xl mb-3">🐛</div>
            <h3 class="font-semibold text-white mb-2">Report Bug</h3>
            <p class="text-gray-400 text-sm mb-4">Signaler un problème</p>
            <a href="#" onclick="alert('GitHub Issues sera bientôt disponible')" class="btn btn-accent">
              GitHub Issues
            </a>
          </div>
        </div>
        
        <p class="text-gray-400">
          Documentation mise à jour en continu. 
          <a href="/docs/changelog" class="text-indigo-400 hover:text-indigo-300 underline">
            Voir les dernières modifications
          </a>
        </p>
      </div>
    </section>

    <!-- Contributing -->
    <section class="py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-6">
          <span class="text-white">Contribuer à la</span>
          <span class="gradient-text"> Documentation</span>
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          Aidez-nous à améliorer cette documentation pour toute la communauté
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="#" onclick="alert('Contribution GitHub sera bientôt disponible')" class="btn btn-primary text-lg px-8 py-4">
            <span class="flex items-center gap-2">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              Éditer sur GitHub
            </span>
          </a>
          <a href="/docs/contributing" class="btn btn-secondary text-lg px-8 py-4">
            Guide Contribution
          </a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>

<script>
  // Show documentation alerts
  function showDocAlert(title, description) {
    alert(`📚 ${title}\n\n${description}\n\nCe guide détaillé sera bientôt disponible dans la documentation VoidBot ! Restez connectés pour les mises à jour.`);
  }
  
  // Documentation search
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('doc-search');
    
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        const sections = document.querySelectorAll('[data-searchable]');
        
        sections.forEach(section => {
          const text = section.textContent.toLowerCase();
          const isVisible = text.includes(query) || query === '';
          section.style.display = isVisible ? 'block' : 'none';
        });
      });
    }
  });

  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
</script>