name: Build VoidBot Multi-Architecture

on:
  push:
    branches: [ main, master, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-linux:
    runs-on: ubuntu-22.04
    
    strategy:
      matrix:
        target: [x86_64-unknown-linux-gnu, aarch64-unknown-linux-gnu]
        include:
          - target: x86_64-unknown-linux-gnu
            arch: x64
          - target: aarch64-unknown-linux-gnu
            arch: arm64
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: voidbot-desktop/package-lock.json
        
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          curl \
          wget \
          file \
          libgtk-3-dev \
          libwebkit2gtk-4.0-dev \
          libayatana-appindicator3-dev \
          librsvg2-dev \
          libssl-dev \
          libudev-dev
          
        # Cross-compilation dependencies for ARM64
        if [ "${{ matrix.target }}" = "aarch64-unknown-linux-gnu" ]; then
          sudo apt-get install -y gcc-aarch64-linux-gnu
          echo "CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc" >> $GITHUB_ENV
        fi
        
    - name: Install Node.js dependencies
      working-directory: voidbot-desktop
      run: npm ci
      
    - name: Build Rust workspace
      run: cargo build --release --target ${{ matrix.target }}
      
    - name: Build Tauri application
      working-directory: voidbot-desktop
      run: |
        npm run tauri build -- --target ${{ matrix.target }} --bundles deb,appimage
        
    - name: Upload Linux artifacts
      uses: actions/upload-artifact@v4
      with:
        name: voidbot-linux-${{ matrix.arch }}
        path: |
          voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/deb/*.deb
          voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/appimage/*.AppImage
        retention-days: 30

  build-windows:
    runs-on: windows-latest
    
    strategy:
      matrix:
        target: [x86_64-pc-windows-msvc, i686-pc-windows-msvc]
        include:
          - target: x86_64-pc-windows-msvc
            arch: x64
          - target: i686-pc-windows-msvc
            arch: x86
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: voidbot-desktop/package-lock.json
        
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}
        
    - name: Install Node.js dependencies
      working-directory: voidbot-desktop
      run: npm ci
      
    - name: Build Rust workspace
      run: cargo build --release --target ${{ matrix.target }}
      
    - name: Build Tauri application
      working-directory: voidbot-desktop
      run: |
        npm run tauri build -- --target ${{ matrix.target }} --bundles msi,nsis
        
    - name: Upload Windows artifacts
      uses: actions/upload-artifact@v4
      with:
        name: voidbot-windows-${{ matrix.arch }}
        path: |
          voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/msi/*.msi
          voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/nsis/*.exe
        retention-days: 30

  build-macos:
    runs-on: macos-14
    
    strategy:
      matrix:
        target: [x86_64-apple-darwin, aarch64-apple-darwin, universal-apple-darwin]
        include:
          - target: x86_64-apple-darwin
            arch: x64
          - target: aarch64-apple-darwin
            arch: arm64
          - target: universal-apple-darwin
            arch: universal
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: voidbot-desktop/package-lock.json
        
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: x86_64-apple-darwin,aarch64-apple-darwin
        
    - name: Install Node.js dependencies
      working-directory: voidbot-desktop
      run: npm ci
      
    - name: Build Rust workspace
      run: |
        if [ "${{ matrix.target }}" = "universal-apple-darwin" ]; then
          cargo build --release --target x86_64-apple-darwin
          cargo build --release --target aarch64-apple-darwin
        else
          cargo build --release --target ${{ matrix.target }}
        fi
        
    - name: Build Tauri application
      working-directory: voidbot-desktop
      run: |
        npm run tauri build -- --target ${{ matrix.target }} --bundles dmg
        
    - name: Upload macOS artifacts
      uses: actions/upload-artifact@v4
      with:
        name: voidbot-macos-${{ matrix.arch }}
        path: |
          voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/dmg/*.dmg
        retention-days: 30

  create-release:
    needs: [build-linux, build-windows, build-macos]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts
        
    - name: Display structure of downloaded files
      run: ls -la artifacts/
      
    - name: Generate release notes
      id: release-notes
      run: |
        VERSION=${GITHUB_REF#refs/tags/}
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        
        cat > release-notes.md << 'EOF'
        # VoidBot $VERSION
        
        ## 🚀 Nouveautés
        - Interface desktop native Tauri v2
        - +100 commandes Discord avancées
        - Snipers ultra-rapides (Giveaway + Nitro)
        - Système de trolls sécurisé
        - Auto-commands intelligents
        - Chiffrement AES-256-GCM
        
        ## 📦 Téléchargements
        
        ### Windows
        - **VoidBot-x64.msi** - Installeur 64-bit (recommandé)
        - **VoidBot-x86.msi** - Installeur 32-bit
        - **VoidBot-Setup.exe** - Installeur NSIS alternatif
        
        ### macOS
        - **VoidBot-universal.dmg** - Support Intel + Apple Silicon
        
        ### Linux
        - **VoidBot-x64.deb** - Package Debian/Ubuntu 64-bit
        - **VoidBot-arm64.deb** - Package Debian/Ubuntu ARM64
        - **VoidBot-x64.AppImage** - Portable 64-bit
        - **VoidBot-arm64.AppImage** - Portable ARM64
        
        ## ✅ Vérification
        Tous les fichiers incluent des checksums SHA256 pour vérifier l'intégrité.
        
        ## 🔒 Sécurité
        - Code source auditable
        - Builds reproductibles
        - Aucune télémétrie
        - 100% local
        
        **Configuration minimale :** 512MB RAM, 50MB espace disque
        EOF
        
    - name: Generate checksums
      run: |
        cd artifacts
        find . -type f \( -name "*.msi" -o -name "*.exe" -o -name "*.dmg" -o -name "*.deb" -o -name "*.AppImage" \) -exec sha256sum {} \; > ../checksums.sha256
        
    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        name: VoidBot ${{ steps.release-notes.outputs.version }}
        body_path: release-notes.md
        files: |
          artifacts/**/*.msi
          artifacts/**/*.exe
          artifacts/**/*.dmg
          artifacts/**/*.deb
          artifacts/**/*.AppImage
          checksums.sha256
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}