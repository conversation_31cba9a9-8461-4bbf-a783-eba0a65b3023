import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, Refresh<PERSON><PERSON>, Bug, Copy } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
}

/**
 * Error Boundary pour capturer les erreurs React non gérées
 * Affiche une interface de récupération gracieuse
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      errorId: this.generateErrorId()
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: ErrorBoundary.generateErrorId()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
      errorId: ErrorBoundary.generateErrorId()
    });

    // Log l'erreur pour debugging
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // TODO: Envoyer à un service de logging si configuré
    this.logErrorToService(error, errorInfo);
  }

  private static generateErrorId(): string {
    return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateErrorId(): string {
    return ErrorBoundary.generateErrorId();
  }

  private async logErrorToService(error: Error, errorInfo: ErrorInfo) {
    try {
      // Log via Tauri si disponible
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('log_frontend_error', {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      });
    } catch (loggingError) {
      console.warn('Failed to log error to service:', loggingError);
    }
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: this.generateErrorId()
    });
  };

  private copyErrorDetails = async () => {
    const errorDetails = {
      id: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      alert('Détails de l\'erreur copiés dans le presse-papier');
    } catch (err) {
      console.error('Failed to copy error details:', err);
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-voidbot-dark via-voidbot-darker to-black flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="max-w-2xl w-full"
          >
            <div className="card text-center">
              {/* Icon d'erreur */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring" }}
                className="mb-6"
              >
                <div className="w-20 h-20 mx-auto bg-red-500/20 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-10 h-10 text-red-500" />
                </div>
              </motion.div>

              {/* Titre */}
              <h1 className="text-2xl font-bold text-white mb-4">
                Oups ! Une erreur s'est produite
              </h1>

              <p className="text-voidbot-secondary mb-6">
                VoidBot a rencontré un problème inattendu. Nous nous excusons pour la gêne occasionnée.
              </p>

              {/* ID d'erreur */}
              <div className="bg-voidbot-darker border border-voidbot-primary/30 rounded-lg p-3 mb-6">
                <p className="text-sm text-voidbot-secondary mb-1">ID d'erreur :</p>
                <code className="text-voidbot-primary font-mono text-sm">{this.state.errorId}</code>
              </div>

              {/* Détails de l'erreur (mode développement) */}
              {import.meta.env.DEV && this.state.error && (
                <motion.details
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-left mb-6 bg-gray-900 rounded-lg border border-gray-700"
                >
                  <summary className="p-4 cursor-pointer text-yellow-400 hover:text-yellow-300 transition-colors">
                    Détails techniques (développement)
                  </summary>
                  <div className="px-4 pb-4 space-y-3">
                    <div>
                      <h4 className="font-semibold text-red-400 mb-2">Message d'erreur :</h4>
                      <pre className="text-sm text-gray-300 bg-gray-800 p-3 rounded overflow-x-auto">
                        {this.state.error.message}
                      </pre>
                    </div>
                    
                    {this.state.error.stack && (
                      <div>
                        <h4 className="font-semibold text-red-400 mb-2">Stack trace :</h4>
                        <pre className="text-xs text-gray-400 bg-gray-800 p-3 rounded overflow-x-auto max-h-40">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <h4 className="font-semibold text-red-400 mb-2">Component stack :</h4>
                        <pre className="text-xs text-gray-400 bg-gray-800 p-3 rounded overflow-x-auto max-h-32">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </motion.details>
              )}

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  onClick={this.handleRetry}
                  className="btn btn-primary flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Réessayer
                </motion.button>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  onClick={this.handleReload}
                  className="btn btn-secondary flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Recharger l'application
                </motion.button>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  onClick={this.copyErrorDetails}
                  className="btn btn-ghost flex items-center justify-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  Copier les détails
                </motion.button>
              </div>

              {/* Conseils de récupération */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
                className="mt-8 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg text-left"
              >
                <div className="flex items-start gap-3">
                  <Bug className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-blue-400 mb-2">Conseils de récupération</h3>
                    <ul className="text-sm text-blue-300 space-y-1">
                      <li>• Essayez d'abord le bouton "Réessayer"</li>
                      <li>• Si le problème persiste, rechargez l'application</li>
                      <li>• Vérifiez votre connexion internet</li>
                      <li>• Redémarrez VoidBot si nécessaire</li>
                      <li>• Reportez le bug avec l'ID d'erreur si le problème continue</li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook pour utiliser l'Error Boundary de manière programmatique
 */
export function useErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  const throwError = React.useCallback((error: Error | string) => {
    setError(error instanceof Error ? error : new Error(error));
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { throwError, clearError };
}