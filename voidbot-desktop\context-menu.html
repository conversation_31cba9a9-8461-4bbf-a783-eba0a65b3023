<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoidBot Context Menu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            background: rgba(15, 15, 20, 0.98);
            color: white;
            border-radius: 12px;
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .menu-container {
            padding: 8px;
            min-width: 280px;
            max-width: 320px;
        }

        .menu-header {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(168, 85, 247, 0.2));
            margin: -8px -8px 8px -8px;
        }

        .menu-title {
            font-size: 14px;
            font-weight: 600;
            color: #a78bfa;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .menu-section {
            margin-bottom: 4px;
        }

        .section-label {
            font-size: 11px;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 8px 12px 4px;
            font-weight: 500;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.15s ease;
            position: relative;
            font-size: 13px;
        }

        .menu-item:hover {
            background: rgba(99, 102, 241, 0.15);
            color: #c7d2fe;
        }

        .menu-item:active {
            transform: scale(0.98);
        }

        .menu-item-icon {
            font-size: 16px;
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .menu-item-text {
            flex: 1;
            font-weight: 500;
        }

        .menu-item-shortcut {
            font-size: 11px;
            color: #6b7280;
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .menu-separator {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 6px 0;
        }

        .menu-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .menu-item.disabled:hover {
            background: none;
            color: white;
        }

        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .menu-container {
            animation: slideIn 0.2s ease-out;
        }

        .menu-item:nth-child(odd) {
            animation: slideIn 0.3s ease-out;
        }

        .menu-item:nth-child(even) {
            animation: slideIn 0.35s ease-out;
        }
    </style>
</head>
<body>
    <div class="menu-container">
        <div class="menu-header">
            <div class="menu-title">
                🔮 VoidBot Utils
            </div>
        </div>

        <div class="menu-section">
            <div class="section-label">👤 Utilisateur</div>
            <div class="menu-item" data-action="copy-user-id">
                <div class="menu-item-icon">🆔</div>
                <div class="menu-item-text">Copier ID utilisateur</div>
                <div class="menu-item-shortcut">Ctrl+I</div>
            </div>
            <div class="menu-item" data-action="user-creation-date">
                <div class="menu-item-icon">📅</div>
                <div class="menu-item-text">Date de création</div>
            </div>
            <div class="menu-item" data-action="download-avatar">
                <div class="menu-item-icon">🎨</div>
                <div class="menu-item-text">Télécharger avatar HD</div>
            </div>
            <div class="menu-item" data-action="nitro-status">
                <div class="menu-item-icon">🌟</div>
                <div class="menu-item-text">Statut Nitro</div>
            </div>
        </div>

        <div class="menu-separator"></div>

        <div class="menu-section">
            <div class="section-label">💬 Message</div>
            <div class="menu-item" data-action="copy-message-id">
                <div class="menu-item-icon">🆔</div>
                <div class="menu-item-text">Copier ID message</div>
            </div>
            <div class="menu-item" data-action="message-timestamp">
                <div class="menu-item-icon">🕐</div>
                <div class="menu-item-text">Timestamp précis</div>
            </div>
            <div class="menu-item" data-action="message-link">
                <div class="menu-item-icon">🔗</div>
                <div class="menu-item-text">Lien direct</div>
            </div>
            <div class="menu-item" data-action="copy-content">
                <div class="menu-item-icon">📋</div>
                <div class="menu-item-text">Copier contenu</div>
            </div>
        </div>

        <div class="menu-separator"></div>

        <div class="menu-section">
            <div class="section-label">😀 Emojis</div>
            <div class="menu-item" data-action="clone-emojis">
                <div class="menu-item-icon">😀</div>
                <div class="menu-item-text">Cloner emojis</div>
            </div>
            <div class="menu-item" data-action="download-emojis">
                <div class="menu-item-icon">💾</div>
                <div class="menu-item-text">Télécharger emojis</div>
            </div>
        </div>

        <div class="menu-separator"></div>

        <div class="menu-section">
            <div class="section-label">🔧 Utilitaires</div>
            <div class="menu-item" data-action="screenshot">
                <div class="menu-item-icon">📸</div>
                <div class="menu-item-text">Screenshot</div>
            </div>
            <div class="menu-item" data-action="export-data">
                <div class="menu-item-icon">💾</div>
                <div class="menu-item-text">Export données</div>
            </div>
        </div>
    </div>

    <script>
        // Import Tauri API
        const { invoke } = window.__TAURI__.tauri;
        const { listen } = window.__TAURI__.event;

        // Gestionnaire des actions du menu
        const menuActions = {
            'copy-user-id': async () => {
                try {
                    const userId = await invoke('copy_user_id', { userId: '123456789012345678' });
                    await copyToClipboard(userId, 'ID utilisateur copié');
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'user-creation-date': async () => {
                try {
                    const date = await invoke('get_user_creation_date', { userId: '123456789012345678' });
                    showNotification('Compte créé le ' + date);
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'download-avatar': async () => {
                try {
                    const avatarUrl = await invoke('download_user_avatar', { 
                        userId: '123456789012345678', 
                        avatarHash: null 
                    });
                    // Ouvrir l'URL de l'avatar dans le navigateur
                    window.open(avatarUrl, '_blank');
                    showNotification('Avatar HD ouvert dans le navigateur');
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'nitro-status': async () => {
                try {
                    const status = await invoke('get_user_nitro_status', { userId: '123456789012345678' });
                    showNotification('Statut Nitro: ' + status);
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'copy-message-id': async () => {
                try {
                    const messageId = await invoke('copy_message_id', { messageId: '987654321098765432' });
                    await copyToClipboard(messageId, 'ID message copié');
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'message-timestamp': async () => {
                try {
                    const timestamp = await invoke('get_message_timestamp', { messageId: '987654321098765432' });
                    showNotification('Créé le ' + timestamp);
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'message-link': async () => {
                try {
                    const link = await invoke('get_message_link', { 
                        guildId: '123456789012345678',
                        channelId: '987654321098765432',
                        messageId: '555666777888999000'
                    });
                    await copyToClipboard(link, 'Lien message copié');
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'copy-content': async () => {
                try {
                    const content = await invoke('copy_message_content', { content: 'Contenu du message exemple' });
                    await copyToClipboard(content, 'Contenu copié');
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'clone-emojis': async () => {
                try {
                    const emojis = await invoke('clone_server_emojis', { guildId: '123456789012345678' });
                    showNotification(`${emojis.length} emojis trouvés et prêts à cloner`);
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'download-emojis': async () => {
                try {
                    const result = await invoke('download_server_emojis', { guildId: '123456789012345678' });
                    showNotification(result);
                } catch (error) {
                    showNotification('Erreur: ' + error, 'error');
                }
            },
            'screenshot': () => showNotification('Screenshot pris (fonctionnalité en développement)'),
            'export-data': () => showNotification('Données exportées (fonctionnalité en développement)')
        };

        // Initialisation du menu
        document.addEventListener('DOMContentLoaded', () => {
            // Ajouter les event listeners sur les items du menu
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    const action = item.getAttribute('data-action');
                    if (action && menuActions[action] && !item.classList.contains('disabled')) {
                        menuActions[action]();
                        hideMenu();
                    }
                });
            });

            // Fermer le menu en cliquant en dehors
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.menu-container')) {
                    hideMenu();
                }
            });

            // Raccourcis clavier
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    hideMenu();
                }
            });
        });

        // Fonctions utilitaires
        async function copyToClipboard(text, message) {
            try {
                await navigator.clipboard.writeText(text);
                showNotification(message || 'Copié dans le presse-papiers');
            } catch (err) {
                console.error('Erreur copie:', err);
                showNotification('Erreur lors de la copie');
            }
        }

        function showNotification(message, type = 'success') {
            // Créer une notification temporaire
            const notification = document.createElement('div');
            
            // Couleur selon le type
            let bgColor = 'rgba(34, 197, 94, 0.9)'; // vert pour succès
            if (type === 'error') {
                bgColor = 'rgba(239, 68, 68, 0.9)'; // rouge pour erreur
            } else if (type === 'info') {
                bgColor = 'rgba(59, 130, 246, 0.9)'; // bleu pour info
            }
            
            notification.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: ${bgColor};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 13px;
                font-weight: 500;
                z-index: 1000;
                animation: slideInNotif 0.3s ease-out;
                max-width: 300px;
                word-wrap: break-word;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Durée selon le type
            const duration = type === 'error' ? 5000 : 3000;
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        async function hideMenu() {
            try {
                await invoke('hide_context_menu');
            } catch (err) {
                console.error('Erreur fermeture menu:', err);
                window.close();
            }
        }

        // Styles pour l'animation de notification
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInNotif {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>