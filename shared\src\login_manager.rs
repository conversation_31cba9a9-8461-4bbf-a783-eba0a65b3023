use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use crate::SecureStorage;

/// Gestionnaire de comptes Discord multiples
#[derive(Debug)]
pub struct LoginManager {
    pub config: LoginManagerConfig,
    pub accounts: HashMap<String, DiscordAccount>,
    pub active_account_id: Option<String>,
    pub secure_storage: Option<SecureStorage>,
}

/// Configuration du Login Manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginManagerConfig {
    pub enabled: bool,
    pub max_accounts: usize,
    pub auto_switch_on_error: bool,
    pub save_credentials: bool,
    pub session_timeout_hours: u32,
    pub validate_tokens_on_startup: bool,
    pub backup_tokens_encrypted: bool,
}

impl Default for LoginManagerConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_accounts: 10, // Limite raisonnable
            auto_switch_on_error: false, // Sécurité
            save_credentials: true,
            session_timeout_hours: 24,
            validate_tokens_on_startup: true,
            backup_tokens_encrypted: true,
        }
    }
}

/// Compte Discord
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiscordAccount {
    pub id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub verified: bool,
    pub mfa_enabled: bool,
    pub premium_type: PremiumType,
    pub flags: u64,
    pub token_hash: String, // Hash du token pour identification
    pub added_at: DateTime<Utc>,
    pub last_used: DateTime<Utc>,
    pub last_validated: Option<DateTime<Utc>>,
    pub status: AccountStatus,
    pub notes: Option<String>,
    pub features: Vec<String>,
    pub guild_count: Option<u64>,
    pub friend_count: Option<u64>,
}

/// Type de premium Discord
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PremiumType {
    None,
    NitroClassic,
    NitroBoost,
    NitroBasic,
}

/// Statut du compte
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AccountStatus {
    Active,        // Compte actif et fonctionnel
    Inactive,      // Compte inactif (choix utilisateur)
    Invalid,       // Token invalide ou expiré
    Banned,        // Compte banni
    Disabled,      // Compte désactivé par Discord
    RateLimited,   // Rate limité temporairement
    Unknown,       // Statut inconnu
}

/// Session de connexion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginSession {
    pub account_id: String,
    pub started_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub features_enabled: Vec<String>,
}

/// Statistiques des comptes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountStats {
    pub total_accounts: usize,
    pub active_accounts: usize,
    pub invalid_accounts: usize,
    pub last_switch: Option<DateTime<Utc>>,
    pub switch_count: u64,
    pub total_login_time: u64, // En secondes
}

impl Default for AccountStats {
    fn default() -> Self {
        Self {
            total_accounts: 0,
            active_accounts: 0,
            invalid_accounts: 0,
            last_switch: None,
            switch_count: 0,
            total_login_time: 0,
        }
    }
}

impl LoginManager {
    pub fn new() -> Self {
        Self {
            config: LoginManagerConfig::default(),
            accounts: HashMap::new(),
            active_account_id: None,
            secure_storage: None,
        }
    }
    
    /// Initialiser avec le stockage sécurisé
    pub async fn init_with_storage(&mut self) -> Result<(), LoginManagerError> {
        match SecureStorage::new() {
            Ok(storage) => {
                self.secure_storage = Some(storage);
                self.load_accounts().await?;
                Ok(())
            },
            Err(e) => Err(LoginManagerError::StorageError(format!("Init storage: {}", e))),
        }
    }
    
    /// Ajouter un compte
    pub async fn add_account(&mut self, token: &str, notes: Option<String>) -> Result<String, LoginManagerError> {
        if self.accounts.len() >= self.config.max_accounts {
            return Err(LoginManagerError::LimitReached);
        }
        
        // Valider le token d'abord
        let user_info = self.validate_token(token).await?;
        
        // Vérifier si le compte existe déjà
        let token_hash = self.hash_token(token);
        if self.accounts.values().any(|acc| acc.token_hash == token_hash) {
            return Err(LoginManagerError::AccountExists(user_info.username));
        }
        
        let account = DiscordAccount {
            id: user_info.id.clone(),
            username: user_info.username,
            discriminator: user_info.discriminator,
            avatar: user_info.avatar,
            email: user_info.email,
            phone: user_info.phone,
            verified: user_info.verified,
            mfa_enabled: user_info.mfa_enabled,
            premium_type: user_info.premium_type,
            flags: user_info.flags,
            token_hash,
            added_at: Utc::now(),
            last_used: Utc::now(),
            last_validated: Some(Utc::now()),
            status: AccountStatus::Active,
            notes,
            features: user_info.features,
            guild_count: user_info.guild_count,
            friend_count: user_info.friend_count,
        };
        
        // Sauvegarder le token de manière sécurisée
        if let Some(storage) = &self.secure_storage {
            storage.save_token_for_account(&user_info.id, token).await
                .map_err(|e| LoginManagerError::StorageError(format!("Save token: {}", e)))?;
        }
        
        let account_id = user_info.id.clone();
        self.accounts.insert(account_id.clone(), account);
        
        // Si c'est le premier compte, le rendre actif
        if self.active_account_id.is_none() {
            self.active_account_id = Some(account_id.clone());
        }
        
        self.save_accounts().await?;
        
        Ok(account_id)
    }
    
    /// Supprimer un compte
    pub async fn remove_account(&mut self, account_id: &str) -> Result<(), LoginManagerError> {
        if !self.accounts.contains_key(account_id) {
            return Err(LoginManagerError::AccountNotFound(account_id.to_string()));
        }
        
        // Supprimer le token sécurisé
        if let Some(storage) = &self.secure_storage {
            if let Err(e) = storage.delete_token_for_account(account_id).await {
                // Log l'erreur mais continue
                eprintln!("Erreur suppression token: {}", e);
            }
        }
        
        self.accounts.remove(account_id);
        
        // Si c'était le compte actif, choisir un autre
        if self.active_account_id.as_ref() == Some(&account_id.to_string()) {
            self.active_account_id = self.accounts.keys().next().cloned();
        }
        
        self.save_accounts().await?;
        
        Ok(())
    }
    
    /// Changer de compte actif
    pub async fn switch_account(&mut self, account_id: &str) -> Result<(), LoginManagerError> {
        if !self.accounts.contains_key(account_id) {
            return Err(LoginManagerError::AccountNotFound(account_id.to_string()));
        }
        
        // Vérifier le statut du compte
        let account = self.accounts.get(account_id).unwrap();
        match account.status {
            AccountStatus::Active => {},
            AccountStatus::Invalid => return Err(LoginManagerError::AccountInvalid(account_id.to_string())),
            AccountStatus::Banned => return Err(LoginManagerError::AccountBanned(account_id.to_string())),
            AccountStatus::Disabled => return Err(LoginManagerError::AccountDisabled(account_id.to_string())),
            _ => return Err(LoginManagerError::AccountUnavailable(account_id.to_string())),
        }
        
        // Mettre à jour last_used de l'ancien compte actif
        if let Some(old_active) = &self.active_account_id {
            if let Some(old_account) = self.accounts.get_mut(old_active) {
                old_account.last_used = Utc::now();
            }
        }
        
        self.active_account_id = Some(account_id.to_string());
        
        // Mettre à jour last_used du nouveau compte
        if let Some(account) = self.accounts.get_mut(account_id) {
            account.last_used = Utc::now();
        }
        
        self.save_accounts().await?;
        
        Ok(())
    }
    
    /// Obtenir le compte actif
    pub fn get_active_account(&self) -> Option<&DiscordAccount> {
        self.active_account_id.as_ref()
            .and_then(|id| self.accounts.get(id))
    }
    
    /// Obtenir le token du compte actif
    pub async fn get_active_token(&self) -> Result<Option<String>, LoginManagerError> {
        if let Some(account_id) = &self.active_account_id {
            if let Some(storage) = &self.secure_storage {
                match storage.load_token_for_account(account_id).await {
                    Ok(Some(token)) => Ok(Some(token)),
                    Ok(None) => Ok(None),
                    Err(e) => Err(LoginManagerError::StorageError(format!("Load token: {}", e))),
                }
            } else {
                Err(LoginManagerError::StorageError("No secure storage".to_string()))
            }
        } else {
            Ok(None)
        }
    }
    
    /// Valider tous les comptes
    pub async fn validate_all_accounts(&mut self) -> Result<(), LoginManagerError> {
        let account_ids: Vec<String> = self.accounts.keys().cloned().collect();
        
        for account_id in account_ids {
            if let Err(e) = self.validate_account(&account_id).await {
                eprintln!("Erreur validation compte {}: {}", account_id, e);
                // Marquer comme invalide
                if let Some(account) = self.accounts.get_mut(&account_id) {
                    account.status = AccountStatus::Invalid;
                }
            }
        }
        
        self.save_accounts().await?;
        
        Ok(())
    }
    
    /// Valider un compte spécifique
    pub async fn validate_account(&mut self, account_id: &str) -> Result<(), LoginManagerError> {
        if let Some(storage) = &self.secure_storage {
            match storage.load_token_for_account(account_id).await {
                Ok(Some(token)) => {
                    match self.validate_token(&token).await {
                        Ok(user_info) => {
                            // Mettre à jour les infos du compte
                            if let Some(account) = self.accounts.get_mut(account_id) {
                                account.username = user_info.username;
                                account.discriminator = user_info.discriminator;
                                account.avatar = user_info.avatar;
                                account.verified = user_info.verified;
                                account.mfa_enabled = user_info.mfa_enabled;
                                account.premium_type = user_info.premium_type;
                                account.last_validated = Some(Utc::now());
                                account.status = AccountStatus::Active;
                                account.guild_count = user_info.guild_count;
                                account.friend_count = user_info.friend_count;
                            }
                            Ok(())
                        },
                        Err(e) => {
                            // Marquer comme invalide
                            if let Some(account) = self.accounts.get_mut(account_id) {
                                account.status = AccountStatus::Invalid;
                                account.last_validated = Some(Utc::now());
                            }
                            Err(e)
                        }
                    }
                },
                Ok(None) => Err(LoginManagerError::TokenNotFound(account_id.to_string())),
                Err(e) => Err(LoginManagerError::StorageError(format!("Load token: {}", e))),
            }
        } else {
            Err(LoginManagerError::StorageError("No secure storage".to_string()))
        }
    }
    
    /// Obtenir les statistiques
    pub fn get_stats(&self) -> AccountStats {
        let active_count = self.accounts.values()
            .filter(|acc| acc.status == AccountStatus::Active)
            .count();
        
        let invalid_count = self.accounts.values()
            .filter(|acc| acc.status == AccountStatus::Invalid)
            .count();
        
        AccountStats {
            total_accounts: self.accounts.len(),
            active_accounts: active_count,
            invalid_accounts: invalid_count,
            last_switch: self.get_active_account().map(|acc| acc.last_used),
            switch_count: 0, // TODO: Tracker les switches
            total_login_time: 0, // TODO: Tracker le temps
        }
    }
    
    /// Nettoyer les comptes invalides
    pub async fn cleanup_invalid_accounts(&mut self) -> Result<Vec<String>, LoginManagerError> {
        let invalid_accounts: Vec<String> = self.accounts.iter()
            .filter(|(_, acc)| acc.status == AccountStatus::Invalid)
            .map(|(id, _)| id.clone())
            .collect();
        
        for account_id in &invalid_accounts {
            self.remove_account(account_id).await?;
        }
        
        Ok(invalid_accounts)
    }
    
    /// Sauvegarder les comptes (sans tokens)
    async fn save_accounts(&self) -> Result<(), LoginManagerError> {
        // TODO: Sauvegarder dans la base de données ou fichier
        Ok(())
    }
    
    /// Charger les comptes
    async fn load_accounts(&mut self) -> Result<(), LoginManagerError> {
        // TODO: Charger depuis la base de données ou fichier
        Ok(())
    }
    
    /// Valider un token Discord
    async fn validate_token(&self, _token: &str) -> Result<DiscordUserInfo, LoginManagerError> {
        // TODO: Appel API Discord pour valider le token
        // Pour l'instant, simulation
        Ok(DiscordUserInfo {
            id: "*********".to_string(),
            username: "TestUser".to_string(),
            discriminator: "0000".to_string(),
            avatar: None,
            email: None,
            phone: None,
            verified: true,
            mfa_enabled: false,
            premium_type: PremiumType::None,
            flags: 0,
            features: vec![],
            guild_count: Some(10),
            friend_count: Some(50),
        })
    }
    
    /// Hasher un token pour l'identification
    fn hash_token(&self, token: &str) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(token.as_bytes());
        format!("{:x}", hasher.finalize())
    }
}

/// Informations utilisateur Discord
#[derive(Debug, Clone)]
pub struct DiscordUserInfo {
    pub id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub verified: bool,
    pub mfa_enabled: bool,
    pub premium_type: PremiumType,
    pub flags: u64,
    pub features: Vec<String>,
    pub guild_count: Option<u64>,
    pub friend_count: Option<u64>,
}

/// Erreurs du Login Manager
#[derive(Debug, thiserror::Error)]
pub enum LoginManagerError {
    #[error("Limite de comptes atteinte")]
    LimitReached,
    
    #[error("Compte existe déjà: {0}")]
    AccountExists(String),
    
    #[error("Compte non trouvé: {0}")]
    AccountNotFound(String),
    
    #[error("Compte invalide: {0}")]
    AccountInvalid(String),
    
    #[error("Compte banni: {0}")]
    AccountBanned(String),
    
    #[error("Compte désactivé: {0}")]
    AccountDisabled(String),
    
    #[error("Compte indisponible: {0}")]
    AccountUnavailable(String),
    
    #[error("Token non trouvé: {0}")]
    TokenNotFound(String),
    
    #[error("Token invalide")]
    TokenInvalid,
    
    #[error("Erreur de stockage: {0}")]
    StorageError(String),
    
    #[error("Erreur API Discord: {0}")]
    DiscordApiError(String),
    
    #[error("Erreur de validation: {0}")]
    ValidationError(String),
}

// Extensions pour SecureStorage
impl SecureStorage {
    pub async fn save_token_for_account(&self, _account_id: &str, token: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implémenter le stockage par compte
        self.save_token(token).await.map_err(|e| e.into())
    }
    
    pub async fn load_token_for_account(&self, _account_id: &str) -> Result<Option<String>, Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implémenter le chargement par compte
        match self.load_token().await.map_err(|e| -> Box<dyn std::error::Error + Send + Sync> { e.into() })? {
            Some(secure_token) => Ok(Some(secure_token.as_str().to_string())),
            None => Ok(None),
        }
    }
    
    pub async fn delete_token_for_account(&self, _account_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // TODO: Implémenter la suppression par compte
        Ok(())
    }
}