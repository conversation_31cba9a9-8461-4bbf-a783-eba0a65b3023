# Rich Presence Frontend Implementation

## Vue d'ensemble

L'interface React pour Rich Presence a été créée avec une intégration complète au système Dynamic Variables du backend Rust. Cette interface offre une expérience utilisateur moderne et intuitive pour gérer les Rich Presence Discord et autres plateformes.

## Fonctionnalités principales

### 🎯 Présence Actuelle
- **Affichage en temps réel** de la présence active
- **Statut visuel** avec indicateur d'état
- **Arrêt de présence** d'un clic
- **Templates rapides** pour créer des présences par plateforme

### ✏️ Éditeur de Rich Presence
- **Formulaire complet** avec tous les champs Rich Presence
- **Intégration Variables Dynamiques** avec boutons d'insertion
- **Aperçu en temps réel** de la présence
- **Validation des données** avant envoi
- **Support multi-plateformes** (Discord, Spotify, Steam, Xbox, etc.)

### 📋 Gestion des Presets
- **Création de presets** avec nom, description et tags
- **Recherche et filtrage** par nom, description ou tags
- **Statistiques d'utilisation** avec compteur et dernière utilisation
- **Variables automatiques** configurables par preset
- **Import/Export** des presets (future feature)

### 📊 Historique
- **Suivi complet** des présences passées
- **Source tracking** (Manual, Preset, Auto, Platform)
- **Durée des sessions** calculée automatiquement
- **Affichage chronologique** avec détails

### ⚙️ Configuration
- **Paramètres généraux** (intervalles, limites)
- **Intégration Variables Dynamiques** activable/désactivable
- **Gestion des plateformes** supportées
- **Options de fallback** en cas d'erreur

## Architecture technique

### Types TypeScript
Les types correspondent exactement aux structures Rust :

```typescript
interface RichPresence {
  activity_type: ActivityType;
  name: string;
  details?: string;
  state?: string;
  timestamps?: ActivityTimestamps;
  assets?: ActivityAssets;
  party?: ActivityParty;
  secrets?: ActivitySecrets;
  buttons: ActivityButton[];
  platform: PlatformType;
  flags: number;
  metadata: Record<string, string>;
}
```

### Composants principaux

#### `RichPresence.tsx` (Composant principal)
- **État global** de l'interface Rich Presence
- **Gestion des onglets** (Current, Editor, Presets, History, Config)
- **Communication Tauri** avec le backend Rust
- **Gestion d'erreurs** avec notifications toast

#### `PresenceCard.tsx` (Composant d'affichage)
- **Affichage standardisé** des présences
- **Icônes par plateforme** et type d'activité
- **Actions contextuelles** (Modifier, Utiliser, Supprimer)
- **Support état actif** avec indicateur visuel

#### `PresetCard.tsx` (Composant preset)
- **Affichage des presets** avec métadonnées
- **Tags visuels** et statistiques d'utilisation
- **Actions preset** (Utiliser, Modifier, Supprimer)
- **Indicateur variables auto** si activé

### Intégration Dynamic Variables

L'interface s'intègre parfaitement au système de variables dynamiques :

1. **Chargement des variables** disponibles via `get_available_variables()`
2. **Boutons d'insertion** rapide dans les champs de texte
3. **Aperçu résolu** en temps réel des variables
4. **Copie en un clic** des variables dans le presse-papier
5. **Templates pré-remplis** avec variables appropriées

### Communication Backend

#### Commandes Tauri implémentées
```typescript
// Configuration
await invoke<RichPresenceConfig>('get_rich_presence_config');
await invoke('update_rich_presence_config', { config });

// Présences
await invoke<RichPresence | null>('get_current_presence');
await invoke('set_rich_presence', { presence });
await invoke('stop_rich_presence');

// Presets
await invoke<RichPresencePreset[]>('get_rich_presence_presets');
await invoke('create_rich_presence_preset', { name, description, presence, autoVariables });
await invoke('use_rich_presence_preset', { presetId });
await invoke('delete_rich_presence_preset', { presetId });

// Templates & Historique
await invoke<RichPresence>('create_platform_presence', { platform });
await invoke<PresenceHistoryEntry[]>('get_presence_history');

// Variables
await invoke<string[]>('get_available_variables');
```

## UX/UI Design

### Thème visuel
- **Gradient backgrounds** cohérents avec l'application
- **Couleurs par plateforme** pour une identification rapide
- **Animations Framer Motion** pour les interactions
- **Icons emoji** pour une interface conviviale

### Navigation
- **Onglets horizontaux** pour les différentes sections
- **Breadcrumb** visuel avec icônes
- **États visuels** (actif, hover, disabled)
- **Responsive design** pour différentes tailles d'écran

### Feedback utilisateur
- **Toast notifications** pour les actions
- **Loading states** pendant les requêtes
- **Validation en temps réel** des formulaires
- **Confirmations** pour les actions destructives

## Fonctionnalités avancées

### Templates par plateforme
Création automatique de présences optimisées :
- **Spotify** : `{spotify_track}` par `{spotify_artist}`
- **Gaming** : Templates Xbox/PlayStation/Steam
- **Streaming** : Templates Twitch/YouTube
- **Développement** : Templates VS Code avec variables système

### Recherche et filtrage
- **Recherche textuelle** dans noms et descriptions
- **Filtrage par tags** avec dropdown
- **Résultats en temps réel** sans rechargement
- **État vide** avec suggestions d'actions

### Variables dynamiques avancées
- **Insertion contextuelle** selon le champ
- **Preview en temps réel** de la résolution
- **Validation des variables** avant envoi
- **Fallback graceful** si variable indisponible

## Sécurité et validation

### Validation côté client
- **Longueur des champs** (Discord limits)
- **URLs valides** pour les boutons
- **Caractères autorisés** selon les plateformes
- **Nombre de boutons** (max 2)

### Gestion d'erreurs
- **Try-catch** sur toutes les requêtes Tauri
- **Messages d'erreur** contextuels et traduits
- **Fallback graceful** si backend indisponible
- **Retry logic** pour les opérations critiques

## Performance

### Optimisations
- **Debounced inputs** pour la recherche
- **Lazy loading** des composants lourds
- **Memoization** des calculs coûteux
- **Virtual scrolling** pour l'historique (future)

### Gestion d'état
- **État local React** pour l'UI
- **Synchronisation** avec le backend Rust
- **Cache intelligent** des données fréquentes
- **Invalidation** appropriée lors des mutations

## Extensions futures

### Prévues
- **Drag & Drop** pour réorganiser les presets
- **Import/Export** des configurations complètes
- **Thèmes personnalisés** par preset
- **Scheduling** automatique des présences
- **Intégration calendrier** pour présences contextuelles

### Techniques
- **WebSocket** pour updates temps réel
- **Offline support** avec synchronisation différée
- **Plugin system** pour nouvelles plateformes
- **API REST** pour intégrations externes

## Testing

### Unit tests
- **Composants isolés** avec Jest/RTL
- **Logique métier** pure functions
- **Mocks Tauri** pour les tests
- **Snapshots** pour les rendus

### Integration tests
- **Flows utilisateur** complets
- **Communication backend** simulée
- **États d'erreur** et edge cases
- **Performance** sous charge

## Déploiement

L'interface Rich Presence est intégrée dans l'application Tauri principale et sera automatiquement disponible après compilation. Aucune configuration supplémentaire requise.

### Build
```bash
cd voidbot-desktop
npm run build
npm run tauri build
```

### Development
```bash
cd voidbot-desktop
npm run dev
# ou
npm run tauri dev
```

---

Cette implémentation offre une interface moderne et complète pour le système Rich Presence, avec une intégration parfaite au système Dynamic Variables et une expérience utilisateur optimale.