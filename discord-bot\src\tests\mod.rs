/// Tests pour VoidBot Discord
/// 
/// Ce module contient tous les tests unitaires et d'intégration
/// pour valider le bon fonctionnement des composants critiques.

pub mod event_emitter_tests;
pub mod notification_tests;
pub mod error_handling_tests;
// pub mod integration_tests; // TODO: Créer plus tard

#[cfg(test)]
mod test_utils {
    use serenity::all::*;
    use std::sync::Arc;
    use tokio::sync::mpsc;
    use voidbot_shared::IpcMessage;
    use crate::event_emitter::EventEmitter;

    /// Créer un émetteur d'événements de test
    pub fn create_test_event_emitter() -> (EventEmitter, mpsc::UnboundedReceiver<IpcMessage>) {
        let (sender, receiver) = mpsc::unbounded_channel();
        let emitter = EventEmitter::new(Some(Arc::new(sender)), true);
        (emitter, receiver)
    }

    /// Créer un utilisateur de test
    pub fn create_test_user() -> User {
        User {
            id: UserId::new(123456789),
            name: "TestUser".to_string(),
            discriminator: Some(1234),
            global_name: Some("Test User".to_string()),
            avatar: None,
            bot: false,
            system: false,
            mfa_enabled: false,
            banner: None,
            accent_colour: None,
            locale: None,
            verified: Some(true),
            email: None,
            flags: Some(UserPublicFlags::empty()),
            premium_type: None,
            public_flags: Some(UserPublicFlags::empty()),
            avatar_decoration: None,
        }
    }

    /// Créer un message de test
    pub fn create_test_message() -> Message {
        let user = create_test_user();
        Message {
            id: MessageId::new(987654321),
            channel_id: ChannelId::new(111222333),
            author: user.clone(),
            content: "Test message content".to_string(),
            timestamp: chrono::Utc::now().into(),
            edited_timestamp: None,
            tts: false,
            mention_everyone: false,
            mentions: vec![user],
            mention_roles: vec![],
            mention_channels: vec![],
            attachments: vec![],
            embeds: vec![],
            reactions: vec![],
            nonce: None,
            pinned: false,
            webhook_id: None,
            kind: MessageType::Regular,
            activity: None,
            application: None,
            application_id: None,
            message_reference: None,
            flags: Some(MessageFlags::empty()),
            referenced_message: None,
            interaction: None,
            thread: None,
            components: vec![],
            sticker_items: vec![],
            stickers: vec![],
            position: None,
            role_subscription_data: None,
            resolved: None,
            guild_id: Some(GuildId::new(444555666)),
            member: None,
            call: None,
        }
    }

    /// Créer un contexte de test mock
    pub fn create_mock_context() -> Context {
        // Note: En réalité, créer un Context complet est complexe
        // Pour les tests, on utilisera des mocks ou des traits
        unimplemented!("Mock context creation - use trait abstractions instead")
    }
}
