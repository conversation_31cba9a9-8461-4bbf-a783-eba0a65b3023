---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const coreFeatures = [
  {
    icon: "👻",
    title: "Mode Furtif Avanc<PERSON>",
    description: "Basculez instantanément entre mode normal et fantôme pour des réponses visibles par tous ou seulement par vous.",
    details: [
      "Switch temps réel sans redémarrage",
      "Commandes sensibles auto-fantôme",
      "Interface dédiée de contrôle",
      "Sécurité maximale garantie"
    ]
  },
  {
    icon: "🎁",
    title: "Giveaway Joiner Ultra-Intelligent",
    description: "Participation automatique aux giveaways avec détection avancée et filtrage intelligent.",
    details: [
      "Détection regex ultra-optimisée",
      "Délais aléatoires anti-détection",
      "Filtrage serveurs/mots-clés",
      "Statistiques détaillées",
      "Support multi-comptes"
    ]
  },
  {
    icon: "💎",
    title: "<PERSON><PERSON>",
    description: "Capture instantanée des codes Nitro Discord en 50-200ms avec analytics complètes.",
    details: [
      "Temps de réaction 50-200ms",
      "Regex ultra-optimisés",
      "Filtrage bots de confiance",
      "Mode test sécurisé",
      "Analytics temps réel"
    ]
  },
  {
    icon: "🔔",
    title: "Notification Center Complet",
    description: "14 types d'événements surveillés avec ghostping en temps réel et notifications desktop.",
    details: [
      "Ghostping instantané",
      "Surveillance amis/serveurs",
      "Détection mots-clés",
      "Notifications desktop",
      "Interface moderne"
    ]
  },
  {
    icon: "🎪",
    title: "Système de Trolls Avancé",
    description: "5 types de trolls avec sécurité intégrée et interface de contrôle temps réel.",
    details: [
      "NoLeave, GhostPing, Spam",
      "FakeTyping, InvisibleMessage",
      "Limites anti-abus",
      "Auto-stop configurable",
      "Mode fantôme forcé"
    ]
  },
  {
    icon: "🤖",
    title: "Auto-Commands Intelligents",
    description: "Traduction automatique, slash commands et réponses avec IA intégrée.",
    details: [
      "Auto-Translate (3 services)",
      "Auto-Slash avec triggers",
      "Auto-Reply intelligent",
      "Cooldowns avancés",
      "Configuration granulaire"
    ]
  },
  {
    icon: "🎨",
    title: "Générateur d'Images Complet",
    description: "Création automatique de memes, overlays, bannières et statistiques.",
    details: [
      "5 types d'images supportés",
      "Engine optimisé",
      "Templates personnalisables",
      "Résolution configurable",
      "Compression intelligente"
    ]
  },
  {
    icon: "⚡",
    title: "Rate Limiting Robuste",
    description: "Protection avancée contre les bans Discord avec monitoring temps réel.",
    details: [
      "Buckets par endpoint",
      "Protection 429 avancée",
      "Backoff exponentiel",
      "Stats temps réel",
      "Interface de monitoring"
    ]
  },
  {
    icon: "🗃️",
    title: "Base de Données SQLite",
    description: "Stockage sécurisé avec 9 tables structurées et interface de gestion complète.",
    details: [
      "9 tables optimisées",
      "Indexes performants",
      "Migrations automatiques",
      "Monitoring usage",
      "Cleanup automatique"
    ]
  }
];

const comparisonFeatures = [
  {
    feature: "Interface Utilisateur",
    voidbot: "Desktop native Tauri + React",
    nighty: "Web browser only",
    voidbotBetter: true
  },
  {
    feature: "Sécurité",
    voidbot: "100% local + chiffrement AES-256",
    nighty: "Cloud-based",
    voidbotBetter: true
  },
  {
    feature: "Performance",
    voidbot: "Rust natif ultra-rapide",
    nighty: "JavaScript web",
    voidbotBetter: true
  },
  {
    feature: "Giveaway Joiner",
    voidbot: "✅ Détection intelligente",
    nighty: "✅ Basique",
    voidbotBetter: true
  },
  {
    feature: "Nitro Sniper",
    voidbot: "✅ 50-200ms",
    nighty: "✅ ~500ms+",
    voidbotBetter: true
  },
  {
    feature: "Mode Furtif",
    voidbot: "✅ Switch temps réel",
    nighty: "✅ Basique",
    voidbotBetter: true
  },
  {
    feature: "Auto-Commands",
    voidbot: "✅ 3 types avancés",
    nighty: "✅ Limité",
    voidbotBetter: true
  },
  {
    feature: "Trolls",
    voidbot: "✅ 5 types sécurisés",
    nighty: "✅ Basique",
    voidbotBetter: true
  },
  {
    feature: "Notifications",
    voidbot: "✅ 14 types + desktop",
    nighty: "✅ Basique",
    voidbotBetter: true
  },
  {
    feature: "Prix",
    voidbot: "100% Gratuit",
    nighty: "Freemium + Premium",
    voidbotBetter: true
  }
];

const securityFeatures = [
  {
    icon: "🔐",
    title: "Chiffrement AES-256-GCM",
    description: "Tous vos tokens Discord sont chiffrés avec la norme militaire AES-256-GCM."
  },
  {
    icon: "🏠",
    title: "100% Local",
    description: "Aucune donnée n'est envoyée à des serveurs externes. Tout reste sur votre machine."
  },
  {
    icon: "🛡️",
    title: "Code Source Auditable",
    description: "Architecture Rust open-source que vous pouvez examiner et compiler vous-même."
  },
  {
    icon: "🔒",
    title: "CSP Tauri Activé",
    description: "Protection XSS avec Content Security Policy strict et plugin Shell désactivé."
  }
];
---

<Layout title="Fonctionnalités - VoidBot" description="Découvrez toutes les fonctionnalités avancées de VoidBot : snipers ultra-rapides, trolls sécurisés, auto-commands intelligents et bien plus.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20 lg:py-32">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl sm:text-6xl font-bold mb-6">
          <span class="gradient-text">Fonctionnalités</span>
          <br>
          <span class="text-white">Avancées</span>
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          Découvrez tous les outils qui font de VoidBot le toolkit Discord le plus puissant et sécurisé disponible aujourd'hui.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/download" class="btn btn-primary text-lg px-8 py-4 glow">
            Télécharger VoidBot
          </a>
          <a href="#comparison" class="btn btn-secondary text-lg px-8 py-4">
            Comparer avec Nighty
          </a>
        </div>
      </div>
    </section>

    <!-- Core Features -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">
            <span class="gradient-text">Fonctionnalités</span>
            <span class="text-white"> Core</span>
          </h2>
          <p class="text-xl text-gray-300">
            Toutes les fonctionnalités principales qui rendent VoidBot unique
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {coreFeatures.map((feature, index) => (
            <div class="card-hover group slide-up" style={`animation-delay: ${index * 0.1}s`}>
              <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-200">
                {feature.icon}
              </div>
              <h3 class="text-xl font-semibold mb-3 text-white group-hover:text-indigo-400 transition-colors duration-200">
                {feature.title}
              </h3>
              <p class="text-gray-400 mb-4 group-hover:text-gray-300 transition-colors duration-200">
                {feature.description}
              </p>
              <ul class="space-y-2">
                {feature.details.map((detail) => (
                  <li class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 text-indigo-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {detail}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Security Section -->
    <section class="py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">
            <span class="gradient-text">Sécurité</span>
            <span class="text-white"> Niveau Entreprise</span>
          </h2>
          <p class="text-xl text-gray-300">
            Vos données et tokens Discord protégés par les meilleures pratiques de sécurité
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          {securityFeatures.map((feature, index) => (
            <div class="card-hover group">
              <div class="flex items-start gap-4">
                <div class="text-3xl group-hover:scale-110 transition-transform duration-200">
                  {feature.icon}
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-2 text-white group-hover:text-indigo-400 transition-colors duration-200">
                    {feature.title}
                  </h3>
                  <p class="text-gray-400 group-hover:text-gray-300 transition-colors duration-200">
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Comparison with Nighty -->
    <section id="comparison" class="py-20 bg-gray-900/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">
            <span class="gradient-text">VoidBot</span>
            <span class="text-white"> vs </span>
            <span class="text-orange-400">Nighty</span>
          </h2>
          <p class="text-xl text-gray-300">
            Comparaison détaillée des fonctionnalités et avantages
          </p>
        </div>

        <div class="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl overflow-hidden">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-950/50">
                <tr>
                  <th class="px-6 py-4 text-left text-sm font-semibold text-gray-300">Fonctionnalité</th>
                  <th class="px-6 py-4 text-center text-sm font-semibold text-indigo-400">VoidBot</th>
                  <th class="px-6 py-4 text-center text-sm font-semibold text-orange-400">Nighty</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-800">
                {comparisonFeatures.map((item, index) => (
                  <tr class="hover:bg-gray-900/30 transition-colors duration-200">
                    <td class="px-6 py-4 text-sm font-medium text-white">{item.feature}</td>
                    <td class="px-6 py-4 text-sm text-center">
                      <span class={item.voidbotBetter ? "text-green-400 font-medium" : "text-gray-300"}>
                        {item.voidbot}
                      </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-center text-gray-400">
                      {item.nighty}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div class="mt-12 text-center">
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-green-500/10 border border-green-500/20 mb-6">
            <span class="text-green-400 text-sm font-medium">✅ VoidBot gagne sur tous les points</span>
          </div>
          <p class="text-lg text-gray-300 mb-8">
            VoidBot offre une expérience supérieure avec plus de sécurité, de performance et de fonctionnalités avancées.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/download" class="btn btn-primary text-lg px-8 py-4 glow">
              Essayer VoidBot Maintenant
            </a>
            <a href="/docs" class="btn btn-secondary text-lg px-8 py-4">
              Lire la Documentation
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Performance Stats -->
    <section class="py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">
            <span class="gradient-text">Performance</span>
            <span class="text-white"> Exceptionnelle</span>
          </h2>
          <p class="text-xl text-gray-300">
            Des chiffres qui parlent d'eux-mêmes
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center card-hover">
            <div class="text-4xl font-bold gradient-text-primary mb-2">50ms</div>
            <div class="text-sm text-gray-400">Temps de réaction Nitro Sniper</div>
          </div>
          <div class="text-center card-hover">
            <div class="text-4xl font-bold gradient-text-primary mb-2">100+</div>
            <div class="text-sm text-gray-400">Commandes Discord disponibles</div>
          </div>
          <div class="text-center card-hover">
            <div class="text-4xl font-bold gradient-text-primary mb-2">14</div>
            <div class="text-sm text-gray-400">Types d'événements surveillés</div>
          </div>
          <div class="text-center card-hover">
            <div class="text-4xl font-bold gradient-text-primary mb-2">0%</div>
            <div class="text-sm text-gray-400">Données envoyées au cloud</div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-indigo-900/20 to-purple-900/20">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl sm:text-4xl font-bold mb-6">
          Prêt à <span class="gradient-text">révolutionner</span> votre expérience Discord ?
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          Téléchargez VoidBot maintenant et découvrez la différence d'un toolkit vraiment avancé.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/download" class="btn btn-primary text-lg px-8 py-4 glow">
            Télécharger Gratuitement
          </a>
          <a href="/support" class="btn btn-secondary text-lg px-8 py-4">
            Obtenir de l'Aide
          </a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>