# 🚀 Guide de Build des Installeurs VoidBot

## 📋 Vue d'ensemble

Ce guide explique comment générer tous les installeurs one-click de VoidBot pour Windows, macOS et Linux. Le système de build automatisé génère 6 types d'installeurs différents avec configuration optimisée.

---

## 🎯 Installeurs générés

### 🪟 **Windows**
- **MSI** (x64/x86) - Installeur classique Windows
- **NSIS** (x64/x86) - Installeur moderne avec interface graphique

### 🍎 **macOS**
- **DMG** (Universal) - Compatible Intel + Apple Silicon

### 🐧 **Linux**
- **DEB** (x64/ARM64) - Packages Debian/Ubuntu
- **AppImage** (x64/ARM64) - Application portable

---

## ⚡ Build rapide

### Option 1: Script automatisé (Recommandé)
```bash
cd voidbot-desktop
npm run build:installers
```

### Option 2: Script manuel
```bash
./scripts/build-installers.sh
```

### Option 3: Build plateforme spécifique
```bash
# Windows uniquement
npm run build:windows

# macOS uniquement  
npm run build:macos

# Linux uniquement
npm run build:linux
```

---

## 🔧 Prérequis

### Dépendances système
- **Node.js** 18+ avec npm
- **Rust** 1.70+ avec Cargo
- **Tauri CLI** (`npm install -g @tauri-apps/cli`)

### Optionnel pour assets
- **Python 3** avec Pillow (`pip install Pillow`)

### Vérification rapide
```bash
./scripts/test-build.sh
```

---

## 📦 Structure des artefacts

Après le build, les installeurs se trouvent dans:

```
builds/
├── windows/
│   ├── VoidBot_1.0.0_x64.msi
│   ├── VoidBot_1.0.0_x86.msi
│   ├── VoidBot_1.0.0_x64-setup.exe
│   ├── VoidBot_1.0.0_x86-setup.exe
│   └── SHA256SUMS
├── macos/
│   ├── VoidBot_1.0.0_universal.dmg
│   └── SHA256SUMS
└── linux/
    ├── voidbot_1.0.0_amd64.deb
    ├── voidbot_1.0.0_arm64.deb
    ├── VoidBot_1.0.0_x86_64.AppImage
    ├── VoidBot_1.0.0_aarch64.AppImage
    └── SHA256SUMS
```

---

## 🛠️ Configuration avancée

### Variables d'environnement
```bash
# Version de build (optionnel)
export CARGO_PKG_VERSION="1.0.0"

# Mode verbeux
export RUST_LOG=debug

# Optimisations de build
export CARGO_BUILD_JOBS=4
```

### Profils de build
- **Dev** : `npm run tauri dev`
- **Release** : `npm run tauri build`
- **Production** : `npm run tauri:build:prod`

---

## 🎨 Personnalisation des installeurs

### Windows (NSIS/MSI)
Les templates sont dans `src-tauri/`:
- `installer.nsi` - Template NSIS
- `main.wxs` - Template WiX MSI
- `assets/header-image.bmp` - Image d'en-tête
- `assets/sidebar-image.bmp` - Image latérale

### macOS (DMG)
Configuration dans `tauri.conf.json` > `bundle.macOS.dmg`:
- Position des icônes
- Taille de la fenêtre
- Image de fond (optionnelle)

### Linux (DEB/AppImage)
Configuration dans `tauri.conf.json` > `bundle.linux`:
- Dépendances system
- Fichiers desktop
- Icônes système

---

## 🚨 Résolution de problèmes

### Erreur: "Assets BMP manquants"
```bash
# Générer automatiquement
cd voidbot-desktop/src-tauri
python3 build-assets.py

# Ou manuellement
npm run assets:generate
```

### Erreur: "Tauri CLI non trouvé"
```bash
npm install -g @tauri-apps/cli@latest
```

### Erreur: "Configuration JSON invalide"
```bash
# Valider la configuration
jq empty src-tauri/tauri.conf.json
```

### Erreur de compilation Rust
```bash
# Nettoyer et reconstruire
cargo clean
cargo update
cargo check
```

---

## 📊 Optimisations de performance

### Build release optimisé
Le fichier `Cargo.toml` contient des optimisations:
```toml
[profile.release]
lto = true
strip = true  
panic = "abort"
codegen-units = 1
```

### Réduction de taille
- **LTO** (Link Time Optimization) activé
- **Strip** des symboles debug
- **Panic abort** au lieu d'unwind
- **Single codegen unit** pour optimisations maximales

---

## 🔐 Sécurité et signature

### Windows
- Support signature code signing (certificat requis)
- Validation Windows Defender automatique
- Installeur sans élévation de privilèges

### macOS
- Notarisation Apple (compte développeur requis)
- Hardened Runtime activé
- Entitlements configurés

### Linux
- Validation GPG des packages (optionnelle)
- Intégration gestionnaires de paquets
- AppImage avec vérification intégrité

---

## 📈 Métriques de build

### Tailles moyennes
- **Windows MSI**: ~25-30 MB
- **Windows NSIS**: ~23-28 MB
- **macOS DMG**: ~30-35 MB  
- **Linux DEB**: ~25-30 MB
- **Linux AppImage**: ~28-33 MB

### Temps de build (approximatifs)
- **Dev build**: 2-3 minutes
- **Release build**: 5-8 minutes  
- **Build multi-plateforme**: 15-25 minutes

---

## 🚀 Distribution automatisée

### GitHub Releases
```bash
# Script de release complet (à venir)
./scripts/github-release.sh v1.0.0
```

### Checksums de sécurité
Tous les builds incluent des fichiers `SHA256SUMS` pour vérification:
```bash
# Vérifier un installeur
sha256sum -c SHA256SUMS
```

---

## 💡 Tips et bonnes pratiques

### Performance
- Utiliser `npm ci` au lieu de `npm install` en CI
- Paralléliser les builds multi-architectures
- Cache des dépendances Rust entre builds

### Debugging
- Logs détaillés avec `RUST_LOG=debug`
- Build incrémental avec `cargo check`
- Validation préalable avec `test-build.sh`

### Maintenance
- Mettre à jour Tauri régulièrement
- Tester sur machines virtuelles propres
- Vérifier compatibilité OS cibles

---

## 📞 Support

### Erreurs communes
1. **Assets manquants** → Exécuter `assets:generate`
2. **Dépendances Rust** → `cargo update && cargo check`
3. **Configuration Tauri** → Valider avec `jq`
4. **Permissions build** → `chmod +x scripts/*.sh`

### Aide supplémentaire
- Documentation Tauri: https://tauri.app/v2/guides/
- Repo GitHub: Issues et Discussions
- Discord VoidBot: Support communautaire

---

**État**: ✅ **Configuration complète et opérationnelle**
**Version**: v1.0 Production Ready
**Dernière mise à jour**: Janvier 2025