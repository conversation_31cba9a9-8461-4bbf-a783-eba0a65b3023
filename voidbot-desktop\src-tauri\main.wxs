<?xml version='1.0' encoding='windows-1252'?>

<!--
  VoidBot WiX Installer Template
  Ultimate Discord Toolkit
-->

<Wix xmlns='http://schemas.microsoft.com/wix/2006/wi'>

  <Product 
    Name='VoidBot' 
    Manufacturer='VoidBot Team'
    Id='{{product_code}}'
    UpgradeCode='{{upgrade_code}}'
    Language='1036'
    Codepage='1252'
    Version='{{product_version}}'>

    <Package 
      Id='*'
      Keywords='Installer'
      Description='VoidBot Ultimate Discord Toolkit'
      Manufacturer='VoidBot Team'
      InstallerVersion='200'
      Languages='1036'
      Compressed='yes'
      SummaryCodepage='1252' />

    <!-- Conditions d'installation -->
    <Condition Message='Cette application nécessite Windows 10 ou plus récent.'>
      <![CDATA[Installed OR (VersionNT >= 1000)]]>
    </Condition>

    <!-- Média d'installation -->
    <Media Id='1' Cabinet='app.cab' EmbedCab='yes' />

    <!-- Structure des répertoires -->
    <Directory Id='TARGETDIR' Name='SourceDir'>
      <Directory Id='{{program_files_folder}}' Name='PFiles'>
        <Directory Id='APPLICATIONFOLDER' Name='VoidBot'>
          
          <!-- Fichiers principaux -->
          <Component Id='MainExecutable' Guid='{{main_executable_guid}}'>
            <File 
              Id='MainExe' 
              Name='{{app_executable_name}}' 
              Source='{{app_executable_source}}'
              KeyPath='yes'>
              <Shortcut 
                Id='DesktopShortcut'
                Directory='DesktopFolder'
                Name='VoidBot'
                Description='Ultimate Discord Toolkit'
                WorkingDirectory='APPLICATIONFOLDER'
                Icon='icon.ico' />
              <Shortcut 
                Id='StartMenuShortcut'
                Directory='ProgramMenuDir'
                Name='VoidBot'
                Description='Ultimate Discord Toolkit'
                WorkingDirectory='APPLICATIONFOLDER'
                Icon='icon.ico' />
            </File>
          </Component>

          <!-- Ressources additionnelles -->
          {{#each app_resources}}
          <Component Id='Resource{{@index}}' Guid='{{guid}}'>
            <File 
              Id='Resource{{@index}}'
              Name='{{name}}'
              Source='{{source}}'
              KeyPath='yes' />
          </Component>
          {{/each}}

        </Directory>
      </Directory>

      <!-- Dossier du menu Démarrer -->
      <Directory Id='ProgramMenuFolder'>
        <Directory Id='ProgramMenuDir' Name='VoidBot'>
          <Component Id='ProgramMenuComponent' Guid='{{program_menu_guid}}'>
            <RemoveFolder Id='ProgramMenuDir' On='uninstall' />
            <RegistryValue 
              Root='HKCU' 
              Key='Software\VoidBot\InstallDir' 
              Type='string' 
              Value='[APPLICATIONFOLDER]' 
              KeyPath='yes' />
          </Component>
        </Directory>
      </Directory>

      <!-- Dossier Bureau -->
      <Directory Id='DesktopFolder' Name='Desktop' />
    </Directory>

    <!-- Icône pour l'installeur -->
    <Icon Id='icon.ico' SourceFile='icons\icon.ico' />
    <Property Id='ARPPRODUCTICON' Value='icon.ico' />

    <!-- Propriétés du produit -->
    <Property Id='ARPHELPLINK' Value='https://voidbot.app/support' />
    <Property Id='ARPURLINFOABOUT' Value='https://voidbot.app' />
    <Property Id='ARPNOREPAIR' Value='yes' Secure='yes' />
    <Property Id='ARPNOMODIFY' Value='yes' Secure='yes' />

    <!-- Interface utilisateur -->
    <UIRef Id='WixUI_InstallDir' />
    <Property Id='WIXUI_INSTALLDIR' Value='APPLICATIONFOLDER' />

    <!-- Textes personnalisés -->
    <WixVariable Id='WixUILicenseRtf' Value='..\..\LICENSE.rtf' />
    <WixVariable Id='WixUIBannerBmp' Value='assets\banner.bmp' />
    <WixVariable Id='WixUIDialogBmp' Value='assets\dialog.bmp' />

    <!-- Feature principale -->
    <Feature Id='MainFeature' Title='VoidBot' Level='1'>
      <ComponentRef Id='MainExecutable' />
      <ComponentRef Id='ProgramMenuComponent' />
      {{#each app_resources}}
      <ComponentRef Id='Resource{{@index}}' />
      {{/each}}
    </Feature>

    <!-- Gestion des mises à niveau -->
    <MajorUpgrade 
      DowngradeErrorMessage='Une version plus récente de VoidBot est déjà installée.' />

  </Product>

</Wix>