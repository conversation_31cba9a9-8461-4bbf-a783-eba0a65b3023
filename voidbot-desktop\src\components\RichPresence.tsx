import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

// Types correspondant au backend Rust
interface RichPresenceConfig {
  enabled: boolean;
  auto_update_interval_seconds: number;
  use_dynamic_variables: boolean;
  max_presets: number;
  max_history_entries: number;
  platforms_enabled: PlatformType[];
  fallback_on_error: boolean;
}

interface RichPresence {
  activity_type: ActivityType;
  name: string;
  details?: string;
  state?: string;
  timestamps?: ActivityTimestamps;
  assets?: ActivityAssets;
  party?: ActivityParty;
  secrets?: ActivitySecrets;
  buttons: ActivityButton[];
  platform: PlatformType;
  flags: number;
  metadata: Record<string, string>;
}

interface RichPresencePreset {
  id: string;
  name: string;
  description?: string;
  presence: RichPresence;
  created_at: string;
  last_used?: string;
  use_count: number;
  tags: string[];
  auto_variables: boolean;
}

interface PresenceHistoryEntry {
  presence: RichPresence;
  started_at: string;
  ended_at?: string;
  duration_seconds?: number;
  source: PresenceSource;
}

interface ActivityTimestamps {
  start?: string;
  end?: string;
}

interface ActivityAssets {
  large_image?: string;
  large_text?: string;
  small_image?: string;
  small_text?: string;
}

interface ActivityParty {
  id?: string;
  size?: [number, number];
}

interface ActivitySecrets {
  join?: string;
  spectate?: string;
  match_secret?: string;
}

interface ActivityButton {
  label: string;
  url: string;
}

type ActivityType = 
  | 'Playing' | 'Streaming' | 'Listening' | 'Watching' | 'Custom' | 'Competing'
  | 'Gaming' | 'Reading' | 'Coding' | 'Browsing' | 'Learning' | 'Working';

type PlatformType = 
  | 'Discord' | 'Spotify' | 'Steam' | 'Xbox' | 'PlayStation' | 'Crunchyroll'
  | 'Netflix' | 'YouTube' | 'Twitch' | 'VisualStudioCode' | 'Chrome'
  | { Custom: string };

type PresenceSource = 
  | 'Manual' | { Preset: string } | { Auto: string } | { Platform: PlatformType };

// Composant pour afficher une présence
const PresenceCard = ({ presence, onEdit, onUse, isActive }: {
  presence: RichPresence;
  onEdit?: () => void;
  onUse?: () => void;
  isActive?: boolean;
}) => {
  const getPlatformIcon = (platform: PlatformType) => {
    switch (platform) {
      case 'Discord': return '💬';
      case 'Spotify': return '🎵';
      case 'Steam': return '🎮';
      case 'Xbox': return '🎮';
      case 'PlayStation': return '🎮';
      case 'Crunchyroll': return '📺';
      case 'Netflix': return '📺';
      case 'YouTube': return '🎥';
      case 'Twitch': return '📺';
      case 'VisualStudioCode': return '💻';
      case 'Chrome': return '🌐';
      default: return '📱';
    }
  };

  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case 'Playing': return '🎮';
      case 'Streaming': return '📹';
      case 'Listening': return '🎵';
      case 'Watching': return '👀';
      case 'Custom': return '✨';
      case 'Competing': return '🏆';
      case 'Gaming': return '🎮';
      case 'Reading': return '📖';
      case 'Coding': return '💻';
      case 'Browsing': return '🌐';
      case 'Learning': return '📚';
      case 'Working': return '💼';
      default: return '❓';
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      className={`bg-gradient-to-br from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-4 border transition-all duration-300 ${
        isActive 
          ? 'border-green-500/50 shadow-lg shadow-green-500/20' 
          : 'border-gray-600/50 hover:border-gray-500/50'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-2xl">{getPlatformIcon(presence.platform)}</span>
          <div>
            <div className="flex items-center space-x-2">
              <span className="text-lg">{getActivityIcon(presence.activity_type)}</span>
              <span className="text-white font-semibold">{presence.name}</span>
            </div>
            <span className="text-xs text-gray-400 capitalize">
              {presence.activity_type} sur {typeof presence.platform === 'string' ? presence.platform : 'Custom'}
            </span>
          </div>
        </div>
        {isActive && (
          <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full animate-pulse">
            ACTIF
          </span>
        )}
      </div>

      {presence.details && (
        <div className="mb-2">
          <span className="text-sm text-gray-300">📝 {presence.details}</span>
        </div>
      )}

      {presence.state && (
        <div className="mb-2">
          <span className="text-sm text-gray-300">💬 {presence.state}</span>
        </div>
      )}

      {presence.buttons.length > 0 && (
        <div className="mb-3">
          <div className="text-xs text-gray-400 mb-1">Boutons:</div>
          <div className="flex flex-wrap gap-2">
            {presence.buttons.map((button, idx) => (
              <span key={idx} className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded">
                {button.label}
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center pt-2 border-t border-gray-600/30">
        <div className="flex items-center space-x-3">
          {onEdit && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onEdit}
              className="text-xs bg-indigo-600/20 text-indigo-400 px-3 py-1 rounded hover:bg-indigo-600/30 transition-colors"
            >
              ✏️ Modifier
            </motion.button>
          )}
          {onUse && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onUse}
              className="text-xs bg-green-600/20 text-green-400 px-3 py-1 rounded hover:bg-green-600/30 transition-colors"
            >
              ✨ Utiliser
            </motion.button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Composant pour une carte de preset
const PresetCard = ({ preset, onUse, onEdit, onDelete }: {
  preset: RichPresencePreset;
  onUse: () => void;
  onEdit: () => void;
  onDelete: () => void;
}) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      className="bg-gradient-to-br from-purple-900/30 to-indigo-900/30 backdrop-blur-sm rounded-xl p-4 border border-purple-500/30"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="text-white font-semibold">{preset.name}</h4>
          {preset.description && (
            <p className="text-gray-300 text-sm mt-1">{preset.description}</p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {preset.auto_variables && (
            <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded" title="Variables dynamiques activées">
              ✨
            </span>
          )}
          <span className="text-xs bg-gray-600/20 text-gray-400 px-2 py-1 rounded">
            {preset.use_count} utilisations
          </span>
        </div>
      </div>

      <PresenceCard presence={preset.presence} />

      {preset.tags.length > 0 && (
        <div className="mt-3">
          <div className="flex flex-wrap gap-1">
            {preset.tags.map((tag, idx) => (
              <span key={idx} className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded">
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-600/30">
        <div className="text-xs text-gray-400">
          Créé le {new Date(preset.created_at).toLocaleDateString('fr-FR')}
          {preset.last_used && (
            <span className="block">
              Dernière utilisation: {new Date(preset.last_used).toLocaleDateString('fr-FR')}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onEdit}
            className="text-xs bg-indigo-600/20 text-indigo-400 px-3 py-1 rounded hover:bg-indigo-600/30 transition-colors"
          >
            ✏️
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onUse}
            className="text-xs bg-green-600/20 text-green-400 px-3 py-1 rounded hover:bg-green-600/30 transition-colors"
          >
            ▶️
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onDelete}
            className="text-xs bg-red-600/20 text-red-400 px-3 py-1 rounded hover:bg-red-600/30 transition-colors"
          >
            🗑️
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default function RichPresence() {
  const [config, setConfig] = useState<RichPresenceConfig>({
    enabled: true,
    auto_update_interval_seconds: 15,
    use_dynamic_variables: true,
    max_presets: 20,
    max_history_entries: 100,
    platforms_enabled: ['Discord', 'Spotify', 'Steam'],
    fallback_on_error: true,
  });

  const [currentPresence, setCurrentPresence] = useState<RichPresence | null>(null);
  const [presets, setPresets] = useState<RichPresencePreset[]>([]);
  const [history, setHistory] = useState<PresenceHistoryEntry[]>([]);
  const [availableVariables, setAvailableVariables] = useState<string[]>([]);

  const [activeTab, setActiveTab] = useState<'current' | 'editor' | 'presets' | 'history' | 'config'>('current');
  
  // Editor state
  const [editorPresence, setEditorPresence] = useState<Partial<RichPresence>>({
    activity_type: 'Playing',
    name: '',
    platform: 'Discord',
    buttons: [],
    flags: 0,
    metadata: {},
  });

  // Preset creation state
  const [showCreatePreset, setShowCreatePreset] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [presetDescription, setPresetDescription] = useState('');
  // const [presetTags, setPresetTags] = useState<string[]>([]); // Future: preset tags
  const [presetAutoVariables, setPresetAutoVariables] = useState(true);

  // Search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('');

  const activityTypes: ActivityType[] = [
    'Playing', 'Streaming', 'Listening', 'Watching', 'Custom', 'Competing',
    'Gaming', 'Reading', 'Coding', 'Browsing', 'Learning', 'Working'  ];

  const platforms: PlatformType[] = [
    'Discord', 'Spotify', 'Steam', 'Xbox', 'PlayStation', 'Crunchyroll',
    'Netflix', 'YouTube', 'Twitch', 'VisualStudioCode', 'Chrome'
  ];

  useEffect(() => {
    loadConfig();
    loadCurrentPresence();
    loadPresets();
    loadHistory();
    loadAvailableVariables();

    // Auto-refresh
    const interval = setInterval(() => {
      if (config.enabled) {
        loadCurrentPresence();
        loadHistory();
      }
    }, config.auto_update_interval_seconds * 1000);

    return () => clearInterval(interval);
  }, [config.enabled, config.auto_update_interval_seconds]);

  const loadConfig = async () => {
    try {
      const richPresenceConfig = await invoke<RichPresenceConfig>('get_rich_presence_config');
      setConfig(richPresenceConfig);
    } catch (error) {
      console.error('Erreur lors du chargement de la config Rich Presence:', error);
    }
  };

  const loadCurrentPresence = async () => {
    try {
      const presence = await invoke<RichPresence | null>('get_current_presence');
      setCurrentPresence(presence);
    } catch (error) {
      console.error('Erreur lors du chargement de la présence actuelle:', error);
    }
  };

  const loadPresets = async () => {
    try {
      const presetsData = await invoke<RichPresencePreset[]>('get_rich_presence_presets');
      setPresets(presetsData);
    } catch (error) {
      console.error('Erreur lors du chargement des presets:', error);
    }
  };

  const loadHistory = async () => {
    try {
      const historyData = await invoke<PresenceHistoryEntry[]>('get_presence_history');
      setHistory(historyData);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
    }
  };

  const loadAvailableVariables = async () => {
    try {
      const variables = await invoke<string[]>('get_available_variables');
      setAvailableVariables(variables);
    } catch (error) {
      console.error('Erreur lors du chargement des variables:', error);
    }
  };

  const setPresence = async (presence: RichPresence) => {
    try {
      await invoke('set_rich_presence', { presence });
      toast.success('✨ Rich Presence mise à jour', {
        style: { background: '#10B981', color: 'white' },
      });
      await loadCurrentPresence();
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const usePreset = async (presetId: string) => {
    try {
      await invoke('use_rich_presence_preset', { presetId });
      toast.success('🎯 Preset appliqué', {
        style: { background: '#10B981', color: 'white' },
      });
      await loadCurrentPresence();
      await loadPresets(); // Recharger pour mettre à jour les stats
    } catch (error: any) {
      console.error('Erreur lors de l\'utilisation du preset:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const createPreset = async () => {
    if (!presetName || !editorPresence.name) {
      toast.error('Nom du preset et nom d\'activité requis');
      return;
    }

    try {
      const presence: RichPresence = {
        activity_type: editorPresence.activity_type || 'Playing',
        name: editorPresence.name,
        details: editorPresence.details,
        state: editorPresence.state,
        timestamps: editorPresence.timestamps,
        assets: editorPresence.assets,
        party: editorPresence.party,
        secrets: editorPresence.secrets,
        buttons: editorPresence.buttons || [],
        platform: editorPresence.platform || 'Discord',
        flags: editorPresence.flags || 0,
        metadata: editorPresence.metadata || {},
      };

      await invoke('create_rich_presence_preset', {
        name: presetName,
        description: presetDescription || null,
        presence,
        autoVariables: presetAutoVariables,
      });

      toast.success('📝 Preset créé', {
        style: { background: '#10B981', color: 'white' },
      });

      setShowCreatePreset(false);
      setPresetName('');
      setPresetDescription('');
      // setPresetTags([]); // Future: reset preset tags
      await loadPresets();
    } catch (error: any) {
      console.error('Erreur lors de la création du preset:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const deletePreset = async (presetId: string) => {
    try {
      await invoke('delete_rich_presence_preset', { presetId });
      toast.success('🗑️ Preset supprimé');
      await loadPresets();
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const stopPresence = async () => {
    try {
      await invoke('stop_rich_presence');
      toast.success('⏹️ Rich Presence arrêtée');
      await loadCurrentPresence();
    } catch (error: any) {
      console.error('Erreur lors de l\'arrêt:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const updateConfig = async (newConfig: RichPresenceConfig) => {
    try {
      await invoke('update_rich_presence_config', { config: newConfig });
      setConfig(newConfig);
      toast.success('✅ Configuration mise à jour', {
        style: { background: '#10B981', color: 'white' },
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('❌ Erreur lors de la mise à jour');
    }
  };

  const insertVariableIntoField = (field: 'name' | 'details' | 'state', variable: string) => {
    const variableText = `{${variable}}`;
    setEditorPresence(prev => ({
      ...prev,
      [field]: (prev[field] || '') + variableText
    }));
  };

  const createPlatformPresence = async (platform: PlatformType) => {
    try {
      const presence = await invoke<RichPresence>('create_platform_presence', { platform });
      setEditorPresence(presence);
      setActiveTab('editor');
      toast.success(`🎯 Template ${typeof platform === 'string' ? platform : 'Custom'} chargé`);
    } catch (error: any) {
      console.error('Erreur lors de la création du template:', error);
      toast.error(`❌ ${error}`);
    }
  };

  const filteredPresets = presets.filter(preset => {
    const matchesSearch = preset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (preset.description?.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesTag = !selectedTag || preset.tags.includes(selectedTag);
    return matchesSearch && matchesTag;
  });

  const allTags = Array.from(new Set(presets.flatMap(preset => preset.tags))).sort();

  const tabs = [
    { id: 'current', label: 'Présence Actuelle', icon: '🎯' },
    { id: 'editor', label: 'Éditeur', icon: '✏️' },
    { id: 'presets', label: 'Presets', icon: '📋' },
    { id: 'history', label: 'Historique', icon: '📊' },
    { id: 'config', label: 'Configuration', icon: '⚙️' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              🎮 Rich Presence
            </h1>
            <p className="text-gray-400 mt-2">
              Gestionnaire de Rich Presence avec intégration Dynamic Variables
              {!config.enabled && (
                <span className="text-red-400 ml-2 animate-pulse">● DÉSACTIVÉ</span>
              )}
              {config.enabled && (
                <span className="text-green-400 ml-2 animate-pulse">● ACTIF</span>
              )}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {currentPresence && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={stopPresence}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                ⏹️ Arrêter
              </motion.button>
            )}
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowCreatePreset(true)}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-medium transition-all duration-300 shadow-lg shadow-purple-500/25"
            >
              ➕ Créer Preset
            </motion.button>
          </div>
        </motion.div>

        {/* Warning si désactivé */}
        <AnimatePresence>
          {!config.enabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 border border-yellow-700/50 rounded-xl p-4 backdrop-blur-sm"
            >
              <div className="flex items-center">
                <div className="text-yellow-400 text-2xl mr-3 animate-bounce">⚠️</div>
                <div>
                  <h3 className="text-yellow-400 font-medium text-lg">Rich Presence désactivé</h3>
                  <p className="text-yellow-300 text-sm mt-1">
                    Activez le système pour utiliser les fonctionnalités de Rich Presence
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Stats rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-4"
        >
          <div className="bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-sm rounded-xl p-4 border border-purple-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{presets.length}</div>
                <div className="text-sm text-gray-300">Presets sauvés</div>
                <div className="text-xs text-gray-400 mt-1">Configurations</div>
              </div>
              <div className="text-3xl opacity-80">📋</div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-sm rounded-xl p-4 border border-green-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{currentPresence ? '1' : '0'}</div>
                <div className="text-sm text-gray-300">Présence active</div>
                <div className="text-xs text-gray-400 mt-1">En cours</div>
              </div>
              <div className="text-3xl opacity-80">🎯</div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-blue-500/20 to-cyan-600/20 backdrop-blur-sm rounded-xl p-4 border border-blue-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{history.length}</div>
                <div className="text-sm text-gray-300">Historique</div>
                <div className="text-xs text-gray-400 mt-1">Sessions</div>
              </div>
              <div className="text-3xl opacity-80">📊</div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-indigo-500/20 to-purple-600/20 backdrop-blur-sm rounded-xl p-4 border border-indigo-500/30">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-white">{config.platforms_enabled.length}</div>
                <div className="text-sm text-gray-300">Plateformes</div>
                <div className="text-xs text-gray-400 mt-1">Activées</div>
              </div>
              <div className="text-3xl opacity-80">🔌</div>
            </div>
          </div>
        </motion.div>

        {/* Tabs Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="border-b border-gray-700/50"
        >
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                whileHover={{ y: -2 }}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-400 bg-purple-500/10 rounded-t-lg'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                }`}
              >
                <span className="mr-2 text-lg">{tab.icon}</span>
                {tab.label}
              </motion.button>
            ))}
          </nav>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Current Presence Tab */}
            {activeTab === 'current' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">🎯 Présence Actuelle</h3>
                  
                  {currentPresence ? (
                    <PresenceCard presence={currentPresence} isActive={true} />
                  ) : (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">🎮</div>
                      <div className="text-gray-400 text-lg">Aucune Rich Presence active</div>
                      <div className="text-gray-500 text-sm">Utilisez l'éditeur ou un preset pour commencer</div>
                      <div className="mt-4 space-x-3">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setActiveTab('editor')}
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                        >
                          ✏️ Éditeur
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setActiveTab('presets')}
                          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                        >
                          📋 Presets
                        </motion.button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Templates rapides par plateforme */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h4 className="text-lg font-semibold text-white mb-4">🚀 Templates Rapides</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {platforms.map((platform) => (
                      <motion.button
                        key={typeof platform === 'string' ? platform : 'custom'}
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => createPlatformPresence(platform)}
                        className="p-3 bg-gradient-to-br from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg hover:from-purple-600/30 hover:to-pink-600/30 transition-all duration-300"
                      >
                        <div className="text-2xl mb-1">
                          {platform === 'Discord' && '💬'}
                          {platform === 'Spotify' && '🎵'}
                          {platform === 'Steam' && '🎮'}
                          {platform === 'Xbox' && '🎮'}
                          {platform === 'PlayStation' && '🎮'}
                          {platform === 'YouTube' && '🎥'}
                          {platform === 'Twitch' && '📺'}
                          {platform === 'Netflix' && '📺'}
                          {platform === 'Crunchyroll' && '📺'}
                          {platform === 'VisualStudioCode' && '💻'}
                          {platform === 'Chrome' && '🌐'}
                        </div>
                        <div className="text-xs text-white font-medium">
                          {typeof platform === 'string' ? platform : 'Custom'}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Editor Tab */}
            {activeTab === 'editor' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Form */}
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                    <h3 className="text-xl font-semibold text-white mb-4">✏️ Éditeur de Rich Presence</h3>
                    
                    <div className="space-y-4">
                      {/* Activity Type & Platform */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Type d'activité *
                          </label>
                          <select
                            value={editorPresence.activity_type}
                            onChange={(e) => setEditorPresence(prev => ({ ...prev, activity_type: e.target.value as ActivityType }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
                          >
                            {activityTypes.map(type => (
                              <option key={type} value={type}>{type}</option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Plateforme *
                          </label>
                          <select
                            value={typeof editorPresence.platform === 'string' ? editorPresence.platform : 'Discord'}
                            onChange={(e) => setEditorPresence(prev => ({ ...prev, platform: e.target.value as PlatformType }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
                          >
                            {platforms.map(platform => (
                              <option key={typeof platform === 'string' ? platform : 'custom'} value={typeof platform === 'string' ? platform : 'Discord'}>
                                {typeof platform === 'string' ? platform : 'Custom'}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Name */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <label className="block text-sm font-medium text-gray-300">
                            Nom d'activité *
                          </label>
                          <div className="flex space-x-1">
                            {availableVariables.slice(0, 3).map(variable => (
                              <motion.button
                                key={variable}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => insertVariableIntoField('name', variable)}
                                className="text-xs bg-purple-600/20 text-purple-400 px-2 py-1 rounded hover:bg-purple-600/30 transition-colors"
                                title={`Insérer {${variable}}`}
                              >
                                {variable}
                              </motion.button>
                            ))}
                          </div>
                        </div>
                        <input
                          type="text"
                          placeholder="Ex: VoidBot Gaming ou {spotify_track}"
                          value={editorPresence.name || ''}
                          onChange={(e) => setEditorPresence(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                        />
                      </div>

                      {/* Details */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <label className="block text-sm font-medium text-gray-300">
                            Détails
                          </label>
                          <div className="flex space-x-1">
                            {availableVariables.slice(3, 6).map(variable => (
                              <motion.button
                                key={variable}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => insertVariableIntoField('details', variable)}
                                className="text-xs bg-purple-600/20 text-purple-400 px-2 py-1 rounded hover:bg-purple-600/30 transition-colors"
                                title={`Insérer {${variable}}`}
                              >
                                {variable}
                              </motion.button>
                            ))}
                          </div>
                        </div>
                        <input
                          type="text"
                          placeholder="Ex: Playing a game ou Listening to {spotify_artist}"
                          value={editorPresence.details || ''}
                          onChange={(e) => setEditorPresence(prev => ({ ...prev, details: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                        />
                      </div>

                      {/* State */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <label className="block text-sm font-medium text-gray-300">
                            État
                          </label>
                          <div className="flex space-x-1">
                            {availableVariables.slice(6, 9).map(variable => (
                              <motion.button
                                key={variable}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => insertVariableIntoField('state', variable)}
                                className="text-xs bg-purple-600/20 text-purple-400 px-2 py-1 rounded hover:bg-purple-600/30 transition-colors"
                                title={`Insérer {${variable}}`}
                              >
                                {variable}
                              </motion.button>
                            ))}
                          </div>
                        </div>
                        <input
                          type="text"
                          placeholder="Ex: Online ou At {local_time}"
                          value={editorPresence.state || ''}
                          onChange={(e) => setEditorPresence(prev => ({ ...prev, state: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                        />
                      </div>

                      {/* Buttons */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Boutons (max 2)
                        </label>
                        {(editorPresence.buttons || []).map((button, idx) => (
                          <div key={idx} className="flex space-x-2 mb-2">
                            <input
                              type="text"
                              placeholder="Label du bouton"
                              value={button.label}
                              onChange={(e) => {
                                const newButtons = [...(editorPresence.buttons || [])];
                                newButtons[idx] = { ...button, label: e.target.value };
                                setEditorPresence(prev => ({ ...prev, buttons: newButtons }));
                              }}
                              className="flex-1 px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                            />
                            <input
                              type="url"
                              placeholder="URL"
                              value={button.url}
                              onChange={(e) => {
                                const newButtons = [...(editorPresence.buttons || [])];
                                newButtons[idx] = { ...button, url: e.target.value };
                                setEditorPresence(prev => ({ ...prev, buttons: newButtons }));
                              }}
                              className="flex-1 px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                            />
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => {
                                const newButtons = (editorPresence.buttons || []).filter((_, i) => i !== idx);
                                setEditorPresence(prev => ({ ...prev, buttons: newButtons }));
                              }}
                              className="p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                            >
                              🗑️
                            </motion.button>
                          </div>
                        ))}
                        
                        {(!editorPresence.buttons || editorPresence.buttons.length < 2) && (
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => {
                              const newButtons = [...(editorPresence.buttons || []), { label: '', url: '' }];
                              setEditorPresence(prev => ({ ...prev, buttons: newButtons }));
                            }}
                            className="w-full p-2 border-2 border-dashed border-gray-600 rounded-lg text-gray-400 hover:border-purple-500 hover:text-purple-400 transition-colors"
                          >
                            ➕ Ajouter un bouton
                          </motion.button>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-600/30">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setEditorPresence({
                          activity_type: 'Playing',
                          name: '',
                          platform: 'Discord',
                          buttons: [],
                          flags: 0,
                          metadata: {},
                        })}
                        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        🗑️ Effacer
                      </motion.button>
                      
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => {
                          if (editorPresence.name) {
                            const presence: RichPresence = {
                              activity_type: editorPresence.activity_type || 'Playing',
                              name: editorPresence.name,
                              details: editorPresence.details,
                              state: editorPresence.state,
                              timestamps: editorPresence.timestamps,
                              assets: editorPresence.assets,
                              party: editorPresence.party,
                              secrets: editorPresence.secrets,
                              buttons: editorPresence.buttons || [],
                              platform: editorPresence.platform || 'Discord',
                              flags: editorPresence.flags || 0,
                              metadata: editorPresence.metadata || {},
                            };
                            setPresence(presence);
                          } else {
                            toast.error('Nom d\'activité requis');
                          }
                        }}
                        disabled={!editorPresence.name}
                        className="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        ✨ Appliquer la Présence
                      </motion.button>
                    </div>
                  </div>
                </div>

                {/* Right Column - Preview & Variables */}
                <div className="space-y-6">
                  {/* Preview */}
                  <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                    <h4 className="text-lg font-semibold text-white mb-4">👁️ Aperçu</h4>
                    
                    {editorPresence.name ? (
                      <PresenceCard 
                        presence={{
                          activity_type: editorPresence.activity_type || 'Playing',
                          name: editorPresence.name,
                          details: editorPresence.details,
                          state: editorPresence.state,
                          timestamps: editorPresence.timestamps,
                          assets: editorPresence.assets,
                          party: editorPresence.party,
                          secrets: editorPresence.secrets,
                          buttons: editorPresence.buttons || [],
                          platform: editorPresence.platform || 'Discord',
                          flags: editorPresence.flags || 0,
                          metadata: editorPresence.metadata || {},
                        }}
                      />
                    ) : (
                      <div className="text-center py-8">
                        <div className="text-4xl mb-2">👁️</div>
                        <div className="text-gray-400">L'aperçu apparaîtra ici</div>
                      </div>
                    )}
                  </div>

                  {/* Variables disponibles */}
                  <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                    <h4 className="text-lg font-semibold text-white mb-4">✨ Variables Disponibles</h4>
                    
                    <div className="grid grid-cols-2 gap-2">
                      {availableVariables.map(variable => (
                        <motion.button
                          key={variable}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            navigator.clipboard.writeText(`{${variable}}`);
                            toast.success(`📋 {${variable}} copié !`);
                          }}
                          className="text-left p-2 bg-purple-600/20 text-purple-300 rounded hover:bg-purple-600/30 transition-colors text-sm font-mono"
                          title={`Cliquer pour copier {${variable}}`}
                        >
                          {`{${variable}}`}
                        </motion.button>
                      ))}
                    </div>
                    
                    <div className="text-xs text-gray-400 mt-3">
                      💡 Cliquez sur une variable pour la copier, ou utilisez les boutons à côté des champs
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Presets Tab */}
            {activeTab === 'presets' && (
              <div className="space-y-6">
                {/* Search and filters */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="Rechercher des presets..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    
                    <select
                      value={selectedTag}
                      onChange={(e) => setSelectedTag(e.target.value)}
                      className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="">Tous les tags</option>
                      {allTags.map(tag => (
                        <option key={tag} value={tag}>#{tag}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Presets grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredPresets.length === 0 ? (
                    <div className="col-span-full text-center py-12">
                      <div className="text-6xl mb-4">📋</div>
                      <div className="text-gray-400 text-lg">
                        {searchQuery || selectedTag ? 'Aucun preset trouvé' : 'Aucun preset créé'}
                      </div>
                      <div className="text-gray-500 text-sm">
                        {searchQuery || selectedTag ? 'Essayez de modifier vos filtres' : 'Créez votre premier preset avec l\'éditeur'}
                      </div>
                    </div>
                  ) : (
                    filteredPresets.map(preset => (
                      <PresetCard
                        key={preset.id}
                        preset={preset}
                        onUse={() => usePreset(preset.id)}
                        onEdit={() => {
                          setEditorPresence(preset.presence);
                          setActiveTab('editor');
                          toast.success('📝 Preset chargé dans l\'éditeur');
                        }}
                        onDelete={() => {
                          if (confirm(`Supprimer le preset "${preset.name}" ?`)) {
                            deletePreset(preset.id);
                          }
                        }}
                      />
                    ))
                  )}
                </div>
              </div>
            )}

            {/* History Tab */}
            {activeTab === 'history' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">📊 Historique des Présences</h3>
                  
                  {history.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">📊</div>
                      <div className="text-gray-400 text-lg">Aucun historique</div>
                      <div className="text-gray-500 text-sm">L'historique des présences apparaîtra ici</div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {history.map((entry, idx) => (
                        <motion.div
                          key={idx}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: idx * 0.05 }}
                          className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <PresenceCard presence={entry.presence} />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm text-gray-300">
                                Début: {new Date(entry.started_at).toLocaleString('fr-FR')}
                              </div>
                              {entry.ended_at && (
                                <div className="text-sm text-gray-300">
                                  Fin: {new Date(entry.ended_at).toLocaleString('fr-FR')}
                                </div>
                              )}
                              {entry.duration_seconds && (
                                <div className="text-sm text-gray-400">
                                  Durée: {Math.floor(entry.duration_seconds / 60)}min {entry.duration_seconds % 60}s
                                </div>
                              )}
                              <div className="text-xs text-gray-500 mt-1">
                                Source: {typeof entry.source === 'string' ? entry.source : Object.keys(entry.source)[0]}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Config Tab */}
            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-6">⚙️ Configuration Rich Presence</h3>
                  
                  <div className="space-y-8">
                    {/* Général */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">📊 Paramètres généraux</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Intervalle de mise à jour (secondes)
                          </label>
                          <input
                            type="number"
                            min="5"
                            max="300"
                            value={config.auto_update_interval_seconds}
                            onChange={(e) => setConfig(prev => ({ ...prev, auto_update_interval_seconds: parseInt(e.target.value) || 15 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Maximum de presets
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="100"
                            value={config.max_presets}
                            onChange={(e) => setConfig(prev => ({ ...prev, max_presets: parseInt(e.target.value) || 20 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Maximum d'entrées d'historique
                          </label>
                          <input
                            type="number"
                            min="10"
                            max="1000"
                            value={config.max_history_entries}
                            onChange={(e) => setConfig(prev => ({ ...prev, max_history_entries: parseInt(e.target.value) || 100 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Options système */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">🎯 Options du système</h4>
                      <div className="space-y-4">
                        <motion.label 
                          className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                          whileHover={{ scale: 1.02 }}
                        >
                          <input
                            type="checkbox"
                            checked={config.enabled}
                            onChange={(e) => setConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                            className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 mt-1"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">🎮</span>
                              <span className="text-white font-medium">Activer Rich Presence</span>
                            </div>
                            <div className="text-gray-400 text-sm mt-1">
                              Permettre l'utilisation du système Rich Presence
                            </div>
                          </div>
                        </motion.label>

                        <motion.label 
                          className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                          whileHover={{ scale: 1.02 }}
                        >
                          <input
                            type="checkbox"
                            checked={config.use_dynamic_variables}
                            onChange={(e) => setConfig(prev => ({ ...prev, use_dynamic_variables: e.target.checked }))}
                            className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 mt-1"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">✨</span>
                              <span className="text-white font-medium">Utiliser les variables dynamiques</span>
                            </div>
                            <div className="text-gray-400 text-sm mt-1">
                              Résoudre automatiquement les variables {`{variable}`} dans les présences
                            </div>
                          </div>
                        </motion.label>

                        <motion.label 
                          className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                          whileHover={{ scale: 1.02 }}
                        >
                          <input
                            type="checkbox"
                            checked={config.fallback_on_error}
                            onChange={(e) => setConfig(prev => ({ ...prev, fallback_on_error: e.target.checked }))}
                            className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 mt-1"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">🛡️</span>
                              <span className="text-white font-medium">Fallback en cas d'erreur</span>
                            </div>
                            <div className="text-gray-400 text-sm mt-1">
                              Utiliser une présence de secours si une erreur survient
                            </div>
                          </div>
                        </motion.label>
                      </div>
                    </div>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateConfig(config)}
                      className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 font-medium shadow-lg shadow-purple-500/25"
                    >
                      ✅ Sauvegarder la configuration
                    </motion.button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Create Preset Modal */}
      <AnimatePresence>
        {showCreatePreset && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={() => setShowCreatePreset(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-600 max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-semibold text-white mb-4">📝 Créer un Preset</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Nom du preset *
                  </label>
                  <input
                    type="text"
                    placeholder="Mon preset gaming"
                    value={presetName}
                    onChange={(e) => setPresetName(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description
                  </label>
                  <textarea
                    placeholder="Description de ce preset..."
                    value={presetDescription}
                    onChange={(e) => setPresetDescription(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 h-20 resize-none"
                  />
                </div>

                <motion.label 
                  className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                  whileHover={{ scale: 1.02 }}
                >
                  <input
                    type="checkbox"
                    checked={presetAutoVariables}
                    onChange={(e) => setPresetAutoVariables(e.target.checked)}
                    className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">✨</span>
                      <span className="text-white font-medium">Variables automatiques</span>
                    </div>
                    <div className="text-gray-400 text-sm mt-1">
                      Résoudre les variables lors de l'utilisation du preset
                    </div>
                  </div>
                </motion.label>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowCreatePreset(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Annuler
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={createPreset}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  ✅ Créer
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}