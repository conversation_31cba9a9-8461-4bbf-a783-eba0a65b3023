import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Smartphone, 
  Bell, 
  Shield, 
  Palette, 
  Info,
  Download,
  RefreshCw
} from 'lucide-react';
import { useUpdateStore } from '../stores/updateStore';
import { UpdateManager } from './UpdateManager';

export const SettingsPage: React.FC = () => {
  const { 
    currentVersion, 
    status, 
    checkForUpdates, 
    isUpdateRequired 
  } = useUpdateStore();
  
  const [showUpdateManager, setShowUpdateManager] = useState(false);
  const [activeSection, setActiveSection] = useState('general');

  const sections = [
    { id: 'general', label: 'Général', icon: Settings },
    { id: 'updates', label: 'Mises à jour', icon: Smartphone },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Sécurité', icon: Shield },
    { id: 'appearance', label: 'Apparence', icon: Palette },
    { id: 'about', label: 'À propos', icon: Info },
  ];

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Paramètres généraux</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="font-medium text-white mb-2">Démarrage automatique</h4>
            <p className="text-gray-400 text-sm mb-3">Lancer VoidBot au démarrage de Windows</p>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
            </label>
          </div>
          
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="font-medium text-white mb-2">Minimiser dans la barre</h4>
            <p className="text-gray-400 text-sm mb-3">Réduire dans la zone de notification</p>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" defaultChecked className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUpdateSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Système de mise à jour</h3>
        
        {/* Current Version */}
        <div className="bg-gray-800/50 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Version actuelle</h4>
              <p className="text-gray-400 text-sm">VoidBot v{currentVersion}</p>
            </div>
            <div className="flex items-center gap-2">
              {isUpdateRequired() && (
                <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                  Mise à jour disponible
                </span>
              )}
              <button
                onClick={checkForUpdates}
                disabled={status.checking}
                className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 text-gray-300 ${status.checking ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
        </div>

        {/* Update Status */}
        {(status.available || status.checking || status.error) && (
          <div className="bg-gray-800/50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-white mb-1">État des mises à jour</h4>
                <p className="text-gray-400 text-sm">
                  {status.checking && 'Vérification en cours...'}
                  {status.available && status.update_info && `Mise à jour v${status.update_info.version} disponible`}
                  {status.error && status.error}
                  {!status.checking && !status.available && !status.error && 'Aucune mise à jour disponible'}
                </p>
              </div>
              {status.available && (
                <button
                  onClick={() => setShowUpdateManager(true)}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                >
                  <Download className="w-4 h-4 inline mr-2" />
                  Installer
                </button>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setShowUpdateManager(true)}
            className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 text-left transition-colors"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-500/20 rounded-lg">
                <Smartphone className="w-5 h-5 text-indigo-400" />
              </div>
              <div>
                <h4 className="font-medium text-white">Gestionnaire de mises à jour</h4>
                <p className="text-gray-400 text-sm">Configuration et historique</p>
              </div>
            </div>
          </button>
          
          <button
            onClick={checkForUpdates}
            disabled={status.checking}
            className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 text-left transition-colors disabled:opacity-50"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <RefreshCw className={`w-5 h-5 text-green-400 ${status.checking ? 'animate-spin' : ''}`} />
              </div>
              <div>
                <h4 className="font-medium text-white">Vérifier maintenant</h4>
                <p className="text-gray-400 text-sm">Rechercher les mises à jour</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderAboutSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">À propos de VoidBot</h3>
        
        <div className="bg-gray-800/50 rounded-lg p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">V</span>
            </div>
            <div>
              <h4 className="text-2xl font-bold text-white">VoidBot</h4>
              <p className="text-gray-400">Ultimate Discord Toolkit</p>
              <p className="text-gray-500 text-sm">Version {currentVersion}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h5 className="font-medium text-white mb-2">Technologies</h5>
              <ul className="text-gray-400 space-y-1">
                <li>• Tauri v2.6.2</li>
                <li>• React + TypeScript</li>
                <li>• Rust Backend</li>
                <li>• Tailwind CSS</li>
              </ul>
            </div>
            
            <div>
              <h5 className="font-medium text-white mb-2">Fonctionnalités</h5>
              <ul className="text-gray-400 space-y-1">
                <li>• +100 commandes Discord</li>
                <li>• Snipers ultra-rapides</li>
                <li>• Interface moderne</li>
                <li>• Sécurité AES-256</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 pt-4 border-t border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-gray-400 text-sm">© 2024 VoidBot Team</span>
              <div className="flex gap-2">
                <a
                  href="https://github.com/VoidBot/VoidBot"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded text-sm transition-colors"
                >
                  GitHub
                </a>
                <a
                  href="https://discord.gg/voidbot"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm transition-colors"
                >
                  Discord
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSection = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'updates':
        return renderUpdateSettings();
      case 'about':
        return renderAboutSettings();
      case 'notifications':
      case 'security':
      case 'appearance':
        return (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              <Settings className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Section en cours de développement</h3>
            <p className="text-gray-400">Cette section sera disponible dans une prochaine mise à jour.</p>
          </div>
        );
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Paramètres</h1>
          <p className="text-gray-400">Configuration et préférences de VoidBot</p>
        </div>

        <div className="flex gap-8">
          {/* Sidebar */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-gray-800/50 rounded-lg p-4">
              <nav className="space-y-2">
                {sections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${
                        activeSection === section.id
                          ? 'bg-indigo-600 text-white'
                          : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{section.label}</span>
                      {section.id === 'updates' && isUpdateRequired() && (
                        <div className="w-2 h-2 bg-green-400 rounded-full ml-auto"></div>
                      )}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-gray-800/30 rounded-lg p-6"
            >
              {renderSection()}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Update Manager Modal */}
      <UpdateManager 
        isOpen={showUpdateManager} 
        onClose={() => setShowUpdateManager(false)} 
      />
    </div>
  );
};