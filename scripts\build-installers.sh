#!/bin/bash

# VoidBot - Script de Build Installeurs Multi-Plateforme
# Génère tous les installeurs one-click pour v1.0

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DESKTOP_DIR="$PROJECT_ROOT/voidbot-desktop"
BUILD_DIR="$PROJECT_ROOT/builds"
TAURI_DIR="$DESKTOP_DIR/src-tauri"

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Vérifications préliminaires
check_dependencies() {
    log_header "Vérification des dépendances"
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi
    log_success "Node.js $(node --version)"
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi
    log_success "npm $(npm --version)"
    
    # Vérifier Rust
    if ! command -v cargo &> /dev/null; then
        log_error "Rust/Cargo n'est pas installé"
        exit 1
    fi
    log_success "Rust $(rustc --version)"
    
    # Vérifier Tauri CLI
    if ! command -v npm list -g @tauri-apps/cli &> /dev/null; then
        log_warning "Tauri CLI n'est pas installé globalement"
        log_info "Installation de Tauri CLI..."
        npm install -g @tauri-apps/cli@latest
    fi
    log_success "Tauri CLI disponible"
    
    # Vérifier Python pour les assets (optionnel)
    if command -v python3 &> /dev/null; then
        log_success "Python3 disponible pour génération assets"
    else
        log_warning "Python3 non disponible - assets manuels requis"
    fi
}

# Génération des assets
generate_assets() {
    log_header "Génération des assets installeurs"
    
    cd "$TAURI_DIR"
    
    # Vérifier si Python et Pillow sont disponibles
    if command -v python3 &> /dev/null; then
        log_info "Tentative de génération automatique des assets..."
        
        # Installer Pillow si nécessaire
        if ! python3 -c "import PIL" &> /dev/null; then
            log_info "Installation de Pillow pour la génération d'images..."
            python3 -m pip install Pillow || {
                log_warning "Impossible d'installer Pillow - génération manuelle requise"
                return 0
            }
        fi
        
        # Exécuter le script de génération
        if python3 build-assets.py; then
            log_success "Assets générés automatiquement"
        else
            log_warning "Échec génération automatique - vérification manuelle requise"
        fi
    else
        log_warning "Python3 non disponible - assets existants utilisés"
    fi
    
    # Vérifier que les assets critiques existent
    local assets_ok=true
    
    if [ ! -f "assets/header-image.bmp" ]; then
        log_warning "assets/header-image.bmp manquant"
        assets_ok=false
    fi
    
    if [ ! -f "assets/sidebar-image.bmp" ]; then
        log_warning "assets/sidebar-image.bmp manquant"  
        assets_ok=false
    fi
    
    if [ "$assets_ok" = false ]; then
        log_info "Création d'assets placeholder..."
        mkdir -p assets
        
        # Créer des images BMP basiques si nécessaire
        # (Cette partie nécessiterait ImageMagick ou des assets pré-créés)
        touch assets/header-image.bmp assets/sidebar-image.bmp
        log_warning "Assets placeholder créés - remplacez par de vraies images BMP"
    fi
}

# Préparation de l'environnement
prepare_build() {
    log_header "Préparation de l'environnement de build"
    
    # Créer le répertoire de build
    mkdir -p "$BUILD_DIR"
    log_success "Répertoire de build créé: $BUILD_DIR"
    
    # Nettoyer les builds précédents
    rm -rf "$BUILD_DIR"/*
    log_success "Builds précédents nettoyés"
    
    # Aller dans le répertoire desktop
    cd "$DESKTOP_DIR"
    
    # Installer les dépendances Node.js
    log_info "Installation des dépendances npm..."
    npm ci || npm install
    log_success "Dépendances npm installées"
    
    # Build du frontend
    log_info "Build du frontend React..."
    npm run build
    log_success "Frontend builé avec succès"
    
    # Mise à jour des dépendances Rust
    cd "$TAURI_DIR"
    log_info "Mise à jour des dépendances Rust..."
    cargo update
    log_success "Dépendances Rust mises à jour"
}

# Build pour Windows
build_windows() {
    log_header "Build des installeurs Windows"
    
    cd "$DESKTOP_DIR"
    
    # Build MSI (Windows Installer classique)
    log_info "Build MSI x64..."
    npm run tauri build -- --target x86_64-pc-windows-msvc --bundles msi
    
    log_info "Build MSI x86..."  
    npm run tauri build -- --target i686-pc-windows-msvc --bundles msi
    
    # Build NSIS (Installer moderne)
    log_info "Build NSIS x64..."
    npm run tauri build -- --target x86_64-pc-windows-msvc --bundles nsis
    
    log_info "Build NSIS x86..."
    npm run tauri build -- --target i686-pc-windows-msvc --bundles nsis
    
    log_success "Installeurs Windows générés"
}

# Build pour macOS
build_macos() {
    log_header "Build de l'installeur macOS"
    
    cd "$DESKTOP_DIR"
    
    # Build DMG Universal (Intel + Apple Silicon)
    log_info "Build DMG Universal..."
    npm run tauri build -- --target universal-apple-darwin --bundles dmg
    
    log_success "Installeur macOS généré"
}

# Build pour Linux
build_linux() {
    log_header "Build des installeurs Linux"
    
    cd "$DESKTOP_DIR"
    
    # Build DEB x64
    log_info "Build DEB x64..."
    npm run tauri build -- --target x86_64-unknown-linux-gnu --bundles deb
    
    # Build DEB ARM64
    log_info "Build DEB ARM64..."
    npm run tauri build -- --target aarch64-unknown-linux-gnu --bundles deb
    
    # Build AppImage x64
    log_info "Build AppImage x64..."
    npm run tauri build -- --target x86_64-unknown-linux-gnu --bundles appimage
    
    # Build AppImage ARM64
    log_info "Build AppImage ARM64..."
    npm run tauri build -- --target aarch64-unknown-linux-gnu --bundles appimage
    
    log_success "Installeurs Linux générés"
}

# Copie des artefacts
copy_artifacts() {
    log_header "Copie des artefacts de build"
    
    local source_dir="$TAURI_DIR/target/release/bundle"
    
    # Créer la structure de répertoires
    mkdir -p "$BUILD_DIR"/{windows,macos,linux}
    
    # Copier les installeurs Windows
    if [ -d "$source_dir/msi" ]; then
        cp "$source_dir/msi"/*.msi "$BUILD_DIR/windows/" 2>/dev/null || true
        log_success "MSI copiés vers builds/windows/"
    fi
    
    if [ -d "$source_dir/nsis" ]; then
        cp "$source_dir/nsis"/*.exe "$BUILD_DIR/windows/" 2>/dev/null || true
        log_success "NSIS copiés vers builds/windows/"
    fi
    
    # Copier les installeurs macOS
    if [ -d "$source_dir/dmg" ]; then
        cp "$source_dir/dmg"/*.dmg "$BUILD_DIR/macos/" 2>/dev/null || true
        log_success "DMG copiés vers builds/macos/"
    fi
    
    # Copier les installeurs Linux
    if [ -d "$source_dir/deb" ]; then
        cp "$source_dir/deb"/*.deb "$BUILD_DIR/linux/" 2>/dev/null || true
        log_success "DEB copiés vers builds/linux/"
    fi
    
    if [ -d "$source_dir/appimage" ]; then
        cp "$source_dir/appimage"/*.AppImage "$BUILD_DIR/linux/" 2>/dev/null || true
        log_success "AppImage copiés vers builds/linux/"
    fi
}

# Génération des checksums
generate_checksums() {
    log_header "Génération des checksums SHA256"
    
    cd "$BUILD_DIR"
    
    # Générer checksums pour chaque plateforme
    for platform in windows macos linux; do
        if [ -d "$platform" ] && [ "$(ls -A "$platform")" ]; then
            cd "$platform"
            sha256sum * > SHA256SUMS 2>/dev/null || shasum -a 256 * > SHA256SUMS
            log_success "Checksums générés pour $platform"
            cd ..
        fi
    done
}

# Résumé des builds
build_summary() {
    log_header "Résumé des builds"
    
    echo ""
    echo -e "${CYAN}📦 Artefacts générés:${NC}"
    
    find "$BUILD_DIR" -type f \( -name "*.msi" -o -name "*.exe" -o -name "*.dmg" -o -name "*.deb" -o -name "*.AppImage" \) | while read file; do
        local size=$(du -h "$file" | cut -f1)
        local basename=$(basename "$file")
        echo -e "   ${GREEN}✓${NC} $basename ($size)"
    done
    
    echo ""
    echo -e "${CYAN}📁 Répertoires de build:${NC}"
    echo -e "   🪟 Windows: ${BUILD_DIR}/windows/"
    echo -e "   🍎 macOS:   ${BUILD_DIR}/macos/"
    echo -e "   🐧 Linux:   ${BUILD_DIR}/linux/"
    
    echo ""
    echo -e "${GREEN}🎉 Build des installeurs terminé avec succès!${NC}"
}

# Script principal
main() {
    log_header "VoidBot - Build Installeurs Multi-Plateforme v1.0"
    
    # Vérifications
    check_dependencies
    
    # Génération assets
    generate_assets
    
    # Préparation
    prepare_build
    
    # Détection de la plateforme et build approprié
    case "$(uname -s)" in
        CYGWIN*|MINGW*|MSYS*|Windows*)
            log_info "Plateforme détectée: Windows"
            build_windows
            ;;
        Darwin*)
            log_info "Plateforme détectée: macOS"
            build_macos
            # Optionnellement aussi Windows via cross-compilation
            ;;
        Linux*)
            log_info "Plateforme détectée: Linux"
            build_linux
            # Optionnellement Windows via cross-compilation avec wine
            ;;
        *)
            log_warning "Plateforme non reconnue - build Linux par défaut"
            build_linux
            ;;
    esac
    
    # Post-processing
    copy_artifacts
    generate_checksums
    build_summary
}

# Exécution du script principal
main "$@"