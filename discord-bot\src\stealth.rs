use serenity::{
    builder::{CreateInteractionResponse, CreateInteractionResponseMessage},
    model::{
        application::CommandInteraction,
        prelude::*,
    },
    prelude::*,
};
use voidbot_shared::StealthMode;

pub struct StealthManager {
    mode: StealthMode,
}

impl StealthManager {
    pub fn new() -> Self {
        Self {
            mode: StealthMode::Normal,
        }
    }

    pub fn set_mode(&mut self, mode: StealthMode) {
        self.mode = mode;
    }

    pub fn get_mode(&self) -> StealthMode {
        self.mode
    }

    pub async fn send_response(
        &self,
        ctx: &Context,
        command: &CommandInteraction,
        content: impl Into<String>,
        always_ephemeral: bool,
    ) -> Result<(), serenity::Error> {
        let content = content.into();
        let ephemeral = always_ephemeral || matches!(self.mode, StealthMode::Ghost);

        command
            .create_response(
                &ctx.http,
                CreateInteractionResponse::Message(
                    CreateInteractionResponseMessage::new()
                        .content(content)
                        .ephemeral(ephemeral),
                ),
            )
            .await
    }

    pub async fn send_followup(
        &self,
        ctx: &Context,
        command: &CommandInteraction,
        content: impl Into<String>,
        always_ephemeral: bool,
    ) -> Result<Message, serenity::Error> {
        let content = content.into();
        let ephemeral = always_ephemeral || matches!(self.mode, StealthMode::Ghost);

        command
            .create_followup(
                &ctx.http,
                serenity::builder::CreateInteractionResponseFollowup::new()
                    .content(content)
                    .ephemeral(ephemeral),
            )
            .await
    }

    pub fn should_log(&self) -> bool {
        !matches!(self.mode, StealthMode::Ghost)
    }

    pub fn is_ghost_mode(&self) -> bool {
        matches!(self.mode, StealthMode::Ghost)
    }
}