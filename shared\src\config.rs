use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Config {
    pub discord: DiscordConfig,
    pub database: DatabaseConfig,
    pub api: ApiConfig,
    pub features: FeaturesConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DiscordConfig {
    pub token: String,
    pub application_id: String,
    pub prefix: String,
    pub owner_ids: Vec<String>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApiConfig {
    pub host: String,
    pub port: u16,
    pub jwt_secret: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FeaturesConfig {
    pub animations_enabled: bool,
    pub max_animation_frames: usize,
    pub sniper_enabled: bool,
    pub backup_enabled: bool,
    pub scripts_enabled: bool,
    pub max_script_size: usize,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            discord: DiscordConfig {
                token: String::new(),
                application_id: String::new(),
                prefix: "/".to_string(),
                owner_ids: Vec::new(),
            },
            database: DatabaseConfig {
                url: if let Ok(appdata) = std::env::var("APPDATA") {
                    format!("sqlite://{}/VoidBot/secure/voidbot.db", appdata)
                } else {
                    "sqlite://voidbot.db".to_string()
                },
                max_connections: 5,
            },
            api: ApiConfig {
                host: "127.0.0.1".to_string(),
                port: 3000,
                jwt_secret: "change-this-secret".to_string(),
            },
            features: FeaturesConfig {
                animations_enabled: true,
                max_animation_frames: 10,
                sniper_enabled: true,
                backup_enabled: true,
                scripts_enabled: true,
                max_script_size: 1024 * 1024, // 1MB
            },
        }
    }
}