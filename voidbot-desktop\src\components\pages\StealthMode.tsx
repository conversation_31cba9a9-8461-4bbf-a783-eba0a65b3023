import { motion } from 'framer-motion';
import { Eye, MessageSquare, Ghost, Info } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';

export function StealthMode() {
  const { stealthMode, setStealthMode } = useAppStore();

  const modes = [
    {
      id: 'normal' as const,
      title: 'Normal Mode',
      description: 'Responses visible to everyone in the server',
      icon: '💬',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30',
    },
    {
      id: 'ghost' as const,
      title: 'Ghost Mode',
      description: 'Responses only visible to you (ephemeral)',
      icon: '👻',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/30',
    },
  ];

  const handleModeChange = async (mode: typeof stealthMode) => {
    try {
      await setStealthMode(mode);
    } catch (error) {
      console.error('Failed to change stealth mode:', error);
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Stealth Mode</h1>
        <p className="page-subtitle">
          Control how bot responses are displayed to other users
        </p>
      </div>

      {/* Current Mode Status */}
      <motion.div 
        className="card mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-3 mb-4">
          <Eye className="w-6 h-6 text-voidbot-primary" />
          <h3 className="text-xl font-semibold">Current Mode</h3>
        </div>
        
        <div className="flex items-center gap-4">
          <span className="text-4xl">
            {stealthMode === 'ghost' ? '👻' : '💬'}
          </span>
          <div>
            <div className="text-lg font-semibold text-voidbot-primary">
              {stealthMode === 'ghost' ? 'Ghost Mode' : 'Normal Mode'}
            </div>
            <div className="text-voidbot-secondary">
              {stealthMode === 'ghost' 
                ? 'All responses are private and only visible to you'
                : 'All responses are public and visible to everyone'
              }
            </div>
          </div>
        </div>
      </motion.div>

      {/* Mode Selection */}
      <motion.div 
        className="card mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <h3 className="text-xl font-semibold mb-6">Select Response Mode</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {modes.map((mode) => (
            <motion.button
              key={mode.id}
              className={`mode-card ${
                stealthMode === mode.id ? 'active' : 'inactive'
              }`}
              onClick={() => handleModeChange(mode.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="text-center">
                <div className="text-5xl mb-4">{mode.icon}</div>
                <h4 className="text-lg font-semibold mb-2">{mode.title}</h4>
                <p className="text-sm text-voidbot-secondary">
                  {mode.description}
                </p>
              </div>
            </motion.button>
          ))}
        </div>
      </motion.div>

      {/* Information */}
      <motion.div 
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex items-center gap-3 mb-4">
          <Info className="w-6 h-6 text-voidbot-accent" />
          <h3 className="text-xl font-semibold">How Stealth Mode Works</h3>
        </div>

        <div className="space-y-4">
          <div className="flex gap-4">
            <MessageSquare className="w-5 h-5 text-voidbot-primary mt-1 flex-shrink-0" />
            <div>
              <h4 className="font-semibold mb-1">Slash Commands Only</h4>
              <p className="text-sm text-voidbot-secondary">
                All commands use Discord's slash command system (/command)
              </p>
            </div>
          </div>

          <div className="flex gap-4">
            <Ghost className="w-5 h-5 text-purple-400 mt-1 flex-shrink-0" />
            <div>
              <h4 className="font-semibold mb-1">Ghost Mode Benefits</h4>
              <p className="text-sm text-voidbot-secondary">
                In Ghost Mode, responses use Discord's ephemeral message feature, making them completely private
              </p>
            </div>
          </div>

          <div className="flex gap-4">
            <Eye className="w-5 h-5 text-voidbot-primary mt-1 flex-shrink-0" />
            <div>
              <h4 className="font-semibold mb-1">Sensitive Commands</h4>
              <p className="text-sm text-voidbot-secondary">
                Some commands like /snipe are always private regardless of the current mode for security
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-voidbot-primary/10 rounded-lg border border-voidbot-primary/20">
          <h4 className="font-semibold text-voidbot-primary mb-2">💡 Pro Tip</h4>
          <p className="text-sm text-voidbot-secondary">
            You can switch between modes instantly without restarting the bot. The change takes effect immediately for all new commands.
          </p>
        </div>
      </motion.div>
    </div>
  );
}