# ⚡ Démarrage Rapide Windows - VoidBot v1.0

## 🎯 Build en 5 minutes

### **Prérequis installés ?**
Vérifiez rapidement votre environnement :
```cmd
scripts\check-windows-env.bat
```

### **Build automatique :**
```cmd
cd C:\chemin\vers\VoidBot
scripts\build-windows.bat
```

**C'est tout !** ✨

---

## 📦 Installation rapide des prérequis

### **1. Node.js** (2 minutes)
```
1. Aller sur https://nodejs.org
2. Télécharger LTS Windows x64
3. Exécuter le .msi et suivre l'assistant
4. Redémarrer l'invite de commandes
```

### **2. Rust** (3 minutes)
```
1. Aller sur https://rustup.rs
2. Télécharger rustup-init.exe
3. Exécuter et choisir "1) Continue with installation"
4. Redémarrer l'invite de commandes
```

### **3. Git** (2 minutes)
```
1. <PERSON><PERSON> sur https://git-scm.com
2. Télécharger Git pour Windows
3. Installer avec les options par défaut
```

**Total : ~7 minutes** ⏱️

---

## 🚀 Commandes essentielles

### **Vérification rapide :**
```cmd
node --version    # v18.x.x ou supérieur
npm --version     # 9.x.x ou supérieur  
cargo --version   # 1.70.x ou supérieur
git --version     # 2.x.x ou supérieur
```

### **Build standard :**
```cmd
scripts\build-windows.bat
```

### **Build PowerShell (plus d'options) :**
```cmd
# Build rapide (sans x86)
powershell .\scripts\build-windows.ps1 -SkipX86

# Build propre (nettoyage avant)
powershell .\scripts\build-windows.ps1 -Clean
```

### **En cas de problème :**
```cmd
# Nettoyer et recommencer
cd voidbot-desktop
rmdir /s node_modules
rmdir /s src-tauri\target
npm install
scripts\build-windows.bat
```

---

## 📁 Récupérer les installeurs

### **Emplacement :**
```
builds\windows\
├── VoidBot_1.0.0_x64_en-US.msi      # Installeur MSI
└── VoidBot_1.0.0_x64-setup.exe      # Installeur NSIS
```

### **Taille attendue :**
- Chaque installeur : **~50-80 MB**
- Si plus petit : Build incomplet
- Si beaucoup plus gros : Problème de compression

---

## ⏱️ Temps de build

### **Première fois :**
- **Téléchargement dépendances** : 5-10 min
- **Compilation** : 10-20 min
- **Total** : 15-30 min

### **Builds suivants :**
- **Compilation seule** : 5-15 min
- **Cache Rust utilisé** : Plus rapide

---

## 🐛 Erreurs fréquentes

### **"command not found"**
```
Solution : Redémarrer l'invite de commandes après installation
```

### **"Erreur npm install"**
```
Solution : 
cd voidbot-desktop
rmdir /s node_modules
npm cache clean --force
npm install
```

### **"Erreur Rust compilation"**
```
Solution :
rustup update
cargo clean
```

### **"Espace disque insuffisant"**
```
Solution : Libérer 10 GB minimum
```

---

## 🎉 Test des installeurs

### **1. Test MSI :**
```
1. Double-clic sur VoidBot_x64.msi
2. Suivre l'assistant d'installation
3. Lancer VoidBot depuis le Menu Démarrer
```

### **2. Test NSIS :**
```
1. Double-clic sur VoidBot-setup.exe
2. Interface d'installation personnalisée
3. Options : Raccourcis, associations
4. Lancer VoidBot
```

### **3. Validation :**
- [ ] Application se lance sans erreur
- [ ] Interface VoidBot s'affiche
- [ ] Login Discord fonctionne (avec vrai token)
- [ ] Navigation entre les pages OK
- [ ] Auto-updater détecte les versions

---

## 🔄 Workflow typique

### **Développement :**
```cmd
# 1. Modifier le code source
# 2. Tester en mode dev
cd voidbot-desktop && npm run tauri dev

# 3. Build pour distribution  
scripts\build-windows.bat

# 4. Tester les installeurs
# 5. Distribuer via GitHub Releases
```

### **Production :**
```cmd
# Build propre final
powershell .\scripts\build-windows.ps1 -Clean -SkipX86

# Génération checksums
certutil -hashfile builds\windows\VoidBot_1.0.0_x64-setup.exe SHA256

# Upload vers GitHub
gh release upload v1.0.0 builds\windows\*
```

---

## 📞 Aide rapide

### **Support immédiat :**
1. **GitHub Issues** : [Créer une issue](https://github.com/VoidBot/issues)
2. **Logs** : Consulter `build-windows.log`
3. **Documentation** : Lire `BUILD_WINDOWS_GUIDE.md`

### **Ressources :**
- **Tauri** : https://tauri.app/v1/guides/building/windows
- **Rust** : https://doc.rust-lang.org/book/
- **Node.js** : https://nodejs.org/docs/

---

**✨ En moins de 30 minutes, vous avez des installeurs Windows production-ready de VoidBot ! 🚀**