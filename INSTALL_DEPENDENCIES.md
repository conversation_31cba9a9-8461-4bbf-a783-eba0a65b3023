# 🔧 Installation des dépendances système VoidBot

## ⚡ **Action requise - Exécute ces commandes dans ton terminal WSL2 :**

```bash
# 1. Mettre à jour les packages
sudo apt update

# 2. Installer les bibliothèques tray-icon manquantes
sudo apt install -y libayatana-appindicator3-dev

# 3. (Optionnel) Installer aussi la version alternative
sudo apt install -y libappindicator3-dev

# 4. Vérifier que les libs sont installées
ldconfig -p | grep appindicator
```

## ✅ **Résultat attendu :**
```
libayatana-appindicator3.so.1 (libc6,x86-64) => /usr/lib/x86_64-linux-gnu/libayatana-appindicator3.so.1
libappindicator3.so.1 (libc6,x86-64) => /usr/lib/x86_64-linux-gnu/libappindicator3.so.1
```

## 🎯 **Après installation :**
1. **Arr<PERSON><PERSON>oid<PERSON>ot** : Ctrl+C dans le terminal où `npm run tauri dev` tourne
2. **Relancer VoidBot** : `npm run tauri dev` 
3. **L'app devrait maintenant démarrer sans crash** avec tray-icon fonctionnel

---

**Note** : Ces bibliothèques permettent à Tauri d'afficher l'icône VoidBot dans la barre système Linux/WSL2.