use voidbot_shared::{IpcMessage, VoidNotification};
use chrono::Utc;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{info, warn, error};
use serde_json::json;

/// Gestionnaire d'émission d'événements temps réel vers l'interface Tauri
/// 
/// Responsabilités :
/// - Envoyer les notifications Discord en temps réel
/// - Émettre les événements de giveaway et Nitro sniper
/// - Gérer les mises à jour de statut du bot
/// - Mode test compatible avec simulation d'événements
pub struct EventEmitter {
    /// Canal pour envoyer les messages IPC vers l'interface
    ipc_sender: Option<Arc<mpsc::UnboundedSender<IpcMessage>>>,
    /// Mode test activé
    test_mode: bool,
}

impl EventEmitter {
    /// Créer un nouveau gestionnaire d'événements
    pub fn new(ipc_sender: Option<Arc<mpsc::UnboundedSender<IpcMessage>>>, test_mode: bool) -> Self {
        Self {
            ipc_sender,
            test_mode,
        }
    }

    /// Émettre une notification en temps réel
    pub async fn emit_notification(&self, notification: VoidNotification) -> Result<(), String> {
        if self.test_mode {
            info!("🧪 [TEST MODE] Notification émise: {}", notification.title);
            return Ok(());
        }

        if let Some(sender) = &self.ipc_sender {
            let message = IpcMessage::NotificationEvent {
                notification,
                timestamp: Utc::now(),
            };

            if let Err(e) = sender.send(message) {
                error!("Erreur lors de l'envoi de notification IPC: {}", e);
                return Err(format!("Échec envoi notification: {}", e));
            }

            info!("📢 Notification envoyée vers l'interface");
        } else {
            warn!("Aucun canal IPC configuré pour les notifications");
        }

        Ok(())
    }

    /// Émettre un événement de giveaway
    pub async fn emit_giveaway_event(
        &self,
        event_type: &str,
        giveaway_info: serde_json::Value,
    ) -> Result<(), String> {
        if self.test_mode {
            info!("🧪 [TEST MODE] Giveaway event: {} - {:?}", event_type, giveaway_info);
            return Ok(());
        }

        if let Some(sender) = &self.ipc_sender {
            let message = IpcMessage::GiveawayEvent {
                event_type: event_type.to_string(),
                giveaway_info,
                timestamp: Utc::now(),
            };

            if let Err(e) = sender.send(message) {
                error!("Erreur lors de l'envoi d'événement giveaway IPC: {}", e);
                return Err(format!("Échec envoi giveaway event: {}", e));
            }

            info!("🎁 Événement giveaway envoyé vers l'interface: {}", event_type);
        }

        Ok(())
    }

    /// Émettre un événement Nitro Sniper
    pub async fn emit_nitro_event(
        &self,
        event_type: &str,
        code_info: serde_json::Value,
    ) -> Result<(), String> {
        if self.test_mode {
            info!("🧪 [TEST MODE] Nitro event: {} - {:?}", event_type, code_info);
            return Ok(());
        }

        if let Some(sender) = &self.ipc_sender {
            let message = IpcMessage::NitroEvent {
                event_type: event_type.to_string(),
                code_info,
                timestamp: Utc::now(),
            };

            if let Err(e) = sender.send(message) {
                error!("Erreur lors de l'envoi d'événement Nitro IPC: {}", e);
                return Err(format!("Échec envoi Nitro event: {}", e));
            }

            info!("💎 Événement Nitro envoyé vers l'interface: {}", event_type);
        }

        Ok(())
    }

    /// Émettre une mise à jour du statut du bot
    pub async fn emit_bot_status_update(
        &self,
        status: &str,
        user_info: Option<serde_json::Value>,
        error_message: Option<String>,
    ) -> Result<(), String> {
        if self.test_mode {
            info!("🧪 [TEST MODE] Bot status update: {} - {:?}", status, user_info);
            return Ok(());
        }

        if let Some(sender) = &self.ipc_sender {
            let message = IpcMessage::BotStatusUpdate {
                status: status.to_string(),
                user_info,
                error_message,
                timestamp: Utc::now(),
            };

            if let Err(e) = sender.send(message) {
                error!("Erreur lors de l'envoi de mise à jour statut IPC: {}", e);
                return Err(format!("Échec envoi status update: {}", e));
            }

            info!("🤖 Mise à jour statut bot envoyée: {}", status);
        }

        Ok(())
    }

    /// Simuler des événements de test pour le mode développement
    pub async fn simulate_test_events(&self) {
        if !self.test_mode {
            return;
        }

        info!("🧪 Démarrage de la simulation d'événements de test...");

        // Simuler une notification de ghostping après 5 secondes
        tokio::spawn(async {
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
            info!("🧪 [SIMULATION] Ghostping détecté de TestUser#0001");
        });

        // Simuler un giveaway détecté après 10 secondes
        tokio::spawn(async {
            tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
            info!("🧪 [SIMULATION] Giveaway détecté dans #general");
        });

        // Simuler un code Nitro détecté après 15 secondes
        tokio::spawn(async {
            tokio::time::sleep(tokio::time::Duration::from_secs(15)).await;
            info!("🧪 [SIMULATION] Code Nitro détecté (simulation)");
        });
    }
}

/// Créer un émetteur d'événements pour le mode test
pub fn create_test_event_emitter() -> EventEmitter {
    EventEmitter::new(None, true)
}

/// Créer un émetteur d'événements pour le mode production
pub fn create_production_event_emitter(
    ipc_sender: Arc<mpsc::UnboundedSender<IpcMessage>>
) -> EventEmitter {
    EventEmitter::new(Some(ipc_sender), false)
}
