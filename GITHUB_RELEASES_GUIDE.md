# 🚀 Guide GitHub Releases - VoidBot

## 📋 Vue d'ensemble

Ce guide explique comment utiliser les GitHub Actions pour automatiser la création de releases multi-plateforme de VoidBot avec génération automatique des installeurs.

## 🎯 Fonctionnalités

### **Release automatisée** avec :
- **Multi-plateforme** : Windows, Linux, macOS
- **Installeurs** : MSI, NSIS, DEB, AppImage, DMG
- **Assets** : Upload automatique vers GitHub Releases
- **Documentation** : Release notes générées automatiquement
- **Notifications** : Intégration Discord optionnelle

## 🔧 Configuration

### **Fichiers GitHub Actions :**
```
.github/workflows/
├── release.yml    # Release automatisée sur tags
└── build.yml      # Builds de développement
```

### **Workflows :**

#### **1. release.yml - Release Production**
- **Déclencheur** : Tags `v*.*.*` ou déclenchement manuel
- **Jobs** : Create release + Build Windows/Linux/macOS  
- **Output** : GitHub Release avec tous les installeurs

#### **2. build.yml - Builds Développement**
- **Déclencheur** : Push sur main/develop
- **Jobs** : Tests + Builds multi-plateforme
- **Output** : Artifacts GitHub pour validation

## 🎯 Release Workflow

### **Processus automatique :**

#### **1. Create Release Job**
```yaml
- Checkout code
- Extract version from tag
- Create GitHub Release
- Generate release notes
```

#### **2. Build Windows Job**
```yaml
- Setup Node.js + Rust
- Install dependencies
- Generate assets
- Build frontend React
- Build Tauri app (MSI + NSIS)
- Upload to release
```

#### **3. Build Linux Job**
```yaml
- Setup environment + dependencies
- Build Tauri app (DEB + AppImage)
- Upload to release
```

#### **4. Build macOS Job**
```yaml
- Setup environment
- Build Tauri app (DMG Universal)
- Upload to release
```

## 🚀 Créer une Release

### **Option 1 : Tag automatique**
```bash
# Créer et pusher un tag
git tag v1.0.0
git push origin v1.0.0

# GitHub Actions se déclenche automatiquement
```

### **Option 2 : Déclenchement manuel**
```yaml
# Dans GitHub Actions:
# 1. Aller sur l'onglet Actions
# 2. Sélectionner "Release VoidBot"  
# 3. Cliquer "Run workflow"
# 4. Entrer la version (e.g., v1.0.0)
# 5. Lancer le workflow
```

## 📦 Installeurs Générés

### **Nommage standardisé :**
```
Windows:
├── VoidBot_v1.0.0_x64_en-US.msi        # MSI Windows Installer
└── VoidBot_v1.0.0_x64-setup.exe       # NSIS Custom Installer

Linux:
├── VoidBot_v1.0.0_amd64.deb            # Debian/Ubuntu Package
└── VoidBot_v1.0.0_amd64.AppImage       # Portable Linux App

macOS:
└── VoidBot_v1.0.0_universal.dmg        # Universal macOS Disk Image
```

### **Tailles typiques :**
- **MSI/NSIS** : 50-80 MB
- **DEB** : 60-90 MB  
- **AppImage** : 80-120 MB
- **DMG** : 70-100 MB

## 📋 Release Notes Automatiques

### **Template généré :**
```markdown
## 🎉 VoidBot v1.0.0

### 📦 Installeurs disponibles
- Windows x64 : VoidBot_v1.0.0_x64-setup.exe (NSIS)
- Windows x64 : VoidBot_v1.0.0_x64_en-US.msi (MSI)
- Linux x64 : VoidBot_v1.0.0_amd64.deb (DEB)
- Linux : VoidBot_v1.0.0_amd64.AppImage (AppImage)
- macOS : VoidBot_v1.0.0_universal.dmg (DMG)

### ✨ Nouveautés
- 🤖 Selfbot Discord avec +100 commandes
- 👻 Mode furtif Normal/Fantôme
- 🚀 Snipers ultra-performants
- 🎨 Interface moderne Tauri + React
- 🔒 Sécurité maximale (100% local)
- 🔄 Auto-updater intégré

### 🔧 Installation
1. Télécharger l'installeur pour votre OS
2. Exécuter et suivre l'assistant
3. Lancer VoidBot et se connecter via Discord
```

## ⏱️ Temps d'Exécution

### **Estimations typiques :**
- **Windows build** : 15-25 minutes
- **Linux build** : 10-20 minutes
- **macOS build** : 20-30 minutes
- **Total release** : 30-45 minutes

### **Builds parallèles :**
Les 3 plateformes buildent en parallèle pour optimiser le temps total.

## 🔒 Sécurité et Tokens

### **Permissions requises :**
```yaml
permissions:
  contents: write    # Créer releases
  actions: read      # Lire workflows
```

### **Secrets GitHub :**
- **GITHUB_TOKEN** : Token automatique (fourni par GitHub)
- **Optionnel** : Certificats de signature de code

## 📊 Monitoring et Logs

### **Suivi en temps réel :**
- Dashboard GitHub Actions
- Logs détaillés par job
- Artifacts de build téléchargeables
- Statut en temps réel

### **Notifications :**
- Email GitHub automatique
- Webhook Discord configurable
- Intégrations Slack/Teams possibles

## 🐛 Dépannage

### **Erreurs courantes :**

#### **"Build failed - Rust compilation"**
```yaml
Solution:
- Vérifier Cargo.toml syntax
- Rust dependencies compatibles
- Targets platform installés
```

#### **"Upload asset failed"**
```yaml
Solution:
- Vérifier permissions GITHUB_TOKEN
- Path des installeurs correct
- Noms de fichiers uniques
```

#### **"Node.js build failed"**
```yaml
Solution:
- package.json valid
- Dependencies compatibles Node 18+
- Scripts build définis
```

### **Debug workflow :**
```yaml
# Activer debug mode
- name: Debug
  run: |
    echo "Debug mode enabled"
    ls -la
    env
```

## 🔄 Versionning

### **Semantic Versioning :**
```
v1.0.0    # Release majeure
v1.0.1    # Patch/bugfix
v1.1.0    # Feature mineure
v2.0.0    # Breaking changes
```

### **Tag format :**
- **Production** : `v1.0.0`
- **Pre-release** : `v1.0.0-beta.1`
- **Release candidate** : `v1.0.0-rc.1`

## 📈 Auto-Updater Integration

### **Configuration Tauri :**
```json
// tauri.conf.json
"updater": {
  "active": true,
  "endpoints": [
    "https://api.github.com/repos/OWNER/REPO/releases/latest"
  ],
  "dialog": true,
  "pubkey": "PUBLIC_KEY_HERE"
}
```

### **Fonctionnement :**
1. App vérifie GitHub Releases API
2. Compare version locale vs distant
3. Télécharge nouvel installeur si disponible
4. Propose mise à jour utilisateur
5. Installation automatique

## 🌐 Distribution

### **URLs automatiques :**
```
Latest Release:
https://github.com/OWNER/REPO/releases/latest

Specific Version:
https://github.com/OWNER/REPO/releases/tag/v1.0.0

Direct Download:
https://github.com/OWNER/REPO/releases/download/v1.0.0/VoidBot_v1.0.0_x64-setup.exe
```

### **Intégration site web :**
```javascript
// Détection OS et redirection automatique
const getDownloadUrl = (os) => {
  const base = 'https://github.com/OWNER/REPO/releases/latest/download/';
  switch(os) {
    case 'Windows': return base + 'VoidBot_latest_x64-setup.exe';
    case 'Linux': return base + 'VoidBot_latest_amd64.deb';
    case 'macOS': return base + 'VoidBot_latest_universal.dmg';
  }
};
```

## ✅ Checklist Release

### **Avant release :**
- [ ] Code testé et validé
- [ ] Version mise à jour dans Cargo.toml
- [ ] CHANGELOG.md mis à jour
- [ ] Assets générés et testés
- [ ] Documentation à jour

### **Pendant release :**
- [ ] Tag créé et pushé
- [ ] Workflow GitHub Actions lancé
- [ ] Surveillance des builds en cours
- [ ] Validation des artifacts générés

### **Après release :**
- [ ] Installeurs testés sur chaque OS
- [ ] Site web mis à jour
- [ ] Auto-updater testé
- [ ] Annonce communauté
- [ ] Documentation déploiement mise à jour

## 📞 Support

### **En cas de problème :**
1. **Vérifier logs** GitHub Actions détaillés
2. **Tester localement** le build qui échoue
3. **Comparer** avec releases précédentes réussies
4. **Consulter** documentation Tauri/GitHub Actions

### **Ressources :**
- **GitHub Actions Docs** : https://docs.github.com/actions
- **Tauri CI/CD** : https://tauri.app/v1/guides/building/cross-platform
- **Semantic Versioning** : https://semver.org

---

**🎉 Système de release automatisé prêt pour distribution VoidBot multi-plateforme !**