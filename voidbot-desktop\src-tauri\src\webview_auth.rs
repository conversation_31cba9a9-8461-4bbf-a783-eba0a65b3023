// Module d'authentification Discord via webview automatique
// Extraction automatique du token utilisateur (comme Nighty)
use tauri::{command, Window, Manager};
use serde::{Deserialize, Serialize};
use anyhow::Result;
use tokio::time::{timeout, Duration};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiscordAuthResult {
    pub success: bool,
    pub token: Option<String>,
    pub user_info: Option<DiscordUserInfo>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiscordUserInfo {
    pub id: String,
    pub username: String,
    pub discriminator: String,
    pub avatar: Option<String>,
    pub email: Option<String>,
}

/// Gestionnaire d'authentification Discord via webview
pub struct DiscordWebAuth {
    pub window: Option<tauri::WebviewWindow<tauri::Wry>>,
}

impl DiscordWebAuth {
    pub fn new() -> Self {
        Self {
            window: None,
        }
    }

    /// Démarre le processus d'authentification Discord via webview
    pub async fn start_auth(&mut self, main_window: &Window<tauri::Wry>) -> Result<DiscordAuthResult> {
        // 1. Créer une nouvelle webview pour Discord
        let webview = tauri::WebviewWindowBuilder::new(
            main_window.app_handle(),
            "discord_auth",
            tauri::WebviewUrl::External("https://discord.com/login".parse().unwrap())
        )
        .title("Connexion Discord - VoidBot")
        .inner_size(450.0, 650.0)
        .center()
        .resizable(false)
        .always_on_top(true)
        .build()?;

        self.window = Some(webview.clone());

        // 2. Injecter le script d'extraction de token
        let extraction_script = self.get_token_extraction_script();
        
        // 3. Attendre que la page Discord soit chargée
        tokio::time::sleep(Duration::from_secs(3)).await;
        
        // 4. Démarrer l'extraction en boucle
        let result = timeout(Duration::from_secs(300), async {
            self.extract_token_loop(&webview).await
        }).await??;

        // 5. Fermer la webview
        if let Some(window) = &self.window {
            let _ = window.close();
        }

        Ok(result)
    }

    /// Boucle d'extraction du token Discord
    async fn extract_token_loop(&self, webview: &tauri::WebviewWindow<tauri::Wry>) -> Result<DiscordAuthResult> {
        let mut attempts = 0;
        let max_attempts = 120; // 5 minutes max (2.5s * 120 = 300s)

        loop {
            attempts += 1;
            
            if attempts > max_attempts {
                return Ok(DiscordAuthResult {
                    success: false,
                    token: None,
                    user_info: None,
                    error: Some("Timeout - Aucun token détecté après 5 minutes".to_string()),
                });
            }

            // Injecter le script d'extraction
            let script = self.get_token_extraction_script();
            match webview.eval(&script) {
                Ok(_) => {
                    // Attendre un peu pour que le script s'exécute
                    tokio::time::sleep(Duration::from_millis(500)).await;
                    
                    // Vérifier si un token a été extrait
                    let check_script = r#"
                        (function() {
                            if (window.voidbotToken) {
                                return {
                                    success: true,
                                    token: window.voidbotToken,
                                    userInfo: window.voidbotUserInfo || null
                                };
                            }
                            return null;
                        })();
                    "#;
                    
                    if let Ok(result_str) = webview.eval(check_script) {
                        // Le résultat sera récupéré via les événements Tauri
                        // Pour l'instant, on continue la boucle
                    }
                }
                Err(e) => {
                    println!("Erreur injection script: {}", e);
                }
            }
            
            tokio::time::sleep(Duration::from_millis(2000)).await;
        }
    }

    /// Script JavaScript pour extraire le token Discord automatiquement
    fn get_token_extraction_script(&self) -> String {
        r#"
        (function() {
            try {
                // Méthode 1: localStorage token
                let token = null;
                let userInfo = null;
                
                // Rechercher le token dans localStorage
                for (let i = 0; i < localStorage.length; i++) {
                    let key = localStorage.key(i);
                    if (key && key.includes('token')) {
                        let value = localStorage.getItem(key);
                        if (value && typeof value === 'string') {
                            // Tenter de parser si c'est du JSON
                            try {
                                let parsed = JSON.parse(value);
                                if (parsed.token && typeof parsed.token === 'string' && parsed.token.length > 50) {
                                    token = parsed.token;
                                    break;
                                }
                            } catch {
                                // Si ce n'est pas du JSON, vérifier si c'est directement un token
                                if (value.length > 50 && (value.startsWith('mfa.') || /^[A-Za-z0-9+/=]{50,}$/.test(value))) {
                                    token = value;
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // Méthode 2: Webview inspection des requêtes
                if (!token && window.webContents) {
                    // Intercepter les requêtes réseau
                    let originalFetch = window.fetch;
                    window.fetch = function(...args) {
                        let headers = args[1]?.headers || {};
                        if (headers.Authorization || headers.authorization) {
                            let auth = headers.Authorization || headers.authorization;
                            if (auth && auth.includes('Bot ')) {
                                token = auth.replace('Bot ', '');
                            }
                        }
                        return originalFetch.apply(this, args);
                    };
                }
                
                // Méthode 3: Extraction depuis les modules Discord (si accessible)
                if (!token && window.webpackChunkdiscord_app) {
                    try {
                        window.webpackChunkdiscord_app.push([
                            [Math.random()],
                            {},
                            req => {
                                for (let m of Object.keys(req.c).map(x => req.c[x].exports).filter(x => x && x.default && x.default.getToken)) {
                                    token = m.default.getToken();
                                    break;
                                }
                            }
                        ]);
                    } catch (e) {
                        console.log('Erreur extraction webpack:', e);
                    }
                }
                
                // Méthode 4: sessionStorage
                if (!token) {
                    for (let i = 0; i < sessionStorage.length; i++) {
                        let key = sessionStorage.key(i);
                        if (key && key.includes('token')) {
                            let value = sessionStorage.getItem(key);
                            if (value && value.length > 50) {
                                try {
                                    let parsed = JSON.parse(value);
                                    if (parsed.token) {
                                        token = parsed.token;
                                        break;
                                    }
                                } catch {
                                    if (/^[A-Za-z0-9+/=]{50,}$/.test(value)) {
                                        token = value;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Si un token est trouvé, essayer d'extraire les infos utilisateur
                if (token && window.webpackChunkdiscord_app) {
                    try {
                        window.webpackChunkdiscord_app.push([
                            [Math.random()],
                            {},
                            req => {
                                for (let m of Object.keys(req.c).map(x => req.c[x].exports).filter(x => x && x.default && x.default.getCurrentUser)) {
                                    userInfo = m.default.getCurrentUser();
                                    break;
                                }
                            }
                        ]);
                    } catch (e) {
                        console.log('Erreur extraction user info:', e);
                    }
                }
                
                // Stocker les résultats globalement pour récupération
                if (token) {
                    window.voidbotToken = token;
                    window.voidbotUserInfo = userInfo;
                    
                    // Envoyer un événement à Tauri
                    if (window.__TAURI__ && window.__TAURI__.event) {
                        window.__TAURI__.event.emit('token-extracted', {
                            success: true,
                            token: token,
                            userInfo: userInfo
                        });
                    }
                    
                    console.log('✅ Token Discord extrait avec succès pour VoidBot');
                    return { success: true, token: token, userInfo: userInfo };
                }
                
                return { success: false, error: 'Token non trouvé' };
                
            } catch (error) {
                console.error('Erreur extraction token Discord:', error);
                return { success: false, error: error.toString() };
            }
        })();
        "#.to_string()
    }
}

/// Commande Tauri pour démarrer l'authentification Discord
#[command]
pub async fn start_discord_webauth(window: Window<tauri::Wry>) -> Result<DiscordAuthResult, String> {
    let mut auth = DiscordWebAuth::new();
    
    match auth.start_auth(&window).await {
        Ok(result) => Ok(result),
        Err(e) => Err(format!("Erreur authentification Discord: {}", e))
    }
}

/// Commande Tauri pour vérifier un token Discord extrait
#[command]
pub async fn verify_discord_token(token: String) -> Result<DiscordUserInfo, String> {
    // Mode test local - simule une vérification réussie
    if token == "test_token_mock_development_mode_voidbot" || std::env::var("VOIDBOT_TEST_MODE").unwrap_or_default() == "true" {
        tokio::time::sleep(tokio::time::Duration::from_millis(800)).await; // Simule requête API
        
        return Ok(DiscordUserInfo {
            id: "123456789012345678".to_string(),
            username: "TestUser".to_string(),
            discriminator: "0001".to_string(),
            avatar: None,
            email: Some("<EMAIL>".to_string()),
        });
    }
    
    let client = reqwest::Client::new();
    
    let response = client
        .get("https://discord.com/api/v10/users/@me")
        .header("Authorization", token)
        .send()
        .await
        .map_err(|e| format!("Erreur requête Discord: {}", e))?;
    
    if response.status().is_success() {
        let user_info: DiscordUserInfo = response
            .json()
            .await
            .map_err(|e| format!("Erreur parsing JSON: {}", e))?;
        Ok(user_info)
    } else {
        Err(format!("Token invalide - Status: {}", response.status()))
    }
}