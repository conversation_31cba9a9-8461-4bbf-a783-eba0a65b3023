# 🤖 VoidBot - Ultimate Discord Toolkit

[![Deploy to Railway](https://railway.app/button.svg)](https://railway.app/new/template/your-template)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Rust](https://img.shields.io/badge/rust-1.75+-orange.svg)](https://www.rust-lang.org)
[![React](https://img.shields.io/badge/react-18+-61DAFB.svg)](https://reactjs.org)

> Clone moderne de Nighty développé en Rust avec Tauri v2 + déploiement cloud Railway

## ⚠️ **AVERTISSEMENT LÉGAL IMPORTANT**

**🚨 LISEZ ATTENTIVEMENT AVANT D'UTILISER CE PROJET 🚨**

### **Risques Légaux et Techniques**
- **VoidBot utilise votre token Discord personnel** (selfbot)
- **Ceci VIOLE les Terms of Service de Discord** ([Discord ToS Section 3.3](https://discord.com/terms))
- **Risque de BAN PERMANENT** de votre compte Discord
- **Perte d'accès** à tous vos serveurs Discord
- **Aucun recours possible** auprès de Discord

### **Fonctionnalités Particulièrement Risquées**
- 🎁 **Giveaway Sniper** : Participation automatique (détection facile)
- 💎 **Nitro Sniper** : Capture de codes (très surveillé par Discord)
- 👻 **Message Snipe** : Lecture de messages supprimés
- 🎪 **Système de Trolls** : Comportements automatisés abusifs

### **Utilisation Recommandée**
- ✅ **À des fins éducatives uniquement**
- ✅ **Recherche et apprentissage** de l'architecture Rust/Tauri
- ✅ **Analyse de code** et bonnes pratiques
- ❌ **PAS pour un usage quotidien** sur votre compte principal
- ❌ **PAS sur des serveurs importants** pour vous

### **Responsabilité**
```
⚠️  UTILISATION À VOS RISQUES ET PÉRILS
    Les développeurs ne sont PAS responsables :
    • Des bans de comptes Discord
    • Des pertes d'accès à des serveurs
    • Des conséquences légales
    • Des dommages directs ou indirects
```

**Si vous continuez, vous acceptez ces risques en toute connaissance de cause.**

---

## 🚀 Démarrage rapide

### Prérequis

- **Rust** (dernière version stable)
- **Node.js** (version LTS)
- **Git**

### Installation

1. **Cloner le projet**
```bash
git clone <repo-url>
cd VoidBot
```

2. **Installer les dépendances**
```bash
# Dépendances frontend
cd voidbot-desktop && npm install && cd ..

# Vérifier la compilation Rust
cargo check
```

3. **Configuration Discord Selfbot**
```bash
# Copier le fichier d'exemple
cp discord-bot/.env.example discord-bot/.env

# Éditer avec votre token utilisateur Discord
nano discord-bot/.env
```

**⚠️ Important** : VoidBot utilise votre **token utilisateur** Discord (selfbot), pas un token de bot.
Pour récupérer votre token :
1. Ouvrir Discord dans le navigateur
2. F12 → Onglet Network → Rafraîchir la page
3. Chercher une requête → Header `Authorization` → Copier le token

### 🖥️ Lancement de l'application desktop

```bash
cd voidbot-desktop
npm run tauri dev
```

L'application s'ouvrira avec :
- **Interface moderne** style cyberpunk
- **Dashboard** avec connexion bot
- **Mode Stealth** Normal/Fantôme
- **Navigation** entre les différentes pages

### 🤖 Lancement du selfbot Discord

```bash
cd discord-bot
cargo run
```

Le selfbot démarre avec :
- **Votre compte Discord** comme "bot"
- **+20 commandes slash** en français
- **Mode Normal/Fantôme** pour réponses
- **Support complet** des fonctionnalités Nighty

### 🌐 Site web vitrine (à venir)

```bash
cd website
npm run dev
```

## 📋 Fonctionnalités principales

### Selfbot Discord
- ✅ **Token utilisateur** (votre compte Discord)
- ✅ **Commandes slash** uniquement (système Discord natif)
- ✅ **Mode Stealth** : Normal (public) / Fantôme (ephemeral)
- ✅ **Commandes essentielles** : animate, rpc, backup, snipe, etc.
- ✅ **Interface française** complète

### Application Desktop
- ✅ **Interface Tauri v2** + React + TypeScript
- ✅ **Design cyberpunk** avec Tailwind CSS
- ✅ **Dashboard** temps réel
- ✅ **Gestion mode furtif** en temps réel
- ✅ **Navigation fluide** entre pages

### Commandes disponibles

| Catégorie | Commandes | Description |
|-----------|-----------|-------------|
| **Animation** | `/animate`, `/animate_stop` | Animations de profil |
| **Rich Presence** | `/rpc`, `/rpc_presets` | Configuration RPC |
| **Backup** | `/backup`, `/restore`, `/clone` | Sauvegarde serveurs |
| **Utilitaires** | `/snipe`, `/clear`, `/ping` | Outils divers |
| **Fun** | `/8ball`, `/coinflip`, `/dice` | Commandes amusantes |
| **Info** | `/server_info`, `/user_info` | Informations |

## 🛠️ Développement

### Structure du projet
```
VoidBot/
├── voidbot-desktop/    # App Tauri v2 + React
├── discord-bot/        # Bot Discord Serenity
├── shared/            # Types partagés
├── backend-api/       # API Rust (futur)
├── website/          # Site vitrine (futur)
└── CLAUDE.md         # Documentation mémoire
```

### Commandes utiles

```bash
# Build complet
cargo build --release

# Tests
cargo test

# Lint et format
cargo clippy
cargo fmt

# Build frontend
cd voidbot-desktop && npm run build
```

### Mode debug

L'application desktop ouvre automatiquement les DevTools en mode développement pour débugger l'interface React.

## 🎨 Personnalisation

### Thème
- Couleurs dans `voidbot-desktop/tailwind.config.js`
- Styles globaux dans `voidbot-desktop/src/styles/globals.css`

### Commandes bot
- Ajouter dans `discord-bot/src/commands/`
- Enregistrer dans `discord-bot/src/commands/mod.rs`
- Handler dans `discord-bot/src/handlers.rs`

## 🚧 Roadmap

### Phase actuelle ✅
- [x] Structure projet Rust
- [x] Application Tauri v2 fonctionnelle
- [x] Bot Discord avec commandes slash
- [x] Mode Stealth opérationnel
- [x] Interface React complète

### Prochaines étapes 🔄
- [ ] Site web vitrine (Astro)
- [ ] Configuration Railway
- [ ] Implémentation features avancées
- [ ] Base de données PostgreSQL
- [ ] Tests automatisés

## 📚 Documentation

- **CLAUDE.md** : Mémoire complète du projet
- **Code comments** : En français
- **Interface** : Entièrement française

## ⚖️ Licence et Responsabilité

### **Licence MIT**
Ce projet est sous licence MIT à des fins éducatives et de recherche.

### **Disclaimer Légal**
```
⚠️ AVERTISSEMENT FINAL

• Ce projet est fourni "EN L'ÉTAT" sans aucune garantie
• L'utilisation de selfbots VIOLE les Terms of Service de Discord
• Risque de suspension permanente de votre compte Discord
• Les développeurs déclinent toute responsabilité
• Utilisation strictement à des fins éducatives
• Respectez les lois locales et les ToS des plateformes
```

### **Pour les Développeurs**
Si vous contribuez à ce projet :
- Comprenez les implications légales
- Ne encouragez pas l'utilisation abusive
- Priorisez l'aspect éducatif
- Ajoutez des avertissements dans vos contributions

---

**Note** : Assurez-vous d'avoir installé Rust et Node.js avant de commencer. En cas de problème, vérifiez les prérequis Tauri pour votre OS.