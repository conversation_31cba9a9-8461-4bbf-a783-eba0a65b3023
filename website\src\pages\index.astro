---
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import '../styles/global.css';

const features = [
  {
    icon: "👻",
    title: "Mode Furtif",
    description: "Basculez entre mode normal et fantôme pour des réponses visibles ou privées"
  },
  {
    icon: "🎪",
    title: "Commandes Troll",
    description: "5 types de trolls avancés avec sécurité intégrée et contrôle temps réel"
  },
  {
    icon: "🎁", 
    title: "Giveaway Joiner",
    description: "Participation automatique aux giveaways avec détection intelligente"
  },
  {
    icon: "💎",
    title: "<PERSON><PERSON> Sniper",
    description: "Capture ultra-rapide des codes Nitro en 50-200ms"
  },
  {
    icon: "🔔",
    title: "Notifications",
    description: "14 types d'événements surveillés avec ghostping en temps réel"
  },
  {
    icon: "🤖",
    title: "Auto-Commands",
    description: "Traduction automatique, slash commands et réponses intelligentes"
  },
  {
    icon: "🎨",
    title: "Génération Images",
    description: "Memes, overlays, bannières et statistiques générés automatiquement"
  },
  {
    icon: "⚡",
    title: "Rate Limiting",
    description: "Protection avancée contre les bans Discord avec monitoring"
  },
  {
    icon: "🎯",
    title: "Interface Native",
    description: "Application desktop Tauri avec React et design cyberpunk"
  }
];

const stats = [
  { number: "100+", label: "Commandes Discord" },
  { number: "14", label: "Types d'événements" },
  { number: "5", label: "Modes de troll" },
  { number: "50ms", label: "Temps de réaction" }
];
---

<Layout title="VoidBot - Toolkit Discord Avancé" description="Clone moderne de Nighty avec interface desktop sécurisée. +100 commandes, snipers ultra-rapides, trolls avancés et fonctionnalités exclusives.">
  <Navbar />
  
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="relative overflow-hidden py-20 lg:py-32">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <!-- Badge -->
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-indigo-500/10 border border-indigo-500/20 mb-8 fade-in">
            <span class="text-indigo-400 text-sm font-medium">🚀 Nouvelle version disponible</span>
          </div>

          <!-- Main headline -->
          <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold mb-6 slide-up">
            <span class="gradient-text">VoidBot</span>
            <br>
            <span class="text-white">Toolkit Discord</span>
            <br>
            <span class="text-gray-400 text-3xl sm:text-4xl lg:text-5xl">Ultime</span>
          </h1>

          <!-- Subtitle -->
          <p class="text-xl sm:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto slide-up">
            Clone moderne de <span class="text-indigo-400 font-semibold">Nighty</span> avec interface desktop sécurisée.
            <br class="hidden sm:block">
            +100 commandes, snipers ultra-rapides et fonctionnalités exclusives.
          </p>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 bounce-in">
            <a href="/download" class="btn btn-primary text-lg px-8 py-4 glow">
              <span class="flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Télécharger Gratuitement
              </span>
            </a>
            <a href="/features" class="btn btn-secondary text-lg px-8 py-4">
              Voir les Fonctionnalités
            </a>
          </div>

          <!-- Social proof / Stats -->
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-2xl mx-auto fade-in">
            {stats.map((stat) => (
              <div class="text-center">
                <div class="text-2xl sm:text-3xl font-bold gradient-text-primary">{stat.number}</div>
                <div class="text-sm text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-900/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section header -->
        <div class="text-center mb-16">
          <h2 class="text-3xl sm:text-4xl font-bold mb-4">
            <span class="gradient-text">Fonctionnalités</span>
            <span class="text-white"> Avancées</span>
          </h2>
          <p class="text-xl text-gray-300 max-w-2xl mx-auto">
            Découvrez les outils les plus puissants pour Discord avec une interface moderne et sécurisée
          </p>
        </div>

        <!-- Features grid -->
        <div class="feature-grid">
          {features.map((feature, index) => (
            <div class="card-hover group slide-up" style={`animation-delay: ${index * 0.1}s`}>
              <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-200">
                {feature.icon}
              </div>
              <h3 class="text-xl font-semibold mb-3 text-white group-hover:text-indigo-400 transition-colors duration-200">
                {feature.title}
              </h3>
              <p class="text-gray-400 group-hover:text-gray-300 transition-colors duration-200">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Why VoidBot Section -->
    <section class="py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <!-- Content -->
          <div>
            <h2 class="text-3xl sm:text-4xl font-bold mb-6">
              Pourquoi <span class="gradient-text">VoidBot</span> ?
            </h2>
            <div class="space-y-6">
              <div class="flex items-start gap-4">
                <div class="flex-shrink-0 w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mt-1">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-2 text-white">100% Local</h3>
                  <p class="text-gray-400">Toutes vos données restent sur votre machine. Aucun serveur externe requis.</p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div class="flex-shrink-0 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mt-1">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-2 text-white">Sécurisé</h3>
                  <p class="text-gray-400">Chiffrement AES-256-GCM pour vos tokens Discord. Code source auditable.</p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div class="flex-shrink-0 w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mt-1">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-2 text-white">Ultra-Rapide</h3>
                  <p class="text-gray-400">Architecture Rust native pour des performances maximales même sur machines peu puissantes.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Visual -->
          <div class="relative">
            <div class="aspect-square bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-2xl p-8 backdrop-blur-sm border border-gray-800">
              <div class="w-full h-full bg-gray-900/50 rounded-xl border border-gray-700 p-6 relative overflow-hidden">
                <!-- Fake terminal -->
                <div class="flex items-center gap-2 mb-4">
                  <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div class="space-y-2 font-mono text-sm">
                  <div class="text-green-400">$ voidbot --status</div>
                  <div class="text-gray-400">✅ Bot Discord: Connecté</div>
                  <div class="text-gray-400">⚡ Nitro Sniper: 47ms</div>
                  <div class="text-gray-400">🎁 Giveaways: 12 joués</div>
                  <div class="text-gray-400">👻 Mode: Fantôme</div>
                  <div class="text-cyan-400 animate-pulse">▊</div>
                </div>
                
                <!-- Glow effect -->
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-indigo-900/20 to-purple-900/20">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl sm:text-4xl font-bold mb-6">
          Prêt à <span class="gradient-text">dominer</span> Discord ?
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          Rejoignez des milliers d'utilisateurs qui font confiance à VoidBot pour leurs besoins Discord avancés.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/download" class="btn btn-primary text-lg px-8 py-4 glow">
            Commencer Maintenant
          </a>
          <a href="/docs" class="btn btn-secondary text-lg px-8 py-4">
            Lire la Documentation
          </a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-950 border-t border-gray-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <span class="font-bold text-xl gradient-text">VoidBot</span>
          </div>
          <p class="text-gray-400 max-w-md">
            Le toolkit Discord le plus avancé avec interface desktop moderne. 
            Sécurisé, rapide et entièrement local.
          </p>
        </div>

        <!-- Links -->
        <div>
          <h3 class="font-semibold text-white mb-4">Produit</h3>
          <ul class="space-y-2">
            <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
            <li><a href="/download" class="text-gray-400 hover:text-white transition-colors">Télécharger</a></li>
            <li><a href="/docs" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
            <li><a href="/support" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4">Communauté</h3>
          <ul class="space-y-2">
            <li><a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
            <li><a href="/discord" class="text-gray-400 hover:text-white transition-colors">Discord</a></li>
            <li><a href="/twitter" class="text-gray-400 hover:text-white transition-colors">Twitter</a></li>
            <li><a href="/changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 VoidBot. Tous droits réservés. 
          <span class="text-indigo-400">Made with 💜 for Discord</span>
        </p>
      </div>
    </div>
  </footer>
</Layout>