#!/bin/bash

# VoidBot Multi-Architecture Build Script
# Builds VoidBot for all supported platforms and architectures

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(dirname "$(dirname "$(realpath "$0")")")"
DESKTOP_DIR="$PROJECT_ROOT/voidbot-desktop"
BUILD_DIR="$PROJECT_ROOT/builds"
VERSION=$(grep '"version"' "$DESKTOP_DIR/package.json" | cut -d'"' -f4)

echo -e "${BLUE}🚀 VoidBot Multi-Architecture Build Script${NC}"
echo -e "${BLUE}Version: $VERSION${NC}"
echo -e "${BLUE}Project Root: $PROJECT_ROOT${NC}"
echo ""

# Create builds directory
mkdir -p "$BUILD_DIR"

# Function to print step
print_step() {
    echo -e "${YELLOW}📦 $1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're on the right OS for cross-compilation
check_build_environment() {
    print_step "Checking build environment..."
    
    # Check for Rust
    if ! command -v cargo &> /dev/null; then
        print_error "Rust/Cargo not found. Please install Rust first."
        exit 1
    fi
    
    # Check for Node.js
    if ! command -v npm &> /dev/null; then
        print_error "Node.js/npm not found. Please install Node.js first."
        exit 1
    fi
    
    # Check for Tauri CLI
    if ! command -v cargo tauri &> /dev/null; then
        print_error "Tauri CLI not found. Installing..."
        cargo install tauri-cli --version ^2.0
    fi
    
    print_success "Build environment ready"
}

# Install dependencies
install_dependencies() {
    print_step "Installing dependencies..."
    
    cd "$DESKTOP_DIR"
    npm ci
    
    print_success "Dependencies installed"
}

# Add Rust targets for cross-compilation
setup_rust_targets() {
    print_step "Setting up Rust targets..."
    
    # Windows targets
    rustup target add x86_64-pc-windows-msvc
    rustup target add i686-pc-windows-msvc
    
    # macOS targets (only on macOS)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        rustup target add x86_64-apple-darwin
        rustup target add aarch64-apple-darwin
    fi
    
    # Linux targets
    rustup target add x86_64-unknown-linux-gnu
    rustup target add aarch64-unknown-linux-gnu
    
    print_success "Rust targets configured"
}

# Build for specific target
build_target() {
    local target=$1
    local arch=$2
    local bundle_target=$3
    
    print_step "Building $target ($arch) - $bundle_target..."
    
    cd "$DESKTOP_DIR"
    
    # Set environment variables for cross-compilation
    export TAURI_BUNDLE_TARGET="$bundle_target"
    export TAURI_BUNDLE_ARCH="$arch"
    
    # Build command
    if [[ "$target" == "windows" ]]; then
        if [[ "$arch" == "x64" ]]; then
            cargo tauri build --target x86_64-pc-windows-msvc --bundles "$bundle_target"
        else
            cargo tauri build --target i686-pc-windows-msvc --bundles "$bundle_target"
        fi
    elif [[ "$target" == "macos" ]]; then
        if [[ "$arch" == "universal" ]]; then
            cargo tauri build --target universal-apple-darwin --bundles "$bundle_target"
        elif [[ "$arch" == "x64" ]]; then
            cargo tauri build --target x86_64-apple-darwin --bundles "$bundle_target"
        else
            cargo tauri build --target aarch64-apple-darwin --bundles "$bundle_target"
        fi
    elif [[ "$target" == "linux" ]]; then
        if [[ "$arch" == "x64" ]]; then
            cargo tauri build --target x86_64-unknown-linux-gnu --bundles "$bundle_target"
        else
            cargo tauri build --target aarch64-unknown-linux-gnu --bundles "$bundle_target"
        fi
    fi
    
    print_success "Built $target ($arch) - $bundle_target"
}

# Copy builds to organized directory
organize_builds() {
    print_step "Organizing build artifacts..."
    
    cd "$DESKTOP_DIR"
    
    # Create organized structure
    mkdir -p "$BUILD_DIR/windows"
    mkdir -p "$BUILD_DIR/macos"
    mkdir -p "$BUILD_DIR/linux"
    
    # Windows builds
    if [ -d "src-tauri/target/x86_64-pc-windows-msvc/release/bundle" ]; then
        cp -r src-tauri/target/x86_64-pc-windows-msvc/release/bundle/* "$BUILD_DIR/windows/" 2>/dev/null || true
    fi
    if [ -d "src-tauri/target/i686-pc-windows-msvc/release/bundle" ]; then
        cp -r src-tauri/target/i686-pc-windows-msvc/release/bundle/* "$BUILD_DIR/windows/" 2>/dev/null || true
    fi
    
    # macOS builds
    if [ -d "src-tauri/target/universal-apple-darwin/release/bundle" ]; then
        cp -r src-tauri/target/universal-apple-darwin/release/bundle/* "$BUILD_DIR/macos/" 2>/dev/null || true
    fi
    if [ -d "src-tauri/target/x86_64-apple-darwin/release/bundle" ]; then
        cp -r src-tauri/target/x86_64-apple-darwin/release/bundle/* "$BUILD_DIR/macos/" 2>/dev/null || true
    fi
    if [ -d "src-tauri/target/aarch64-apple-darwin/release/bundle" ]; then
        cp -r src-tauri/target/aarch64-apple-darwin/release/bundle/* "$BUILD_DIR/macos/" 2>/dev/null || true
    fi
    
    # Linux builds
    if [ -d "src-tauri/target/x86_64-unknown-linux-gnu/release/bundle" ]; then
        cp -r src-tauri/target/x86_64-unknown-linux-gnu/release/bundle/* "$BUILD_DIR/linux/" 2>/dev/null || true
    fi
    if [ -d "src-tauri/target/aarch64-unknown-linux-gnu/release/bundle" ]; then
        cp -r src-tauri/target/aarch64-unknown-linux-gnu/release/bundle/* "$BUILD_DIR/linux/" 2>/dev/null || true
    fi
    
    print_success "Build artifacts organized"
}

# Generate checksums
generate_checksums() {
    print_step "Generating checksums..."
    
    cd "$BUILD_DIR"
    
    # Generate SHA256 checksums for all files
    find . -type f \( -name "*.msi" -o -name "*.exe" -o -name "*.dmg" -o -name "*.deb" -o -name "*.AppImage" \) -exec sha256sum {} \; > checksums.sha256
    
    print_success "Checksums generated"
}

# Main build process
main() {
    echo -e "${BLUE}Starting VoidBot multi-architecture build...${NC}"
    echo ""
    
    check_build_environment
    install_dependencies
    setup_rust_targets
    
    # Current OS detection for native builds
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_step "Building Linux targets..."
        build_target "linux" "x64" "deb"
        build_target "linux" "x64" "appimage"
        build_target "linux" "arm64" "deb"
        build_target "linux" "arm64" "appimage"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_step "Building macOS targets..."
        build_target "macos" "universal" "dmg"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        print_step "Building Windows targets..."
        build_target "windows" "x64" "msi"
        build_target "windows" "x64" "nsis"
        build_target "windows" "x86" "msi"
        build_target "windows" "x86" "nsis"
    fi
    
    organize_builds
    generate_checksums
    
    echo ""
    echo -e "${GREEN}🎉 Build completed successfully!${NC}"
    echo -e "${GREEN}Build artifacts available in: $BUILD_DIR${NC}"
    echo -e "${GREEN}Version: $VERSION${NC}"
    echo ""
    
    # Show build summary
    echo -e "${BLUE}📊 Build Summary:${NC}"
    find "$BUILD_DIR" -type f \( -name "*.msi" -o -name "*.exe" -o -name "*.dmg" -o -name "*.deb" -o -name "*.AppImage" \) -exec basename {} \; | sort
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            print_step "Cleaning previous builds..."
            rm -rf "$BUILD_DIR"
            rm -rf "$DESKTOP_DIR/src-tauri/target"
            print_success "Cleaned previous builds"
            shift
            ;;
        --help)
            echo "VoidBot Multi-Architecture Build Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --clean    Clean previous builds before building"
            echo "  --help     Show this help message"
            echo ""
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main function
main