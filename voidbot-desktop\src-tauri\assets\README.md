# Assets pour Installeurs Windows

## 📁 Fichiers requis

### **NSIS (Nullsoft Installer)**
- `header-image.bmp` - 497x58 pixels - Image d'en-tête
- `sidebar-image.bmp` - 164x314 pixels - Image sidebar gauche

### **WiX (MSI Installer)**  
- `banner.bmp` - 493x58 pixels - Banner haut
- `dialog.bmp` - 493x312 pixels - Dialog principal

### **Linux Desktop**
- `voidbot.desktop` - Fichier desktop Linux

## 🎨 Génération automatique

### **Avec Python + Pillow :**
```bash
cd src-tauri
pip install Pillow
python build-assets.py
```

### **<PERSON> (si Python indisponible) :**
1. Créer des images BMP aux bonnes dimensions
2. Utiliser les couleurs VoidBot :
   - Fond : #111827 (gray-900)
   - Texte : #8B5CF6 (indigo-500)  
   - Accent : #1F2937 (gray-800)

## 📐 Spécifications

### **Couleurs VoidBot :**
```
Primary: #8B5CF6 (139, 92, 246)   - indigo-500
Dark: #111827 (17, 24, 39)        - gray-900  
Medium: #1F2937 (31, 41, 55)      - gray-800
Light: #374151 (55, 65, 81)       - gray-700
```

### **Polices :**
- **Titre** : Arial Bold 24px
- **Sous-titre** : Arial Regular 16px
- **Description** : Arial Regular 12px

### **Contenu texte :**
- **Titre principal** : "VoidBot"
- **Sous-titre** : "Ultimate Discord Toolkit"
- **Description** : "Toolkit Discord avancé et sécurisé"

## 🔧 Utilisation

Ces assets sont automatiquement intégrés lors du build Tauri :

```bash
# Build avec assets
npm run tauri build --target x86_64-pc-windows-msvc --bundles msi,nsis
```

Les installeurs utilisent ces images pour créer une expérience d'installation professionnelle et cohérente avec l'identité visuelle VoidBot.

## ⚠️ Notes importantes

- **Format obligatoire** : BMP (Bitmap) pour compatibilité Windows
- **Dimensions exactes** : Respecter les tailles spécifiées
- **Profondeur couleur** : 24-bit minimum recommandé
- **Compression** : Non compressé pour éviter les artéfacts

Les assets actuels sont des placeholders fonctionnels. Pour une version production, remplacer par des images VoidBot personnalisées.