@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo.
echo 🛠️ VoidBot - Préparation Build Windows
echo =====================================
echo.

set "PROJECT_ROOT=%~dp0.."
set "DESKTOP_DIR=%PROJECT_ROOT%\voidbot-desktop"

echo 📂 Vérification structure projet...

if not exist "%DESKTOP_DIR%" (
    echo ❌ Dossier voidbot-desktop non trouvé
    pause
    exit /b 1
)

cd /d "%DESKTOP_DIR%"

echo ✅ Projet trouvé: %DESKTOP_DIR%
echo.

echo 🔧 Préparation environnement...

REM Vérifier Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js non installé
    echo    Télécharger: https://nodejs.org
    pause
    exit /b 1
)

REM Vérifier Rust
cargo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Rust non installé  
    echo    Télécharger: https://rustup.rs
    pause
    exit /b 1
)

echo ✅ Node.js et Rust détectés
echo.

echo 📦 Installation dépendances npm...
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Erreur installation npm
        pause
        exit /b 1
    )
) else (
    echo ✅ Dépendances déjà installées
)

echo.
echo 🦀 Configuration cibles Rust...
rustup target add x86_64-pc-windows-msvc >nul 2>&1
rustup target add i686-pc-windows-msvc >nul 2>&1
echo ✅ Cibles Windows ajoutées

echo.
echo 🎨 Génération assets installeurs...
cd src-tauri

if exist "build-assets.py" (
    echo    Tentative avec Pillow...
    python build-assets.py >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Assets générés avec Pillow
    ) else (
        echo ⚠️ Pillow non disponible, utilisation assets basiques...
        python build-assets-simple.py
        if %errorlevel% equ 0 (
            echo ✅ Assets basiques générés
        ) else (
            echo ❌ Erreur génération assets
        )
    )
) else (
    echo ⚠️ Script build-assets.py non trouvé
)

cd ..

echo.
echo 📁 Vérification assets...
if exist "src-tauri\assets\header-image.bmp" (
    echo ✅ header-image.bmp
) else (
    echo ❌ header-image.bmp manquant
)

if exist "src-tauri\assets\sidebar-image.bmp" (
    echo ✅ sidebar-image.bmp
) else (
    echo ❌ sidebar-image.bmp manquant
)

if exist "src-tauri\assets\banner.bmp" (
    echo ✅ banner.bmp
) else (
    echo ❌ banner.bmp manquant
)

if exist "src-tauri\assets\dialog.bmp" (
    echo ✅ dialog.bmp
) else (
    echo ❌ dialog.bmp manquant
)

echo.
echo 📋 Vérification configuration Tauri...

if exist "src-tauri\tauri.conf.json" (
    echo ✅ tauri.conf.json trouvé
) else (
    echo ❌ tauri.conf.json manquant
)

if exist "src-tauri\tauri.prod.conf.json" (
    echo ✅ tauri.prod.conf.json trouvé
) else (
    echo ⚠️ tauri.prod.conf.json manquant (optionnel)
)

echo.
echo 🔍 Vérification templates installeurs...

if exist "src-tauri\installer.nsi" (
    echo ✅ Template NSIS trouvé
) else (
    echo ❌ Template NSIS manquant
)

if exist "src-tauri\main.wxs" (
    echo ✅ Template WiX trouvé
) else (
    echo ❌ Template WiX manquant
)

echo.
echo 📊 Résumé de préparation:
echo ========================

echo 🔧 Environnement:
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
for /f "tokens=2" %%i in ('cargo --version') do set CARGO_VERSION=%%i

echo    Node.js: %NODE_VERSION%
echo    npm: %NPM_VERSION%
echo    Cargo: %CARGO_VERSION%

echo.
echo 📁 Structure:
echo    ✅ Projet VoidBot configuré
echo    ✅ Dépendances npm installées
echo    ✅ Cibles Rust Windows ajoutées
echo    ✅ Assets installeurs générés

echo.
echo 🚀 Prêt pour le build !
echo.
echo 💡 Prochaines étapes:
echo    1. Exécuter: scripts\build-windows.bat
echo    2. Ou: scripts\build-windows.ps1
echo    3. Récupérer installeurs dans builds\windows\
echo.

echo Appuyer sur une touche pour continuer...
pause >nul