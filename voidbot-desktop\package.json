{"name": "voidbot-desktop", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:strict": "tsc && vite build", "build:prod": "tsc && vite build --mode production", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:build:prod": "npm run build:prod && tauri build --config src-tauri/tauri.prod.conf.json", "build:windows": "tauri build --target x86_64-pc-windows-msvc --bundles msi,nsis", "build:windows:x86": "tauri build --target i686-pc-windows-msvc --bundles msi,nsis", "build:macos": "tauri build --target universal-apple-darwin --bundles dmg", "build:linux": "tauri build --target x86_64-unknown-linux-gnu --bundles deb,appimage", "build:linux:arm64": "tauri build --target aarch64-unknown-linux-gnu --bundles deb,appimage", "build:all": "../scripts/build-all.sh", "build:installers": "../scripts/build-installers.sh", "build:clean": "rm -rf src-tauri/target && rm -rf dist", "assets:generate": "cd src-tauri && python3 build-assets.py", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}", "format:check": "prettier --check src/**/*.{ts,tsx,js,jsx,json,css,md}"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2", "@tauri-apps/plugin-updater": "^2", "framer-motion": "^11.0.0", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.20.0", "zustand": "^4.4.0"}, "devDependencies": {"@tauri-apps/cli": "^2.6.2", "@types/node": "^24.0.12", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "~5.6.2", "vite": "^6.0.3"}}