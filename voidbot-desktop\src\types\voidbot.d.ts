/**
 * Déclarations TypeScript pour VoidBot
 * Définit tous les types utilisés dans l'application pour une meilleure sécurité de type
 */

declare namespace VoidBot {
  // === TYPES DE BASE ===
  
  /**
   * Mode de furtivité pour les réponses de commandes Discord
   */
  type StealthMode = 'normal' | 'ghost';

  /**
   * Statut de connexion du bot Discord avec états de transition
   */
  type BotStatus = 'offline' | 'connecting' | 'online' | 'error';

  /**
   * Niveaux de priorité pour les notifications et événements
   */
  type Priority = 'Low' | 'Medium' | 'High' | 'Critical';

  /**
   * Services de traduction supportés
   */
  type TranslationService = 'google' | 'deepl' | 'bing';

  /**
   * Catégories de thèmes disponibles
   */
  type ThemeCategory = 'builtin' | 'community' | 'custom';

  // === INTERFACES UTILISATEUR ===

  /**
   * Informations d'utilisateur Discord
   */
  interface DiscordUser {
    /** ID unique Discord */
    id: string;
    /** Nom d'utilisateur (sans discriminant) */
    username: string;
    /** Discriminant numérique à 4 chiffres */
    discriminator: string;
    /** Hash avatar Discord (optionnel) */
    avatar?: string;
    /** Email du compte (optionnel) */
    email?: string;
  }

  /**
   * Session utilisateur sauvegardée (chiffrée)
   */
  interface UserSession {
    /** Données utilisateur */
    userData: DiscordUser;
    /** Token chiffré */
    encryptedToken: string;
    /** Connexion automatique activée */
    autoLogin: boolean;
    /** Date de création de la session */
    createdAt: string;
    /** Date d'expiration */
    expiresAt: string;
  }

  // === INTERFACES NOTIFICATIONS ===

  /**
   * Types d'événements de notification supportés
   */
  type NotificationEventType = 
    | 'Ghostping'
    | 'FriendRemoved'
    | 'FriendBlocked'
    | 'ServerKicked'
    | 'ServerBanned'
    | 'RoleChanged'
    | 'NicknameChanged'
    | 'KeywordDetected'
    | 'TicketReplied'
    | 'UserTyping'
    | 'GiveawayDetected'
    | 'NitroSniped'
    | 'ChannelMessage'
    | 'Mentioned';

  /**
   * Notification système VoidBot
   */
  interface Notification {
    /** ID unique de la notification */
    id: string;
    /** Type d'événement */
    event_type: NotificationEventType;
    /** Titre de la notification */
    title: string;
    /** Description détaillée */
    description: string;
    /** Niveau de priorité */
    priority: Priority;
    /** Timestamp de création */
    timestamp: string;
    /** ID du serveur (optionnel) */
    guild_id?: string;
    /** ID du canal (optionnel) */
    channel_id?: string;
    /** ID de l'utilisateur (optionnel) */
    user_id?: string;
    /** Métadonnées additionnelles */
    metadata: Record<string, string>;
    /** Statut de lecture */
    read: boolean;
  }

  /**
   * Configuration du système de notifications
   */
  interface NotificationConfig {
    /** Types d'événements activés */
    enabled_events: NotificationEventType[];
    /** Mots-clés surveillés */
    keywords: string[];
    /** URLs de webhooks Discord */
    webhook_urls: string[];
    /** Notifications desktop activées */
    desktop_notifications: boolean;
    /** Sons activés */
    sound_enabled: boolean;
    /** Volume des sons (0.0-1.0) */
    sound_volume: number;
    /** Rétention historique en jours */
    history_retention_days: number;
    /** Canaux surveillés */
    monitored_channels: string[];
    /** Utilisateurs surveillés */
    monitored_users: string[];
  }

  // === INTERFACES THÈMES ===

  /**
   * Configuration complète d'un thème
   */
  interface ThemeConfig {
    /** Nom du thème */
    name: string;
    /** Palette de couleurs */
    colors: {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
      surface: string;
      text: string;
      textSecondary: string;
      success: string;
      warning: string;
      error: string;
      border: string;
    };
    /** Dégradés CSS */
    gradients: {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
    };
    /** Ombres et effets */
    shadows: {
      small: string;
      medium: string;
      large: string;
      glow: string;
    };
    /** Rayons de bordure */
    borderRadius: {
      small: string;
      medium: string;
      large: string;
    };
    /** Paramètres d'animation */
    animations: {
      duration: string;
      easing: string;
      scale: number;
    };
    /** Thème par défaut */
    isDefault: boolean;
    /** Date de création */
    createdAt: string;
    /** Dernière modification */
    lastModified: string;
  }

  /**
   * Thème prédéfini avec métadonnées
   */
  interface PresetTheme {
    /** Identifiant unique */
    id: string;
    /** Nom d'affichage */
    name: string;
    /** Description */
    description: string;
    /** Aperçu CSS gradient */
    preview: string;
    /** Catégorie */
    category: ThemeCategory;
    /** Configuration */
    config: ThemeConfig;
  }

  // === INTERFACES AUTO-COMMANDES ===

  /**
   * Configuration Auto-Translate
   */
  interface AutoTranslateConfig {
    /** Système activé */
    enabled: boolean;
    /** Service de traduction */
    service: TranslationService;
    /** Langue cible */
    target_language: string;
    /** Canaux ignorés */
    ignore_channels: string[];
    /** Utilisateurs ignorés */
    ignore_users: string[];
    /** Longueur minimum */
    min_length: number;
    /** Seuil de similarité */
    similarity_threshold: number;
  }

  /**
   * Trigger pour Auto-Slash
   */
  interface SlashTrigger {
    /** Mot/phrase déclencheur */
    trigger: string;
    /** Commande à exécuter */
    command: string;
    /** Délai en ms */
    delay_ms: number;
    /** Cooldown en secondes */
    cooldown_seconds: number;
    /** Trigger actif */
    enabled: boolean;
    /** Canaux autorisés */
    channels: string[];
    /** Correspondance exacte */
    exact_match: boolean;
    /** Sensible à la casse */
    case_sensitive: boolean;
  }

  /**
   * Configuration Auto-Slash
   */
  interface AutoSlashConfig {
    /** Système activé */
    enabled: boolean;
    /** Liste des triggers */
    triggers: SlashTrigger[];
    /** Cooldown global */
    global_cooldown_seconds: number;
  }

  /**
   * Réponse automatique
   */
  interface AutoReply {
    /** Mot/phrase déclencheur */
    trigger: string;
    /** Réponses possibles */
    responses: string[];
    /** Délai en ms */
    delay_ms: number;
    /** Cooldown en secondes */
    cooldown_seconds: number;
    /** Réponse active */
    enabled: boolean;
    /** Probabilité (0.0-1.0) */
    probability: number;
    /** DM uniquement */
    dm_only: boolean;
    /** Supprimer le trigger */
    delete_trigger: boolean;
    /** Mode fantôme */
    ghost_mode: boolean;
  }

  /**
   * Configuration Auto-Reply
   */
  interface AutoReplyConfig {
    /** Système activé */
    enabled: boolean;
    /** Liste des réponses */
    replies: AutoReply[];
    /** Cooldown global */
    global_cooldown_seconds: number;
  }

  // === INTERFACES SNIPERS ===

  /**
   * Configuration Giveaway Joiner
   */
  interface GiveawayJoinerConfig {
    /** Système activé */
    enabled: boolean;
    /** Délai minimum (ms) */
    min_delay_ms: number;
    /** Délai maximum (ms) */
    max_delay_ms: number;
    /** Serveurs surveillés */
    monitored_guilds: string[];
    /** Serveurs blacklistés */
    blacklisted_guilds: string[];
    /** Mots-clés requis */
    required_keywords: string[];
    /** Mots-clés interdits */
    forbidden_keywords: string[];
    /** Comptes alternatifs */
    alt_accounts: string[];
  }

  /**
   * Configuration Nitro Sniper
   */
  interface NitroSniperConfig {
    /** Système activé */
    enabled: boolean;
    /** Mode test (sans récupération) */
    test_mode: boolean;
    /** Délai minimum (ms) */
    min_delay_ms: number;
    /** Délai maximum (ms) */
    max_delay_ms: number;
    /** Bots de confiance */
    trusted_bots: string[];
    /** Serveurs surveillés */
    monitored_guilds: string[];
    /** Canaux surveillés */
    monitored_channels: string[];
  }

  // === INTERFACES STATISTIQUES ===

  /**
   * Statistiques d'utilisation globales
   */
  interface UsageStats {
    /** Commandes exécutées */
    commandsUsed: number;
    /** Serveurs sauvegardés */
    serversBackedUp: number;
    /** Animations actives */
    animationsActive: number;
    /** Temps de fonctionnement (secondes) */
    uptime: number;
  }

  /**
   * Statistiques de performance Nitro Sniper
   */
  interface NitroSniperStats {
    /** Codes détectés */
    codes_detected: number;
    /** Codes récupérés */
    codes_claimed: number;
    /** Taux de succès */
    success_rate: number;
    /** Temps de réaction moyen (ms) */
    average_reaction_time: number;
    /** Valeur totale récupérée */
    total_value_claimed: string;
  }

  /**
   * Statistiques Giveaway Joiner
   */
  interface GiveawayJoinerStats {
    /** Giveaways détectés */
    giveaways_detected: number;
    /** Participations */
    participations: number;
    /** Giveaways gagnés */
    giveaways_won: number;
    /** Taux de victoire */
    win_rate: number;
  }

  // === INTERFACES ÉVÉNEMENTS ===

  /**
   * Événement de changement d'état
   */
  interface StateChangeEvent {
    /** Type d'événement */
    type: string;
    /** Nouvelle valeur */
    value: any;
    /** Timestamp */
    timestamp: string;
  }

  /**
   * Événement de notification temps réel
   */
  interface NotificationEvent {
    /** Notification */
    notification: Notification;
    /** Timestamp de réception */
    received_at: string;
  }

  // === INTERFACES API TAURI ===

  /**
   * Réponse standard de l'API Tauri
   */
  interface TauriResponse<T = any> {
    /** Succès de l'opération */
    success: boolean;
    /** Données de réponse */
    data?: T;
    /** Message d'erreur */
    error?: string;
    /** Timestamp */
    timestamp: string;
  }

  /**
   * Paramètres de commande Tauri
   */
  interface TauriCommandParams {
    [key: string]: any;
  }

  // === UTILITAIRES TYPES ===

  /**
   * Type partiel récursif pour les mises à jour d'état
   */
  type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
  };

  /**
   * Type pour les handlers d'événements
   */
  type EventHandler<T = any> = (event: T) => void | Promise<void>;

  /**
   * Type pour les callbacks de mise à jour
   */
  type UpdateCallback<T = any> = (value: T) => void | Promise<void>;
}

// === EXTENSIONS GLOBALES ===

declare global {
  interface Window {
    /**
     * API Tauri exposée globalement
     */
    __TAURI__: {
      invoke: (cmd: string, args?: any) => Promise<any>;
      listen: (event: string, handler: (e: any) => void) => Promise<() => void>;
      emit: (event: string, payload?: any) => Promise<void>;
    };
  }
}

export = VoidBot;
export as namespace VoidBot;