use anyhow::Result;
use serenity::{
    model::application::CommandInteraction,
    prelude::*,
};
use crate::{BotData, stealth::StealthManager};

pub async fn handle_animate(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let animation_type = command
        .data
        .options
        .iter()
        .find(|opt| opt.name == "type")
        .and_then(|opt| opt.value.as_str())
        .unwrap_or("status");

    let content = match animation_type {
        "status" => "✨ **Status Animation Started**\n\nYour status will now cycle through different states.\nUse `/animate_stop` to stop the animation.",
        "bio" => "📝 **Bio Animation Started**\n\nYour bio will now cycle through different texts.\nUse `/animate_stop` to stop the animation.",
        "avatar" => "🖼️ **Avatar Animation Started**\n\nYour avatar will now cycle through different images.\nUse `/animate_stop` to stop the animation.",
        _ => "❌ Invalid animation type.",
    };

    stealth.send_response(ctx, command, content, false).await?;

    // TODO: Start actual animation in background
    
    Ok(())
}

pub async fn handle_animate_stop(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let content = "⏹️ **Animation Stopped**\n\nAll profile animations have been stopped.";

    stealth.send_response(ctx, command, content, false).await?;

    // TODO: Stop actual animations
    
    Ok(())
}