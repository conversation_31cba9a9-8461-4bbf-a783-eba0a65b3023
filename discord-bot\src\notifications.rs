use serenity::all::*;
use voidbot_shared::{VoidNotification, NotificationEventType, NotificationPriority, NotificationManager};
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::sync::Arc;
use anyhow::Result;
use tracing::{info, warn, error};
use crate::event_emitter::EventEmitter;

/// Gestionnaire des événements Discord pour le Notification Center
pub struct DiscordNotificationHandler {
    /// Gestionnaire des notifications
    notification_manager: Arc<RwLock<NotificationManager>>,
    /// Cache des messages récents pour détecter les ghostpings
    recent_messages: Arc<RwLock<HashMap<MessageId, CachedMessage>>>,
    /// ID utilisateur du selfbot
    user_id: UserId,
    /// Émetteur d'événements temps réel (optionnel)
    event_emitter: Option<Arc<EventEmitter>>,
}

/// Message mis en cache pour détecter les suppressions
#[derive(Debug, Clone)]
struct CachedMessage {
    content: String,
    author_id: UserId,
    channel_id: ChannelId,
    guild_id: Option<GuildId>,
    mentions: Vec<UserId>,
    timestamp: std::time::SystemTime,
}

impl DiscordNotificationHandler {
    /// Créer un nouveau gestionnaire de notifications Discord
    pub fn new(user_id: UserId) -> Self {
        Self {
            notification_manager: Arc::new(RwLock::new(NotificationManager::new())),
            recent_messages: Arc::new(RwLock::new(HashMap::new())),
            user_id,
            event_emitter: None,
        }
    }

    /// Créer un nouveau gestionnaire avec émetteur d'événements
    pub fn new_with_emitter(user_id: UserId, event_emitter: Arc<EventEmitter>) -> Self {
        Self {
            notification_manager: Arc::new(RwLock::new(NotificationManager::new())),
            recent_messages: Arc::new(RwLock::new(HashMap::new())),
            user_id,
            event_emitter: Some(event_emitter),
        }
    }

    /// Obtenir le gestionnaire de notifications
    pub fn get_notification_manager(&self) -> Arc<RwLock<NotificationManager>> {
        self.notification_manager.clone()
    }

    /// Gérer la réception d'un nouveau message
    pub async fn handle_message_create(&self, ctx: &Context, message: &Message) -> Result<()> {
        // Mettre en cache le message pour détecter les suppressions
        let cached_msg = CachedMessage {
            content: message.content.clone(),
            author_id: message.author.id,
            channel_id: message.channel_id,
            guild_id: message.guild_id,
            mentions: message.mentions.iter().map(|u| u.id).collect(),
            timestamp: std::time::SystemTime::now(),
        };

        {
            let mut cache = self.recent_messages.write().await;
            cache.insert(message.id, cached_msg);
            
            // Nettoyer les anciens messages (garde seulement les 1000 derniers)
            if cache.len() > 1000 {
                let mut entries: Vec<_> = cache.iter().collect();
                entries.sort_by_key(|(_, msg)| msg.timestamp);
                let to_remove = entries.into_iter().take(cache.len() - 900).map(|(id, _)| *id).collect::<Vec<_>>();
                for id in to_remove {
                    cache.remove(&id);
                }
            }
        }

        // Vérifier les détections de mots-clés
        self.check_keyword_detection(ctx, message).await?;

        // Vérifier les mentions
        if message.mentions.iter().any(|u| u.id == self.user_id) {
            self.handle_mention(ctx, message).await?;
        }

        // Vérifier les giveaways
        self.check_giveaway_detection(ctx, message).await?;

        Ok(())
    }

    /// Gérer la suppression d'un message (détection ghostping)
    pub async fn handle_message_delete(&self, ctx: &Context, channel_id: ChannelId, deleted_message_id: MessageId, guild_id: Option<GuildId>) -> Result<()> {
        let cached_message = {
            let cache = self.recent_messages.read().await;
            cache.get(&deleted_message_id).cloned()
        };

        if let Some(cached_msg) = cached_message {
            // Vérifier si le message supprimé contenait une mention de l'utilisateur
            if cached_msg.mentions.contains(&self.user_id) {
                let notification = self.create_ghostping_notification(ctx, &cached_msg).await?;

                // Émettre la notification en temps réel si possible
                if let Some(emitter) = &self.event_emitter {
                    if let Err(e) = emitter.emit_notification(notification.clone()).await {
                        warn!("Erreur lors de l'émission de notification ghostping: {}", e);
                    }
                }

                let mut manager = self.notification_manager.write().await;
                manager.add_notification(notification);

                info!("👻 Ghostping détecté de {} dans #{}", cached_msg.author_id, cached_msg.channel_id);
            }

            // Retirer le message du cache
            let mut cache = self.recent_messages.write().await;
            cache.remove(&deleted_message_id);
        }

        Ok(())
    }

    /// Gérer les changements de relation (ami retiré/bloqué)
    pub async fn handle_friend_removed(&self, ctx: &Context, user_id: UserId) -> Result<()> {
        let notification = VoidNotification::new(
            NotificationEventType::FriendRemoved,
            "Ami retiré".to_string(),
            format!("L'utilisateur {} vous a retiré de ses amis", user_id),
            NotificationPriority::Medium,
        ).with_user(user_id.to_string());

        // Émettre la notification en temps réel si possible
        if let Some(emitter) = &self.event_emitter {
            if let Err(e) = emitter.emit_notification(notification.clone()).await {
                warn!("Erreur lors de l'émission de notification ami retiré: {}", e);
            }
        }

        let mut manager = self.notification_manager.write().await;
        manager.add_notification(notification);

        Ok(())
    }

    /// Gérer le blocage par un ami
    pub async fn handle_friend_blocked(&self, ctx: &Context, user_id: UserId) -> Result<()> {
        let notification = VoidNotification::new(
            NotificationEventType::FriendBlocked,
            "Bloqué par un ami".to_string(),
            format!("L'utilisateur {} vous a bloqué", user_id),
            NotificationPriority::High,
        ).with_user(user_id.to_string());

        let mut manager = self.notification_manager.write().await;
        manager.add_notification(notification);

        Ok(())
    }

    /// Gérer l'expulsion/bannissement d'un serveur
    pub async fn handle_guild_ban_addition(&self, ctx: &Context, guild_id: GuildId, banned_user: User) -> Result<()> {
        if banned_user.id == self.user_id {
            let guild_name = match ctx.cache.guild(guild_id) {
                Some(guild) => {
                    info!("Récupération nom serveur réussie: {}", guild.name);
                    guild.name.clone()
                },
                None => {
                    warn!("Impossible de récupérer le nom du serveur {}: non trouvé dans le cache", guild_id);
                    format!("Serveur #{}", guild_id)
                }
            };

            let notification = VoidNotification::new(
                NotificationEventType::ServerBanned,
                "Banni d'un serveur".to_string(),
                format!("Vous avez été banni du serveur '{}'", guild_name),
                NotificationPriority::High,
            ).with_guild(guild_id.to_string());

            let mut manager = self.notification_manager.write().await;
            manager.add_notification(notification);

            warn!("🔨 Banni du serveur {} ({})", guild_name, guild_id);
        }

        Ok(())
    }

    /// Gérer les changements de rôle
    pub async fn handle_guild_member_update(&self, ctx: &Context, old: Option<Member>, new: Member) -> Result<()> {
        if new.user.id != self.user_id {
            return Ok(());
        }

        if let Some(old_member) = old {
            let old_roles: std::collections::HashSet<_> = old_member.roles.iter().collect();
            let new_roles: std::collections::HashSet<_> = new.roles.iter().collect();

            if old_roles != new_roles {
                let guild_name = match ctx.cache.guild(new.guild_id) {
                    Some(guild) => {
                        info!("Récupération nom serveur pour changement rôle: {}", guild.name);
                        guild.name.clone()
                    },
                    None => {
                        warn!("Impossible de récupérer le nom du serveur {} pour changement rôle: non trouvé dans le cache", new.guild_id);
                        format!("Serveur #{}", new.guild_id)
                    }
                };

                let added_roles: Vec<_> = new_roles.difference(&old_roles).collect();
                let removed_roles: Vec<_> = old_roles.difference(&new_roles).collect();

                let description = if !added_roles.is_empty() && !removed_roles.is_empty() {
                    format!("Rôles modifiés dans '{}' - Ajoutés: {}, Retirés: {}", 
                        guild_name, added_roles.len(), removed_roles.len())
                } else if !added_roles.is_empty() {
                    format!("Rôles ajoutés dans '{}': {}", guild_name, added_roles.len())
                } else {
                    format!("Rôles retirés dans '{}': {}", guild_name, removed_roles.len())
                };

                let notification = VoidNotification::new(
                    NotificationEventType::RoleChanged,
                    "Rôles modifiés".to_string(),
                    description,
                    NotificationPriority::Medium,
                ).with_guild(new.guild_id.to_string());

                let mut manager = self.notification_manager.write().await;
                manager.add_notification(notification);
            }
        }

        Ok(())
    }

    /// Créer une notification de ghostping
    async fn create_ghostping_notification(&self, ctx: &Context, cached_msg: &CachedMessage) -> Result<VoidNotification> {
        let channel_name = if let Ok(channel) = ctx.http.get_channel(cached_msg.channel_id).await {
            match channel {
                Channel::Guild(gc) => format!("#{}", gc.name),
                Channel::Private(_) => "DM".to_string(),
                _ => "Canal inconnu".to_string(),
            }
        } else {
            "Canal inconnu".to_string()
        };

        let author_name = if let Ok(user) = ctx.http.get_user(cached_msg.author_id).await {
            user.name
        } else {
            "Utilisateur inconnu".to_string()
        };

        let guild_name = if let Some(guild_id) = cached_msg.guild_id {
            match ctx.cache.guild(guild_id) {
                Some(guild) => {
                    info!("Récupération nom serveur pour ghostping: {}", guild.name);
                    guild.name.clone()
                },
                None => {
                    warn!("Impossible de récupérer le nom du serveur {} pour ghostping: non trouvé dans le cache", guild_id);
                    format!("Serveur #{}", guild_id)
                }
            }
        } else {
            "DM".to_string()
        };

        let notification = VoidNotification::new(
            NotificationEventType::Ghostping,
            "Ghostping détecté".to_string(),
            format!("{} vous a mentionné dans {} puis a supprimé son message", author_name, channel_name),
            NotificationPriority::High,
        )
        .with_user(cached_msg.author_id.to_string())
        .with_channel(cached_msg.channel_id.to_string())
        .with_metadata("original_content".to_string(), cached_msg.content.clone())
        .with_metadata("guild_name".to_string(), guild_name)
        .with_metadata("channel_name".to_string(), channel_name)
        .with_metadata("author_name".to_string(), author_name);

        Ok(notification)
    }

    /// Vérifier la détection de mots-clés
    async fn check_keyword_detection(&self, ctx: &Context, message: &Message) -> Result<()> {
        let keywords = {
            let manager = self.notification_manager.read().await;
            manager.config.keywords.clone()
        };

        let content_lower = message.content.to_lowercase();
        for keyword in keywords {
            if content_lower.contains(&keyword.to_lowercase()) {
                let channel_name = if let Ok(channel) = ctx.http.get_channel(message.channel_id).await {
                    match channel {
                        Channel::Guild(gc) => format!("#{}", gc.name),
                        _ => "DM".to_string(),
                    }
                } else {
                    "Canal inconnu".to_string()
                };

                let notification = VoidNotification::new(
                    NotificationEventType::KeywordDetected,
                    "Mot-clé détecté".to_string(),
                    format!("'{}' détecté dans {} par {}", keyword, channel_name, message.author.name),
                    NotificationPriority::Medium,
                )
                .with_user(message.author.id.to_string())
                .with_channel(message.channel_id.to_string())
                .with_metadata("keyword".to_string(), keyword.clone())
                .with_metadata("content".to_string(), message.content.clone());

                let mut manager = self.notification_manager.write().await;
                manager.add_notification(notification);

                info!("🔍 Mot-clé '{}' détecté dans {}", keyword, channel_name);
            }
        }

        Ok(())
    }

    /// Gérer les mentions
    async fn handle_mention(&self, ctx: &Context, message: &Message) -> Result<()> {
        let channel_name = if let Ok(channel) = ctx.http.get_channel(message.channel_id).await {
            match channel {
                Channel::Guild(gc) => format!("#{}", gc.name),
                _ => "DM".to_string(),
            }
        } else {
            "Canal inconnu".to_string()
        };

        let notification = VoidNotification::new(
            NotificationEventType::Mentioned,
            "Vous avez été mentionné".to_string(),
            format!("{} vous a mentionné dans {}", message.author.name, channel_name),
            NotificationPriority::Medium,
        )
        .with_user(message.author.id.to_string())
        .with_channel(message.channel_id.to_string())
        .with_metadata("content".to_string(), message.content.clone());

        let mut manager = self.notification_manager.write().await;
        manager.add_notification(notification);

        Ok(())
    }

    /// Vérifier la détection de giveaways
    async fn check_giveaway_detection(&self, ctx: &Context, message: &Message) -> Result<()> {
        let content_lower = message.content.to_lowercase();
        let giveaway_keywords = ["giveaway", "🎉", "concours", "cadeau"];

        if giveaway_keywords.iter().any(|&keyword| content_lower.contains(keyword)) {
            let channel_name = if let Ok(channel) = ctx.http.get_channel(message.channel_id).await {
                match channel {
                    Channel::Guild(gc) => format!("#{}", gc.name),
                    _ => "DM".to_string(),
                }
            } else {
                "Canal inconnu".to_string()
            };

            let notification = VoidNotification::new(
                NotificationEventType::GiveawayDetected,
                "Giveaway détecté".to_string(),
                format!("Nouveau giveaway dans {} par {}", channel_name, message.author.name),
                NotificationPriority::Low,
            )
            .with_user(message.author.id.to_string())
            .with_channel(message.channel_id.to_string())
            .with_metadata("content".to_string(), message.content.clone());

            let mut manager = self.notification_manager.write().await;
            manager.add_notification(notification);

            info!("🎁 Giveaway détecté dans {}", channel_name);
        }

        Ok(())
    }
}