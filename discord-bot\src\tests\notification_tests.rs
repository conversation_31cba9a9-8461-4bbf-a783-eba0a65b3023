use crate::notifications::DiscordNotificationHandler;
use crate::event_emitter::EventEmitter;
use super::test_utils::*;
use serenity::all::*;
use std::sync::Arc;
use tokio::time::{timeout, Duration};
use voidbot_shared::{IpcMessage, NotificationEventType};

#[tokio::test]
async fn test_notification_handler_creation() {
    let user_id = UserId::new(123456789);
    let handler = DiscordNotificationHandler::new(user_id);
    
    // Le handler doit être créé avec l'ID utilisateur correct
    // Note: Nous ne pouvons pas accéder directement aux champs privés,
    // mais nous pouvons tester le comportement
    assert!(true); // Test basique de création
}

#[tokio::test]
async fn test_notification_handler_with_emitter() {
    let user_id = UserId::new(123456789);
    let (emitter, _receiver) = create_test_event_emitter();
    
    let handler = DiscordNotificationHandler::new_with_emitter(user_id, Arc::new(emitter));
    
    // Le handler doit être créé avec l'émetteur d'événements
    assert!(true); // Test basique de création avec émetteur
}

// Note: Les tests suivants nécessiteraient des mocks plus sophistiqués
// pour le Context de Serenity. Pour l'instant, nous testons la logique
// de base et les intégrations.

#[tokio::test]
async fn test_ghostping_notification_creation() {
    // Test de la logique de création de notification ghostping
    // sans dépendance au Context complet
    
    let user_id = UserId::new(123456789);
    let handler = DiscordNotificationHandler::new(user_id);
    
    // Simuler un message avec mention
    let message = create_test_message();
    
    // Vérifier que le message contient bien une mention
    assert!(!message.mentions.is_empty());
    assert_eq!(message.mentions[0].id, user_id);
}

#[tokio::test]
async fn test_notification_priority_logic() {
    // Test de la logique de priorité des notifications
    use voidbot_shared::{VoidNotification, NotificationPriority};
    
    // Notification ghostping (haute priorité)
    let ghostping = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Ghostping détecté".to_string(),
        "Un utilisateur vous a mentionné puis a supprimé son message".to_string(),
        NotificationPriority::High,
    );
    
    assert_eq!(ghostping.priority, NotificationPriority::High);
    assert_eq!(ghostping.event_type, NotificationEventType::Ghostping);
    
    // Notification ami retiré (priorité moyenne)
    let friend_removed = VoidNotification::new(
        NotificationEventType::FriendRemoved,
        "Ami retiré".to_string(),
        "Un utilisateur vous a retiré de ses amis".to_string(),
        NotificationPriority::Medium,
    );
    
    assert_eq!(friend_removed.priority, NotificationPriority::Medium);
    assert_eq!(friend_removed.event_type, NotificationEventType::FriendRemoved);
}

#[tokio::test]
async fn test_notification_with_context() {
    // Test d'ajout de contexte aux notifications
    use voidbot_shared::VoidNotification;
    
    let mut notification = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Test".to_string(),
        "Test message".to_string(),
        voidbot_shared::NotificationPriority::High,
    );
    
    // Ajouter du contexte
    notification = notification
        .with_user("123456789".to_string())
        .with_guild("987654321".to_string())
        .with_channel("555666777".to_string());
    
    assert_eq!(notification.user_id, Some("123456789".to_string()));
    assert_eq!(notification.guild_id, Some("987654321".to_string()));
    assert_eq!(notification.channel_id, Some("555666777".to_string()));
}

#[tokio::test]
async fn test_notification_manager_integration() {
    // Test d'intégration avec le NotificationManager
    use voidbot_shared::{NotificationManager, VoidNotification};
    
    let mut manager = NotificationManager::new();
    
    let notification = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Test Notification".to_string(),
        "Test message content".to_string(),
        voidbot_shared::NotificationPriority::High,
    );
    
    // Ajouter la notification
    manager.add_notification(notification.clone());
    
    // Récupérer les notifications non lues
    let notifications = manager.get_unread_notifications();
    assert_eq!(notifications.len(), 1);
    assert_eq!(notifications[0].title, "Test Notification");
}

#[tokio::test]
async fn test_notification_filtering() {
    // Test de filtrage des notifications
    use voidbot_shared::{NotificationManager, VoidNotification, NotificationEventType};
    
    let mut manager = NotificationManager::new();
    
    // Ajouter différents types de notifications
    let ghostping = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Ghostping".to_string(),
        "Message".to_string(),
        voidbot_shared::NotificationPriority::High,
    );
    
    let friend_removed = VoidNotification::new(
        NotificationEventType::FriendRemoved,
        "Friend Removed".to_string(),
        "Message".to_string(),
        voidbot_shared::NotificationPriority::Medium,
    );
    
    manager.add_notification(ghostping);
    manager.add_notification(friend_removed);
    
    // Filtrer par type
    let ghostping_notifications = manager.get_notifications_by_type(&NotificationEventType::Ghostping);
    assert_eq!(ghostping_notifications.len(), 1);
    assert_eq!(ghostping_notifications[0].title, "Ghostping");

    let friend_notifications = manager.get_notifications_by_type(&NotificationEventType::FriendRemoved);
    assert_eq!(friend_notifications.len(), 1);
    assert_eq!(friend_notifications[0].title, "Friend Removed");
}

#[tokio::test]
async fn test_notification_cleanup() {
    // Test de nettoyage des anciennes notifications
    use voidbot_shared::{NotificationManager, VoidNotification};
    
    let mut manager = NotificationManager::new();
    
    // Ajouter plusieurs notifications
    for i in 0..15 {
        let notification = VoidNotification::new(
            NotificationEventType::Ghostping,
            format!("Notification {}", i),
            "Test message".to_string(),
            voidbot_shared::NotificationPriority::Low,
        );
        manager.add_notification(notification);
    }
    
    // Vérifier la limite de notifications
    let notifications = manager.get_unread_notifications();
    assert!(notifications.len() <= 15); // Toutes les notifications ajoutées
}

#[tokio::test]
async fn test_notification_serialization() {
    // Test de sérialisation/désérialisation des notifications
    use voidbot_shared::VoidNotification;
    
    let notification = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Test Notification".to_string(),
        "Test message content".to_string(),
        voidbot_shared::NotificationPriority::High,
    ).with_user("123456789".to_string());
    
    // Sérialiser en JSON
    let json = serde_json::to_string(&notification).unwrap();
    assert!(json.contains("Test Notification"));
    assert!(json.contains("Ghostping"));
    
    // Désérialiser depuis JSON
    let deserialized: VoidNotification = serde_json::from_str(&json).unwrap();
    assert_eq!(deserialized.title, notification.title);
    assert_eq!(deserialized.event_type, notification.event_type);
    assert_eq!(deserialized.user_id, notification.user_id);
}

#[tokio::test]
async fn test_notification_timestamp() {
    // Test de gestion des timestamps
    use voidbot_shared::VoidNotification;
    use chrono::Utc;
    
    let before = Utc::now();
    
    let notification = VoidNotification::new(
        NotificationEventType::Ghostping,
        "Test".to_string(),
        "Test".to_string(),
        voidbot_shared::NotificationPriority::Low,
    );
    
    let after = Utc::now();
    
    // Le timestamp doit être entre before et after
    assert!(notification.timestamp >= before);
    assert!(notification.timestamp <= after);
}
