# 🪟 Guide de Build Windows pour VoidBot v1.0

## 📋 Vue d'ensemble

Ce guide vous permet de compiler VoidBot nativement sur Windows pour générer les installeurs MSI et NSIS. La cross-compilation depuis WSL2/Linux n'étant pas supportée par Tauri, ce processus doit être exécuté sur un système Windows.

## 🎯 Objectif

Générer les installeurs Windows production-ready :
- **VoidBot-1.0.0-x64.msi** - Installeur MSI pour Windows 64-bit
- **VoidBot-1.0.0-x64-setup.exe** - Installeur NSIS pour Windows 64-bit  
- **VoidBot-1.0.0-x86.msi** - Installeur MSI pour Windows 32-bit (optionnel)
- **VoidBot-1.0.0-x86-setup.exe** - Installeur NSIS pour Windows 32-bit (optionnel)

---

## ⚙️ Prérequis Windows

### 🔧 Logiciels requis

#### 1. **Node.js** (Obligatoire)
- **Version** : 18.x ou supérieure (LTS recommandée)
- **Téléchargement** : https://nodejs.org
- **Installation** : Télécharger l'installeur Windows `.msi` et suivre l'assistant
- **Vérification** :
  ```cmd
  node --version
  npm --version
  ```

#### 2. **Rust** (Obligatoire)
- **Version** : 1.70+ stable
- **Téléchargement** : https://rustup.rs
- **Installation** : 
  - Télécharger `rustup-init.exe`
  - Exécuter et suivre les instructions
  - Redémarrer le terminal après installation
- **Vérification** :
  ```cmd
  rustc --version
  cargo --version
  rustup --version
  ```

#### 3. **Git** (Obligatoire)
- **Téléchargement** : https://git-scm.com
- **Installation** : Installeur Windows standard
- **Vérification** :
  ```cmd
  git --version
  ```

#### 4. **Python 3** (Optionnel)
- **Version** : 3.8+ pour la génération d'assets
- **Téléchargement** : https://python.org
- **Installation** : Cocher "Add Python to PATH"
- **Vérification** :
  ```cmd
  python --version
  ```

#### 5. **Visual Studio Build Tools** (Automatique)
- Rust installe automatiquement les outils de build Microsoft
- Pas d'action manuelle requise normalement

### 📁 Structure de fichiers

```
C:\VoidBot\                    # Dossier racine du projet
├── voidbot-desktop\           # Application Tauri principal
├── scripts\                   # Scripts de build automatisés
│   ├── build-windows.bat      # Script de build Batch
│   ├── build-windows.ps1      # Script de build PowerShell
│   └── check-windows-env.bat  # Vérification environnement
├── builds\                    # Dossier de sortie (créé automatiquement)
│   └── windows\               # Installeurs Windows
└── shared\                    # Code partagé
```

---

## 🚀 Build automatique (Recommandé)

### **Option 1 : Script Batch (.bat)**

#### **Étapes :**
1. **Ouvrir Invite de commandes** (cmd) en tant qu'Administrateur
2. **Naviguer vers le projet** :
   ```cmd
   cd C:\chemin\vers\VoidBot
   ```
3. **Vérifier l'environnement** (optionnel) :
   ```cmd
   scripts\check-windows-env.bat
   ```
4. **Lancer le build** :
   ```cmd
   scripts\build-windows.bat
   ```

#### **Que fait le script :**
- ✅ Vérifie Node.js, npm, Rust, Cargo
- ✅ Installe les dépendances npm si nécessaire
- ✅ Configure les cibles Windows Rust (x64 + x86)
- ✅ Génère les assets avec Python (si disponible)
- ✅ Build le frontend React en mode production
- ✅ Compile les installeurs MSI et NSIS pour x64
- ✅ Compile les installeurs MSI et NSIS pour x86 (optionnel)
- ✅ Copie tous les installeurs dans `builds\windows\`
- ✅ Affiche un résumé avec les fichiers générés

### **Option 2 : Script PowerShell (.ps1)**

#### **Étapes :**
1. **Ouvrir PowerShell** en tant qu'Administrateur
2. **Autoriser l'exécution de scripts** (première fois uniquement) :
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
3. **Naviguer vers le projet** :
   ```powershell
   cd C:\chemin\vers\VoidBot
   ```
4. **Lancer le build** :
   ```powershell
   .\scripts\build-windows.ps1
   ```

#### **Options avancées PowerShell :**
```powershell
# Build sans x86 (plus rapide)
.\scripts\build-windows.ps1 -SkipX86

# Build avec nettoyage complet
.\scripts\build-windows.ps1 -Clean

# Build avec logs détaillés
.\scripts\build-windows.ps1 -Verbose

# Combinaison d'options
.\scripts\build-windows.ps1 -SkipX86 -Clean -Verbose
```

---

## 🔧 Build manuel (Avancé)

### **Étape 1 : Préparation**
```cmd
cd C:\chemin\vers\VoidBot\voidbot-desktop

# Installer les dépendances
npm install

# Installer les cibles Rust
rustup target add x86_64-pc-windows-msvc
rustup target add i686-pc-windows-msvc
```

### **Étape 2 : Build frontend**
```cmd
# Build React en mode production
npm run build:prod
```

### **Étape 3 : Génération assets (optionnel)**
```cmd
cd src-tauri
python build-assets.py
cd ..
```

### **Étape 4 : Build installeurs**

#### **Windows 64-bit :**
```cmd
npx tauri build --target x86_64-pc-windows-msvc --bundles msi,nsis
```

#### **Windows 32-bit :**
```cmd
npx tauri build --target i686-pc-windows-msvc --bundles msi,nsis
```

### **Étape 5 : Récupération des fichiers**
Les installeurs sont générés dans :
```
src-tauri\target\x86_64-pc-windows-msvc\release\bundle\msi\
src-tauri\target\x86_64-pc-windows-msvc\release\bundle\nsis\
src-tauri\target\i686-pc-windows-msvc\release\bundle\msi\
src-tauri\target\i686-pc-windows-msvc\release\bundle\nsis\
```

---

## ⏱️ Temps de compilation

### **Estimations typiques :**
- **Premier build** : 15-30 minutes (téléchargement dépendances)
- **Builds suivants** : 5-15 minutes (cache utilisé)
- **Build x64 uniquement** : 5-10 minutes
- **Build x64 + x86** : 10-20 minutes

### **Facteurs d'impact :**
- **CPU** : Plus de cœurs = compilation plus rapide
- **RAM** : 8GB minimum, 16GB recommandé
- **Stockage** : SSD fortement recommandé
- **Internet** : Première compilation télécharge ~2GB

---

## 📦 Fichiers de sortie

### **Installeurs générés :**

#### **Format MSI (Windows Installer)** 
- **Fichier** : `VoidBot_1.0.0_x64_en-US.msi`
- **Taille** : ~50-80 MB
- **Usage** : Installation silencieuse, déploiement entreprise
- **Caractéristiques** :
  - Installation dans Program Files
  - Entrées de registre Windows
  - Désinstallation via Panneau de configuration
  - Support GPO et déploiement automatisé

#### **Format NSIS (Nullsoft Installer)**
- **Fichier** : `VoidBot_1.0.0_x64-setup.exe`
- **Taille** : ~50-80 MB  
- **Usage** : Installation utilisateur final, distribution web
- **Caractéristiques** :
  - Interface d'installation personnalisée
  - Options d'installation avancées
  - Création raccourcis Bureau/Menu Démarrer
  - Association de fichiers automatique

### **Structure finale :**
```
builds\windows\
├── VoidBot_1.0.0_x64_en-US.msi           # MSI 64-bit
├── VoidBot_1.0.0_x64-setup.exe           # NSIS 64-bit
├── VoidBot_1.0.0_x86_en-US.msi           # MSI 32-bit (optionnel)
└── VoidBot_1.0.0_x86-setup.exe           # NSIS 32-bit (optionnel)
```

---

## 🐛 Dépannage

### **Erreurs courantes**

#### **1. "Node.js non trouvé"**
```
Solution : Installer Node.js depuis https://nodejs.org
Vérifier : node --version
```

#### **2. "Rust non installé"**
```
Solution : Installer Rust depuis https://rustup.rs
Vérifier : cargo --version
```

#### **3. "Erreur compilation Rust"**
```
Cause : Dépendances ou cibles manquantes
Solution :
  rustup update
  rustup target add x86_64-pc-windows-msvc
  cargo clean
```

#### **4. "Erreur build npm"**
```
Cause : Dépendances Node.js corrompues
Solution :
  rm -rf node_modules
  npm install
```

#### **5. "Erreur linking Visual Studio"**
```
Cause : Outils de build Microsoft manquants
Solution :
  rustup toolchain install stable-x86_64-pc-windows-msvc
  rustup default stable-x86_64-pc-windows-msvc
```

#### **6. "Espace disque insuffisant"**
```
Cause : Build Rust nécessite 5-10 GB d'espace
Solution : Libérer de l'espace et nettoyer :
  cargo clean
  cd voidbot-desktop && rm -rf node_modules dist
```

### **Logs et diagnostic**

#### **Vérifier l'environnement :**
```cmd
scripts\check-windows-env.bat
```

#### **Logs de build :**
```cmd
# Logs automatiques
type build-windows.log

# Logs Tauri détaillés
set RUST_LOG=debug
npx tauri build --verbose
```

#### **Nettoyage complet :**
```cmd
# PowerShell avec nettoyage
.\scripts\build-windows.ps1 -Clean

# Ou manuellement :
cd voidbot-desktop
rm -rf src-tauri\target
rm -rf node_modules
rm -rf dist
cargo clean
npm install
```

---

## 🔒 Sécurité et signatures

### **Code signing (Production)**
Pour la distribution publique, les installeurs doivent être signés :

1. **Obtenir un certificat** de signature de code
2. **Configurer Tauri** avec le certificat :
   ```json
   // tauri.conf.json
   "windows": {
     "certificateThumbprint": "YOUR_CERT_THUMBPRINT",
     "digestAlgorithm": "sha256",
     "timestampUrl": "http://timestamp.sectigo.com"
   }
   ```
3. **Build avec signature** :
   ```cmd
   npx tauri build --bundles msi,nsis
   ```

### **Vérification intégrité**
```cmd
# Générer checksums SHA256
certutil -hashfile VoidBot_1.0.0_x64-setup.exe SHA256
certutil -hashfile VoidBot_1.0.0_x64_en-US.msi SHA256
```

---

## 🚀 Distribution et déploiement

### **1. GitHub Releases**
```yaml
# .github/workflows/release.yml
- name: Build Windows
  run: |
    .\scripts\build-windows.ps1 -SkipX86
- name: Upload Release Assets
  uses: actions/upload-release-asset@v1
  with:
    asset_path: builds/windows/
```

### **2. Auto-updater**
Les installeurs supportent automatiquement l'auto-updater intégré de VoidBot :
- Vérification automatique des mises à jour
- Téléchargement en arrière-plan
- Installation silencieuse
- Redémarrage de l'application

### **3. Distribution web**
- **Site officiel** : Téléchargement direct depuis voidbot.com
- **CDN** : Distribution via CDN global pour performance
- **Détection OS** : Sélection automatique x64/x86

---

## 📊 Métriques et monitoring

### **Tailles typiques :**
- **Executable principal** : ~15-25 MB
- **Installeur compressé** : ~50-80 MB
- **Installation complète** : ~150-200 MB

### **Performance :**
- **Démarrage** : <3 secondes
- **Utilisation RAM** : 80-150 MB
- **CPU** : <5% en idle, 10-30% en utilisation

---

## ✅ Checklist finale

### **Avant build :**
- [ ] Node.js 18+ installé et fonctionnel
- [ ] Rust stable installé et à jour
- [ ] Git installé pour récupération du code
- [ ] 10 GB d'espace disque libre minimum
- [ ] Connexion internet stable (première fois)

### **Après build :**
- [ ] Installeurs générés dans `builds\windows\`
- [ ] Taille des fichiers cohérente (50-80 MB)
- [ ] Test d'installation sur machine propre
- [ ] Vérification fonctionnalités principales
- [ ] Génération checksums SHA256

### **Distribution :**
- [ ] Signature de code (si production)
- [ ] Upload vers GitHub Releases
- [ ] Mise à jour site web de téléchargement
- [ ] Test auto-updater fonctionnel
- [ ] Annonce communauté

---

## 🆘 Support

### **En cas de problème :**
1. **Vérifier** l'environnement avec `check-windows-env.bat`
2. **Consulter** les logs dans `build-windows.log`
3. **Nettoyer** et recommencer avec `-Clean`
4. **Rechercher** l'erreur dans la documentation Tauri
5. **Demander** de l'aide sur GitHub Issues

### **Ressources utiles :**
- **Documentation Tauri** : https://tauri.app/v1/guides/building/windows
- **Issues VoidBot** : https://github.com/VoidBot/issues
- **Communauté Discord** : lien dans le README

---

**🎉 Vous êtes maintenant prêt à compiler VoidBot nativement sur Windows et générer des installeurs production-ready !**