# 🔐 Configuration OAuth Discord pour VoidBot

## 🎯 **Problème actuel**
VoidBot affiche "Erreur lors de l'initialisation OAuth" car aucun Client ID Discord n'est configuré.

## ⚡ **Solution rapide (5 minutes)**

### **Étape 1 : Créer une application Discord**
1. Va sur https://discord.com/developers/applications
2. Clique sur **"New Application"**
3. Nomme ton application : **"VoidBot"**
4. Clique **"Create"**

### **Étape 2 : Configurer OAuth**
1. Dans ton application, va dans **"OAuth2" → "General"**
2. **Copie le Client ID** (nombre à 18 chiffres)
3. Dans **"Redirects"**, ajoute : `http://localhost:3000/oauth/callback`
4. **Sauvegarde** les changements

### **Étape 3 : Configurer VoidBot**
Dans ton terminal WSL2, exécute :

```bash
# Définir le Client ID Discord (remplace par ton vrai Client ID)
export DISCORD_CLIENT_ID="TON_CLIENT_ID_ICI"

# Vérifier que c'est défini
echo $DISCORD_CLIENT_ID

# Relancer VoidBot
./start_voidbot.sh
```

### **Étape 4 : Tester OAuth**
1. **Interface VoidBot** s'ouvre
2. **Clique sur "Se connecter avec Discord"**
3. **Page Discord officielle** s'ouvre
4. **Autorise VoidBot**
5. **Retour automatique** vers VoidBot
6. **Dashboard** avec toutes les fonctionnalités !

---

## 🛡️ **Configuration permanente (optionnel)**

Pour éviter de refaire `export` à chaque fois :

```bash
# Ajouter au fichier .bashrc
echo 'export DISCORD_CLIENT_ID="TON_CLIENT_ID_ICI"' >> ~/.bashrc
source ~/.bashrc
```

---

## 🔍 **Vérification**

Si OAuth fonctionne, tu verras :
- ✅ **Bouton OAuth** cliquable
- ✅ **Page Discord** s'ouvre
- ✅ **Connexion réussie**
- ✅ **Dashboard VoidBot** accessible

Si ça ne fonctionne pas :
- ❌ Message "OAuth non configuré"
- ❌ Client ID manquant ou invalide

---

## 📋 **Scopes OAuth configurés**

VoidBot demande ces permissions Discord :
- `identify` - Informations utilisateur de base
- `guilds` - Liste des serveurs
- `guilds.members.read` - Infos membres serveurs
- `messages.read` - Lecture messages
- `applications.commands` - Commandes slash

**Ces permissions sont nécessaires** pour toutes les fonctionnalités VoidBot.

---

## 🚀 **Une fois configuré**

OAuth Discord te permet d'utiliser **toutes les fonctionnalités VoidBot** :
- ✅ **Interface sécurisée** sans token manuel
- ✅ **Authentification officielle** Discord
- ✅ **Session persistante** avec auto-reconnexion
- ✅ **Respect total** des conditions Discord ToS

**Ton clone de Nighty sera 100% opérationnel !** 🎯