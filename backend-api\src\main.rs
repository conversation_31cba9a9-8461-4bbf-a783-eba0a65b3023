// VoidBot Backend API
// API REST pour VoidBot avec Axum et PostgreSQL

use axum::{
    routing::get,
    Router,
    response::Json,
};
use serde_json::{json, Value};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing::info;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialiser le logger
    tracing_subscriber::fmt::init();
    
    // Charger les variables d'environnement
    dotenv::dotenv().ok();
    
    info!("🚀 Démarrage de l'API VoidBot Backend");
    
    // Créer l'application Axum
    let app = Router::new()
        .route("/", get(health_check))
        .route("/api/health", get(health_check))
        .route("/api/stats", get(get_stats))
        .layer(CorsLayer::permissive());
    
    // Configurer l'adresse d'écoute
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "3000".to_string())
        .parse::<u16>()?;
    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    
    info!("🌐 API VoidBot Backend démarrée sur http://{}", addr);
    
    // Démarrer le serveur
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}

/// Endpoint de santé de l'API
async fn health_check() -> Json<Value> {
    Json(json!({
        "status": "healthy",
        "service": "VoidBot Backend API",
        "version": "1.0.0",
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

/// Endpoint de statistiques
async fn get_stats() -> Json<Value> {
    Json(json!({
        "users": 0,
        "servers": 0,
        "commands_executed": 0,
        "uptime": "0s"
    }))
}