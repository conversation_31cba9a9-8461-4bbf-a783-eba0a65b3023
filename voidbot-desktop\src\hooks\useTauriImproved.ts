import { listen } from '@tauri-apps/api/event';
import { useEffect, useState, useCallback } from 'react';
import { ErrorHandler, VoidError, ValidationRule } from '../utils/errorHandler';

/**
 * Hook amélioré pour les commandes Tauri avec gestion d'erreur robuste
 */
export function useTauriCommand<T>(
  command: string, 
  args?: any, 
  options?: {
    retryAttempts?: number;
    autoExecute?: boolean;
    validation?: ValidationRule[];
  }
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(options?.autoExecute !== false);
  const [error, setError] = useState<VoidError | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const execute = useCallback(async (newArgs?: any, skipValidation = false) => {
    const executeArgs = newArgs || args;
    
    // Validation des arguments si spécifiée
    if (!skipValidation && options?.validation && executeArgs) {
      const validationError = ErrorHandler.validateInput(executeArgs, options.validation);
      if (validationError) {
        setError(validationError);
        setLoading(false);
        return { error: validationError };
      }
    }

    setLoading(true);
    setError(null);

    const result = await ErrorHandler.invokeTauriSafe<T>(
      command, 
      executeArgs, 
      `useTauriCommand:${command}`,
      options?.retryAttempts || 0
    );

    if (result.error) {
      setError(result.error);
      setData(null);
      setRetryCount(prev => prev + 1);
    } else {
      setData(result.data || null);
      setError(null);
      setRetryCount(0);
    }
    
    setLoading(false);
    return result;
  }, [command, args, options]);

  const retry = useCallback(() => {
    execute(args, true); // Skip validation on retry
  }, [execute, args]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
    setRetryCount(0);
  }, []);

  useEffect(() => {
    if (options?.autoExecute !== false) {
      execute();
    }
  }, [command, JSON.stringify(args)]);

  return { 
    data, 
    loading, 
    error, 
    execute, 
    retry,
    reset,
    refetch: execute,
    retryCount,
    canRetry: error?.retryable === true
  };
}

/**
 * Hook amélioré pour la gestion d'état Tauri avec validation
 */
export function useTauriState<T>(
  getCommand: string,
  setCommand: string,
  initialValue?: T,
  options?: {
    validation?: ValidationRule[];
    retryAttempts?: number;
  }
) {
  const [value, setValue] = useState<T | undefined>(initialValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<VoidError | null>(null);

  const updateValue = async (newValue: T) => {
    // Validation de la nouvelle valeur si spécifiée
    if (options?.validation) {
      const validationError = ErrorHandler.validateInput(newValue, options.validation);
      if (validationError) {
        setError(validationError);
        throw new Error(validationError.userMessage);
      }
    }

    const result = await ErrorHandler.invokeTauriSafe(
      setCommand, 
      { value: newValue },
      `useTauriState:${setCommand}`,
      options?.retryAttempts || 0
    );

    if (result.error) {
      setError(result.error);
      throw new Error(result.error.userMessage);
    }

    setValue(newValue);
    setError(null);
  };

  const refresh = async () => {
    setLoading(true);
    setError(null);
    
    const result = await ErrorHandler.invokeTauriSafe<T>(
      getCommand,
      undefined,
      `useTauriState:${getCommand}`,
      options?.retryAttempts || 0
    );

    if (result.error) {
      setError(result.error);
    } else {
      setValue(result.data);
    }
    
    setLoading(false);
  };

  const retry = useCallback(() => {
    refresh();
  }, [refresh]);

  useEffect(() => {
    refresh();
  }, [getCommand]);

  return {
    value,
    setValue: updateValue,
    loading,
    error,
    refresh,
    retry,
    canRetry: error?.retryable === true
  };
}

/**
 * Hook pour écouter les événements Tauri avec gestion d'erreur
 */
export function useTauriEvent<T>(
  event: string, 
  callback: (payload: T) => void,
  options?: {
    autoStart?: boolean;
  }
) {
  const [isListening, setIsListening] = useState(false);
  const [error, setError] = useState<VoidError | null>(null);

  const startListening = useCallback(async () => {
    try {
      setError(null);
      const unlisten = await listen<T>(event, (event) => {
        callback(event.payload);
      });
      
      setIsListening(true);
      
      return () => {
        unlisten();
        setIsListening(false);
      };
    } catch (err) {
      const voidError = ErrorHandler.handleTauriError(err, `useTauriEvent:${event}`);
      setError(voidError);
      return () => {};
    }
  }, [event, callback]);

  const stopListening = useCallback(() => {
    setIsListening(false);
  }, []);

  useEffect(() => {
    if (options?.autoStart !== false) {
      const cleanup = startListening();
      return () => {
        cleanup.then(fn => fn());
      };
    }
  }, [event, callback, options?.autoStart]);

  return {
    isListening,
    error,
    startListening,
    stopListening
  };
}

/**
 * Hook pour operations asynchrones avec retry automatique
 */
export function useAsyncOperation<T>() {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<VoidError | null>(null);

  const execute = useCallback(async (
    operation: () => Promise<T>,
    context?: string,
    retryAttempts: number = 0
  ) => {
    setLoading(true);
    setError(null);

    let lastError: VoidError | undefined;
    
    for (let attempt = 0; attempt <= retryAttempts; attempt++) {
      try {
        const result = await operation();
        setData(result);
        setError(null);
        setLoading(false);
        return { data: result };
      } catch (err) {
        lastError = ErrorHandler.handleTauriError(err, context);
        
        if (!lastError.retryable || attempt === retryAttempts) {
          break;
        }
        
        // Délai exponentiel entre les tentatives
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    setError(lastError!);
    setData(null);
    setLoading(false);
    return { error: lastError! };
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset,
    canRetry: error?.retryable === true
  };
}