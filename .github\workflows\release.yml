name: 🚀 Release VoidBot

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        type: string

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # Job de création de la release GitHub
  create-release:
    runs-on: ubuntu-latest
    outputs:
      release_id: ${{ steps.create-release.outputs.result }}
      version: ${{ steps.version.outputs.version }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Create release
        id: create-release
        uses: actions/github-script@v7
        with:
          script: |
            const { data } = await github.rest.repos.createRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag_name: '${{ steps.version.outputs.version }}',
              name: 'VoidBot ${{ steps.version.outputs.version }}',
              body: `## 🎉 VoidBot ${{ steps.version.outputs.version }}
              
              ### 📦 Installeurs disponibles
              - **Windows x64** : \`VoidBot_${{ steps.version.outputs.version }}_x64-setup.exe\` (NSIS)
              - **Windows x64** : \`VoidBot_${{ steps.version.outputs.version }}_x64_en-US.msi\` (MSI)
              - **Linux x64** : \`VoidBot_${{ steps.version.outputs.version }}_amd64.deb\` (DEB)
              - **Linux** : \`VoidBot_${{ steps.version.outputs.version }}_amd64.AppImage\` (AppImage)
              - **macOS** : \`VoidBot_${{ steps.version.outputs.version }}_universal.dmg\` (DMG)
              
              ### ✨ Nouveautés
              - 🤖 Selfbot Discord avec +100 commandes
              - 👻 Mode furtif Normal/Fantôme
              - 🚀 Snipers ultra-performants (Giveaway + Nitro)
              - 🎨 Interface moderne Tauri + React
              - 🔒 Sécurité maximale (100% local, chiffrement AES-256)
              - 🔄 Auto-updater intégré
              
              ### 🔧 Installation
              1. Télécharger l'installeur pour votre OS
              2. Exécuter et suivre l'assistant
              3. Lancer VoidBot et se connecter via Discord
              
              ### 📖 Documentation
              - [Guide de démarrage](https://github.com/${{ github.repository }}/blob/main/README.md)
              - [Documentation complète](https://voidbot.com/docs)
              - [Support Discord](https://voidbot.com/discord)
              
              **⚠️ Important** : VoidBot est un selfbot utilisant votre token Discord personnel. Utilisez de manière responsable et respectez les ToS Discord.`,
              draft: false,
              prerelease: false
            });
            return data.id

  # Job de build Windows
  build-windows:
    needs: create-release
    runs-on: windows-latest
    strategy:
      matrix:
        target: [x86_64-pc-windows-msvc]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: voidbot-desktop/package-lock.json

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: |
            voidbot-desktop/src-tauri -> target

      - name: Install dependencies
        run: |
          cd voidbot-desktop
          npm ci

      - name: Generate assets
        run: |
          cd voidbot-desktop/src-tauri
          python build-assets-simple.py
        continue-on-error: true

      - name: Build frontend
        run: |
          cd voidbot-desktop
          npm run build:prod

      - name: Build Tauri app
        run: |
          cd voidbot-desktop
          npm run tauri build -- --target ${{ matrix.target }} --bundles msi,nsis

      - name: Upload MSI to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/msi/VoidBot_*.msi
          asset_name: VoidBot_${{ needs.create-release.outputs.version }}_x64_en-US.msi
          asset_content_type: application/octet-stream

      - name: Upload NSIS to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/nsis/VoidBot_*-setup.exe
          asset_name: VoidBot_${{ needs.create-release.outputs.version }}_x64-setup.exe
          asset_content_type: application/octet-stream

  # Job de build Linux
  build-linux:
    needs: create-release
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target: [x86_64-unknown-linux-gnu]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: voidbot-desktop/package-lock.json

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libgtk-3-dev \
            libwebkit2gtk-4.0-dev \
            libappindicator3-dev \
            librsvg2-dev \
            patchelf

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: |
            voidbot-desktop/src-tauri -> target

      - name: Install dependencies
        run: |
          cd voidbot-desktop
          npm ci

      - name: Generate assets
        run: |
          cd voidbot-desktop/src-tauri
          python3 build-assets-simple.py
        continue-on-error: true

      - name: Build frontend
        run: |
          cd voidbot-desktop
          npm run build:prod

      - name: Build Tauri app
        run: |
          cd voidbot-desktop
          npm run tauri build -- --target ${{ matrix.target }} --bundles deb,appimage

      - name: Upload DEB to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/deb/voidbot_*.deb
          asset_name: VoidBot_${{ needs.create-release.outputs.version }}_amd64.deb
          asset_content_type: application/octet-stream

      - name: Upload AppImage to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle/appimage/voidbot_*.AppImage
          asset_name: VoidBot_${{ needs.create-release.outputs.version }}_amd64.AppImage
          asset_content_type: application/octet-stream

  # Job de build macOS
  build-macos:
    needs: create-release
    runs-on: macos-latest
    strategy:
      matrix:
        target: [universal-apple-darwin]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: voidbot-desktop/package-lock.json

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: aarch64-apple-darwin,x86_64-apple-darwin

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: |
            voidbot-desktop/src-tauri -> target

      - name: Install dependencies
        run: |
          cd voidbot-desktop
          npm ci

      - name: Generate assets
        run: |
          cd voidbot-desktop/src-tauri
          python3 build-assets-simple.py
        continue-on-error: true

      - name: Build frontend
        run: |
          cd voidbot-desktop
          npm run build:prod

      - name: Build Tauri app
        run: |
          cd voidbot-desktop
          npm run tauri build -- --target ${{ matrix.target }} --bundles dmg

      - name: Upload DMG to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: voidbot-desktop/src-tauri/target/universal-apple-darwin/release/bundle/dmg/VoidBot_*.dmg
          asset_name: VoidBot_${{ needs.create-release.outputs.version }}_universal.dmg
          asset_content_type: application/octet-stream

  # Job de notification Discord (optionnel)
  notify-discord:
    needs: [create-release, build-windows, build-linux, build-macos]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Discord notification
        if: success()
        run: |
          echo "🎉 VoidBot ${{ needs.create-release.outputs.version }} released successfully!"
          echo "All installers have been built and uploaded to GitHub Releases."