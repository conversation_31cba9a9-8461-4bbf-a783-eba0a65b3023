; VoidBot NSIS Installer Template
; Ultimate Discord Toolkit

!define PRODUCT_NAME "VoidBot"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "VoidBot Team"
!define PRODUCT_WEB_SITE "https://voidbot.app"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\voidbot.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

; Inclure Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"

; Configuration générale
Name "${PRODUCT_NAME}"
OutFile "VoidBot-Setup.exe"
InstallDir "$PROGRAMFILES64\${PRODUCT_NAME}"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

; Interface utilisateur moderne
!define MUI_ABORTWARNING
!define MUI_ICON "icons\icon.ico"
!define MUI_UNICON "icons\icon.ico"

; Images personnalisées
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "assets\header-image.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "assets\sidebar-image.bmp"

; Pages de l'assistant
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "..\..\LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES

; Page de fin avec options
!define MUI_FINISHPAGE_RUN "$INSTDIR\voidbot.exe"
!define MUI_FINISHPAGE_RUN_TEXT "Lancer VoidBot maintenant"
!define MUI_FINISHPAGE_LINK "Visiter le site web de VoidBot"
!define MUI_FINISHPAGE_LINK_LOCATION "${PRODUCT_WEB_SITE}"
!insertmacro MUI_PAGE_FINISH

; Page de désinstallation
!insertmacro MUI_UNPAGE_INSTFILES

; Langues supportées
!insertmacro MUI_LANGUAGE "French"
!insertmacro MUI_LANGUAGE "English"

; Fonction d'initialisation
Function .onInit
  ; Vérifier si déjà installé
  ReadRegStr $R0 HKLM "${PRODUCT_UNINST_KEY}" "UninstallString"
  StrCmp $R0 "" done
  
  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
  "VoidBot est déjà installé. $\n$\nCliquez sur OK pour supprimer la version précédente ou Annuler pour arrêter l'installation." \
  IDOK uninst
  Abort
  
  uninst:
    ClearErrors
    ExecWait '$R0 _?=$INSTDIR'
    
  done:
FunctionEnd

; Section d'installation principale
Section "VoidBot" SEC01
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  ; Fichiers principaux (à remplacer par Tauri)
  File "{{app_executable_path}}"
  File /r "{{resources_path}}"
  
  ; Créer les raccourcis
  CreateDirectory "$SMPROGRAMS\VoidBot"
  CreateShortCut "$SMPROGRAMS\VoidBot\VoidBot.lnk" "$INSTDIR\voidbot.exe"
  CreateShortCut "$SMPROGRAMS\VoidBot\Désinstaller VoidBot.lnk" "$INSTDIR\uninst.exe"
  CreateShortCut "$DESKTOP\VoidBot.lnk" "$INSTDIR\voidbot.exe"
  
  ; Ajouter au menu Démarrer
  CreateShortCut "$STARTMENU\VoidBot.lnk" "$INSTDIR\voidbot.exe"
SectionEnd

; Section registre
Section -AdditionalIcons
  WriteIniStr "$INSTDIR\${PRODUCT_NAME}.url" "InternetShortcut" "URL" "${PRODUCT_WEB_SITE}"
  CreateShortCut "$SMPROGRAMS\VoidBot\Site Web.lnk" "$INSTDIR\${PRODUCT_NAME}.url"
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\voidbot.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\voidbot.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

; Fonction de désinstallation
Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "$(^Name) a été désinstallé avec succès de votre ordinateur."
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Êtes-vous sûr de vouloir désinstaller complètement $(^Name) et tous ses composants ?" IDYES +2
  Abort
FunctionEnd

Section Uninstall
  Delete "$INSTDIR\${PRODUCT_NAME}.url"
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\voidbot.exe"
  
  Delete "$SMPROGRAMS\VoidBot\VoidBot.lnk"
  Delete "$SMPROGRAMS\VoidBot\Désinstaller VoidBot.lnk"
  Delete "$SMPROGRAMS\VoidBot\Site Web.lnk"
  Delete "$DESKTOP\VoidBot.lnk"
  Delete "$STARTMENU\VoidBot.lnk"
  
  RMDir "$SMPROGRAMS\VoidBot"
  RMDir /r "$INSTDIR"
  
  DeleteRegKey HKLM "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  SetAutoClose true
SectionEnd