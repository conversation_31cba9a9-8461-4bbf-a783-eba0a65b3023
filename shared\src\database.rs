use sqlx::{sqlite::{SqlitePool, SqlitePoolOptions}, Row};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use anyhow::Result;
use std::path::PathBuf;

/// Configuration de la base de données
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// Chemin vers le fichier SQLite
    pub database_path: PathBuf,
    /// Nombre maximum de connexions
    pub max_connections: u32,
    /// Timeout de connexion en secondes
    pub connection_timeout: u64,
    /// Activer les logs SQL
    pub enable_sql_logging: bool,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        // Utiliser le dossier %APPDATA%\VoidBot\secure pour la base de données
        let db_path = if let Ok(appdata) = std::env::var("APPDATA") {
            let mut path = PathBuf::from(appdata);
            path.push("VoidBot");
            path.push("secure");
            path.push("voidbot.db");
            path
        } else {
            // Fallback vers le dossier local si APPDATA n'est pas disponible
            PathBuf::from("voidbot.db")
        };
        
        Self {
            database_path: db_path,
            max_connections: 5,
            connection_timeout: 30,
            enable_sql_logging: false,
        }
    }
}

/// Gestionnaire principal de la base de données
pub struct Database {
    pool: SqlitePool,
}

impl Database {
    /// Créer une nouvelle instance de la base de données
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        // Créer le dossier si nécessaire
        if let Some(parent) = config.database_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // Créer la connexion SQLite
        let database_url = format!("sqlite:{}", config.database_path.display());
        
        let pool = SqlitePoolOptions::new()
            .max_connections(config.max_connections)
            .acquire_timeout(std::time::Duration::from_secs(config.connection_timeout))
            .connect(&database_url)
            .await?;

        let db = Self { pool };
        
        // Initialiser les tables
        db.initialize_tables().await?;
        
        Ok(db)
    }

    /// Initialiser toutes les tables
    async fn initialize_tables(&self) -> Result<()> {
        // Table des configurations
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS configurations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(category, key)
            )
        "#).execute(&self.pool).await?;

        // Table des statistiques d'utilisation
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS usage_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feature TEXT NOT NULL,
                action TEXT NOT NULL,
                count INTEGER DEFAULT 1,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT,
                UNIQUE(feature, action)
            )
        "#).execute(&self.pool).await?;

        // Table des logs d'événements
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS event_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                message TEXT NOT NULL,
                context TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        "#).execute(&self.pool).await?;

        // Table des commandes exécutées
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS command_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                command_name TEXT NOT NULL,
                parameters TEXT,
                user_id TEXT,
                guild_id TEXT,
                channel_id TEXT,
                success BOOLEAN NOT NULL,
                error_message TEXT,
                execution_time_ms INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        "#).execute(&self.pool).await?;

        // Table des notifications
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS notifications_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                notification_type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                metadata TEXT,
                read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        "#).execute(&self.pool).await?;

        // Table des tokens sécurisés (avec chiffrement)
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS secure_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                token_type TEXT NOT NULL,
                encrypted_value TEXT NOT NULL,
                account_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP,
                UNIQUE(token_type, account_name)
            )
        "#).execute(&self.pool).await?;

        // Table des giveaways
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS giveaway_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                giveaway_id TEXT NOT NULL UNIQUE,
                guild_id TEXT NOT NULL,
                channel_id TEXT NOT NULL,
                prize TEXT NOT NULL,
                host_id TEXT NOT NULL,
                joined BOOLEAN NOT NULL,
                won BOOLEAN DEFAULT FALSE,
                end_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        "#).execute(&self.pool).await?;

        // Table des codes Nitro
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS nitro_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT NOT NULL UNIQUE,
                code_type TEXT NOT NULL,
                guild_id TEXT,
                channel_id TEXT,
                author_id TEXT,
                claimed BOOLEAN NOT NULL,
                claim_time_ms INTEGER,
                success BOOLEAN DEFAULT FALSE,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        "#).execute(&self.pool).await?;

        // Table des images générées
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS generated_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_type TEXT NOT NULL,
                parameters TEXT NOT NULL,
                file_path TEXT,
                size_bytes INTEGER,
                generation_time_ms INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        "#).execute(&self.pool).await?;

        // Table des sessions
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL UNIQUE,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP,
                total_commands INTEGER DEFAULT 0,
                total_events INTEGER DEFAULT 0,
                peak_memory_mb INTEGER,
                average_cpu_percent REAL
            )
        "#).execute(&self.pool).await?;

        // Créer les index pour performance
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_event_logs_type ON event_logs(event_type)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_command_history_name ON command_history(command_name)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_giveaway_history_guild ON giveaway_history(guild_id)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_nitro_history_claimed ON nitro_history(claimed)")
            .execute(&self.pool).await?;

        Ok(())
    }

    // =================== CONFIGURATIONS ===================

    /// Sauvegarder une configuration
    pub async fn save_config(&self, category: &str, key: &str, value: &str) -> Result<()> {
        sqlx::query(r#"
            INSERT INTO configurations (category, key, value, updated_at)
            VALUES (?1, ?2, ?3, CURRENT_TIMESTAMP)
            ON CONFLICT(category, key) DO UPDATE SET
                value = excluded.value,
                updated_at = CURRENT_TIMESTAMP
        "#)
        .bind(category)
        .bind(key)
        .bind(value)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }

    /// Récupérer une configuration
    pub async fn get_config(&self, category: &str, key: &str) -> Result<Option<String>> {
        let result = sqlx::query(r#"
            SELECT value FROM configurations
            WHERE category = ?1 AND key = ?2
        "#)
        .bind(category)
        .bind(key)
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(result.map(|row| row.get("value")))
    }

    /// Récupérer toutes les configurations d'une catégorie
    pub async fn get_category_configs(&self, category: &str) -> Result<Vec<(String, String)>> {
        let rows = sqlx::query(r#"
            SELECT key, value FROM configurations
            WHERE category = ?1
            ORDER BY key
        "#)
        .bind(category)
        .fetch_all(&self.pool)
        .await?;
        
        Ok(rows.into_iter().map(|row| {
            (row.get("key"), row.get("value"))
        }).collect())
    }

    // =================== STATISTIQUES ===================

    /// Incrémenter une statistique d'utilisation
    pub async fn increment_usage_stat(&self, feature: &str, action: &str, metadata: Option<&str>) -> Result<()> {
        sqlx::query(r#"
            INSERT INTO usage_stats (feature, action, count, last_used, metadata)
            VALUES (?1, ?2, 1, CURRENT_TIMESTAMP, ?3)
            ON CONFLICT(feature, action) DO UPDATE SET
                count = count + 1,
                last_used = CURRENT_TIMESTAMP,
                metadata = COALESCE(?3, metadata)
        "#)
        .bind(feature)
        .bind(action)
        .bind(metadata)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }

    /// Récupérer les statistiques d'utilisation
    pub async fn get_usage_stats(&self, feature: Option<&str>) -> Result<Vec<UsageStat>> {
        let query = if let Some(feature) = feature {
            sqlx::query(r#"
                SELECT feature, action, count, last_used, metadata
                FROM usage_stats
                WHERE feature = ?1
                ORDER BY count DESC
            "#).bind(feature)
        } else {
            sqlx::query(r#"
                SELECT feature, action, count, last_used, metadata
                FROM usage_stats
                ORDER BY count DESC
                LIMIT 100
            "#)
        };
        
        let rows = query.fetch_all(&self.pool).await?;
        
        Ok(rows.into_iter().map(|row| UsageStat {
            feature: row.get("feature"),
            action: row.get("action"),
            count: row.get("count"),
            last_used: row.get("last_used"),
            metadata: row.get("metadata"),
        }).collect())
    }

    // =================== LOGS D'ÉVÉNEMENTS ===================

    /// Ajouter un log d'événement
    pub async fn log_event(&self, event_type: &str, severity: EventSeverity, message: &str, context: Option<&str>) -> Result<()> {
        sqlx::query(r#"
            INSERT INTO event_logs (event_type, severity, message, context)
            VALUES (?1, ?2, ?3, ?4)
        "#)
        .bind(event_type)
        .bind(severity.as_str())
        .bind(message)
        .bind(context)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }

    /// Récupérer les logs récents
    pub async fn get_recent_logs(&self, limit: i32, severity_filter: Option<EventSeverity>) -> Result<Vec<EventLog>> {
        let severity_str = severity_filter.as_ref().map(|s| s.as_str());
        
        let query = if let Some(severity) = severity_str {
            sqlx::query(r#"
                SELECT id, event_type, severity, message, context, created_at
                FROM event_logs
                WHERE severity = ?1
                ORDER BY created_at DESC
                LIMIT ?2
            "#)
            .bind(severity)
            .bind(limit)
        } else {
            sqlx::query(r#"
                SELECT id, event_type, severity, message, context, created_at
                FROM event_logs
                ORDER BY created_at DESC
                LIMIT ?1
            "#)
            .bind(limit)
        };
        
        let rows = query.fetch_all(&self.pool).await?;
        
        Ok(rows.into_iter().map(|row| EventLog {
            id: row.get("id"),
            event_type: row.get("event_type"),
            severity: EventSeverity::from_str(row.get("severity")),
            message: row.get("message"),
            context: row.get("context"),
            created_at: row.get("created_at"),
        }).collect())
    }

    // =================== HISTORIQUE DES COMMANDES ===================

    /// Enregistrer une commande exécutée
    pub async fn log_command(&self, command: CommandLog) -> Result<()> {
        sqlx::query(r#"
            INSERT INTO command_history (
                command_name, parameters, user_id, guild_id, channel_id,
                success, error_message, execution_time_ms
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
        "#)
        .bind(&command.command_name)
        .bind(&command.parameters)
        .bind(&command.user_id)
        .bind(&command.guild_id)
        .bind(&command.channel_id)
        .bind(command.success)
        .bind(&command.error_message)
        .bind(command.execution_time_ms)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }

    /// Récupérer l'historique des commandes
    pub async fn get_command_history(&self, limit: i32, user_id: Option<&str>) -> Result<Vec<CommandLog>> {
        let query = if let Some(user_id) = user_id {
            sqlx::query(r#"
                SELECT * FROM command_history
                WHERE user_id = ?1
                ORDER BY created_at DESC
                LIMIT ?2
            "#)
            .bind(user_id)
            .bind(limit)
        } else {
            sqlx::query(r#"
                SELECT * FROM command_history
                ORDER BY created_at DESC
                LIMIT ?1
            "#)
            .bind(limit)
        };
        
        let rows = query.fetch_all(&self.pool).await?;
        
        Ok(rows.into_iter().map(|row| CommandLog {
            command_name: row.get("command_name"),
            parameters: row.get("parameters"),
            user_id: row.get("user_id"),
            guild_id: row.get("guild_id"),
            channel_id: row.get("channel_id"),
            success: row.get("success"),
            error_message: row.get("error_message"),
            execution_time_ms: row.get("execution_time_ms"),
            created_at: row.get("created_at"),
        }).collect())
    }

    // =================== STATISTIQUES GLOBALES ===================

    /// Obtenir les statistiques de la base de données
    pub async fn get_database_stats(&self) -> Result<DatabaseStats> {
        let total_configs: i64 = sqlx::query("SELECT COUNT(*) as count FROM configurations")
            .fetch_one(&self.pool)
            .await?
            .get("count");
            
        let total_commands: i64 = sqlx::query("SELECT COUNT(*) as count FROM command_history")
            .fetch_one(&self.pool)
            .await?
            .get("count");
            
        let total_events: i64 = sqlx::query("SELECT COUNT(*) as count FROM event_logs")
            .fetch_one(&self.pool)
            .await?
            .get("count");
            
        let total_giveaways: i64 = sqlx::query("SELECT COUNT(*) as count FROM giveaway_history")
            .fetch_one(&self.pool)
            .await?
            .get("count");
            
        let total_nitro_codes: i64 = sqlx::query("SELECT COUNT(*) as count FROM nitro_history")
            .fetch_one(&self.pool)
            .await?
            .get("count");
            
        let db_size_kb = self.get_database_size_kb().await?;
        
        Ok(DatabaseStats {
            total_configs: total_configs as u64,
            total_commands: total_commands as u64,
            total_events: total_events as u64,
            total_giveaways: total_giveaways as u64,
            total_nitro_codes: total_nitro_codes as u64,
            database_size_kb: db_size_kb,
        })
    }

    /// Obtenir la taille de la base de données en KB
    async fn get_database_size_kb(&self) -> Result<u64> {
        let page_count: i64 = sqlx::query("PRAGMA page_count")
            .fetch_one(&self.pool)
            .await?
            .get(0);
            
        let page_size: i64 = sqlx::query("PRAGMA page_size")
            .fetch_one(&self.pool)
            .await?
            .get(0);
            
        Ok((page_count * page_size / 1024) as u64)
    }

    /// Nettoyer les anciennes données
    pub async fn cleanup_old_data(&self, days_to_keep: i32) -> Result<()> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days_to_keep as i64);
        
        // Nettoyer les logs
        sqlx::query("DELETE FROM event_logs WHERE created_at < ?1")
            .bind(cutoff_date)
            .execute(&self.pool)
            .await?;
            
        // Nettoyer l'historique des commandes
        sqlx::query("DELETE FROM command_history WHERE created_at < ?1")
            .bind(cutoff_date)
            .execute(&self.pool)
            .await?;
            
        // Nettoyer les notifications lues
        sqlx::query("DELETE FROM notifications_history WHERE read = TRUE AND created_at < ?1")
            .bind(cutoff_date)
            .execute(&self.pool)
            .await?;
            
        // Optimiser la base de données
        sqlx::query("VACUUM")
            .execute(&self.pool)
            .await?;
            
        Ok(())
    }

    /// Fermer la base de données
    pub async fn close(self) -> Result<()> {
        self.pool.close().await;
        Ok(())
    }
}

// =================== STRUCTURES DE DONNÉES ===================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UsageStat {
    pub feature: String,
    pub action: String,
    pub count: i64,
    pub last_used: DateTime<Utc>,
    pub metadata: Option<String>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum EventSeverity {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

impl EventSeverity {
    pub fn as_str(&self) -> &str {
        match self {
            Self::Debug => "DEBUG",
            Self::Info => "INFO",
            Self::Warning => "WARNING",
            Self::Error => "ERROR",
            Self::Critical => "CRITICAL",
        }
    }
    
    pub fn from_str(s: &str) -> Self {
        match s {
            "DEBUG" => Self::Debug,
            "INFO" => Self::Info,
            "WARNING" => Self::Warning,
            "ERROR" => Self::Error,
            "CRITICAL" => Self::Critical,
            _ => Self::Info,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventLog {
    pub id: i64,
    pub event_type: String,
    pub severity: EventSeverity,
    pub message: String,
    pub context: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandLog {
    pub command_name: String,
    pub parameters: Option<String>,
    pub user_id: Option<String>,
    pub guild_id: Option<String>,
    pub channel_id: Option<String>,
    pub success: bool,
    pub error_message: Option<String>,
    pub execution_time_ms: Option<i64>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseStats {
    pub total_configs: u64,
    pub total_commands: u64,
    pub total_events: u64,
    pub total_giveaways: u64,
    pub total_nitro_codes: u64,
    pub database_size_kb: u64,
}