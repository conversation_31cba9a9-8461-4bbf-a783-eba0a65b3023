import { 
  Home, 
  Settings, 
  Zap, 
  Gamepad2, 
  Database, 
  Eye, 
  Command,
  Activity,
  Bell,
  Skull,
  Bot
} from 'lucide-react';
import { useAppStore } from '../stores/appStore';

const navigation = [
  { id: 'dashboard', label: 'Tableau de bord', icon: Home },
  { id: 'stealth', label: 'Mode Furtif', icon: Eye },
  { id: 'notifications', label: 'Notifications', icon: Bell },
  { id: 'troll', label: 'Contrôle Trolls', icon: Skull },
  { id: 'auto', label: 'Auto-Commands', icon: Bo<PERSON> },
  { id: 'commands', label: 'Commandes', icon: Command },
  { id: 'animations', label: 'Animations', icon: Zap },
  { id: 'rpc', label: 'Rich Presence', icon: Gamepad2 },
  { id: 'backup', label: 'Sauvegarde', icon: Database },
  { id: 'stats', label: 'Statistiques', icon: Activity },
  { id: 'settings', label: 'Paramètres', icon: Settings },
];

export function Sidebar() {
  const { currentPage, setCurrentPage, isConnected, stealthMode } = useAppStore();

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="sidebar-logo">VoidBot</div>
        <div className="flex items-center gap-2 mt-2">
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-green-500' : 'bg-red-500'
          }`} />
          <span className="text-sm text-gray-400">
            {isConnected ? 'Connecté' : 'Déconnecté'}
          </span>
        </div>
        
        {stealthMode === 'ghost' && (
          <div className="status-ghost mt-2">
            👻 Mode Fantôme
          </div>
        )}
      </div>

      <nav className="sidebar-nav">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.id;
          
          return (
            <div
              key={item.id}
              className={`nav-item ${isActive ? 'active' : ''}`}
              onClick={() => setCurrentPage(item.id)}
            >
              <Icon className="nav-icon" />
              <span>{item.label}</span>
            </div>
          );
        })}
      </nav>

      <div className="p-4 mt-auto border-t border-gray-700">
        <div className="text-xs text-gray-500 text-center">
          VoidBot v1.0.0
        </div>
      </div>
    </div>
  );
}