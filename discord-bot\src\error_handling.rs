use thiserror::Error;
use tracing::{error, warn, info};
use std::fmt;

/// Types d'erreurs spécifiques à VoidBot Discord
#[derive(Error, Debug, Clone)]
pub enum VoidBotError {
    #[error("Erreur Discord API: {message}")]
    DiscordApi { message: String, code: Option<u16> },
    
    #[error("Erreur de cache Discord: {resource} non trouvé")]
    CacheNotFound { resource: String, id: String },
    
    #[error("Erreur de notification: {message}")]
    Notification { message: String, context: Option<String> },
    
    #[error("Erreur de configuration: {message}")]
    Configuration { message: String, field: Option<String> },
    
    #[error("Erreur de rate limiting: {message}")]
    RateLimit { message: String, retry_after: Option<u64> },
    
    #[error("Erreur de réseau: {message}")]
    Network { message: String, url: Option<String> },
    
    #[error("Erreur de sérialisation: {message}")]
    Serialization { message: String },
    
    #[error("Erreur interne: {message}")]
    Internal { message: String, context: Option<String> },
}

impl VoidBotError {
    /// Créer une erreur de cache non trouvé
    pub fn cache_not_found(resource: &str, id: &str) -> Self {
        warn!("Ressource {} avec ID {} non trouvée dans le cache Discord", resource, id);
        Self::CacheNotFound {
            resource: resource.to_string(),
            id: id.to_string(),
        }
    }
    
    /// Créer une erreur de notification
    pub fn notification_error(message: &str, context: Option<&str>) -> Self {
        error!("Erreur de notification: {} (contexte: {:?})", message, context);
        Self::Notification {
            message: message.to_string(),
            context: context.map(|s| s.to_string()),
        }
    }
    
    /// Créer une erreur Discord API
    pub fn discord_api_error(message: &str, code: Option<u16>) -> Self {
        error!("Erreur Discord API: {} (code: {:?})", message, code);
        Self::DiscordApi {
            message: message.to_string(),
            code,
        }
    }
    
    /// Créer une erreur de configuration
    pub fn config_error(message: &str, field: Option<&str>) -> Self {
        error!("Erreur de configuration: {} (champ: {:?})", message, field);
        Self::Configuration {
            message: message.to_string(),
            field: field.map(|s| s.to_string()),
        }
    }
    
    /// Vérifier si l'erreur est récupérable
    pub fn is_recoverable(&self) -> bool {
        match self {
            VoidBotError::DiscordApi { code, .. } => {
                // Codes 5xx sont généralement récupérables
                code.map_or(false, |c| c >= 500 && c < 600)
            },
            VoidBotError::CacheNotFound { .. } => true, // Cache peut être rafraîchi
            VoidBotError::Network { .. } => true, // Problèmes réseau temporaires
            VoidBotError::RateLimit { .. } => true, // Rate limit temporaire
            _ => false,
        }
    }
    
    /// Obtenir le délai de retry recommandé (en secondes)
    pub fn retry_delay(&self) -> Option<u64> {
        match self {
            VoidBotError::RateLimit { retry_after, .. } => *retry_after,
            VoidBotError::DiscordApi { .. } => Some(5), // 5 secondes par défaut
            VoidBotError::Network { .. } => Some(3), // 3 secondes pour réseau
            VoidBotError::CacheNotFound { .. } => Some(1), // 1 seconde pour cache
            _ => None,
        }
    }
    
    /// Obtenir la sévérité de l'erreur
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            VoidBotError::Internal { .. } => ErrorSeverity::Critical,
            VoidBotError::Configuration { .. } => ErrorSeverity::High,
            VoidBotError::DiscordApi { code, .. } => {
                match code {
                    Some(401) | Some(403) => ErrorSeverity::High, // Auth/Permission
                    Some(429) => ErrorSeverity::Medium, // Rate limit
                    Some(500..=599) => ErrorSeverity::Medium, // Server errors
                    _ => ErrorSeverity::Low,
                }
            },
            VoidBotError::CacheNotFound { .. } => ErrorSeverity::Low,
            VoidBotError::Network { .. } => ErrorSeverity::Medium,
            _ => ErrorSeverity::Medium,
        }
    }
}

/// Niveaux de sévérité des erreurs
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorSeverity::Low => write!(f, "LOW"),
            ErrorSeverity::Medium => write!(f, "MEDIUM"),
            ErrorSeverity::High => write!(f, "HIGH"),
            ErrorSeverity::Critical => write!(f, "CRITICAL"),
        }
    }
}

/// Gestionnaire d'erreurs avec retry automatique et logging
pub struct ErrorHandler;

impl ErrorHandler {
    /// Gérer une erreur avec retry automatique si applicable
    pub async fn handle_with_retry<F, T, E>(
        operation: F,
        max_retries: u32,
        context: &str,
    ) -> Result<T, VoidBotError>
    where
        F: Fn() -> Result<T, E> + Send + Sync,
        E: Into<VoidBotError>,
    {
        let mut last_error = None;
        
        for attempt in 0..=max_retries {
            match operation() {
                Ok(result) => {
                    if attempt > 0 {
                        info!("Opération réussie après {} tentatives: {}", attempt, context);
                    }
                    return Ok(result);
                },
                Err(e) => {
                    let error = e.into();
                    last_error = Some(error.clone());
                    
                    if attempt < max_retries && error.is_recoverable() {
                        let delay = error.retry_delay().unwrap_or(1);
                        warn!(
                            "Tentative {}/{} échouée pour '{}': {}. Retry dans {}s",
                            attempt + 1, max_retries + 1, context, error, delay
                        );
                        tokio::time::sleep(tokio::time::Duration::from_secs(delay)).await;
                    } else {
                        error!("Échec définitif pour '{}' après {} tentatives: {}", context, attempt + 1, error);
                        break;
                    }
                }
            }
        }
        
        Err(last_error.unwrap())
    }
    
    /// Logger une erreur selon sa sévérité
    pub fn log_error(error: &VoidBotError, context: &str) {
        match error.severity() {
            ErrorSeverity::Critical => error!("[CRITICAL] {}: {}", context, error),
            ErrorSeverity::High => error!("[HIGH] {}: {}", context, error),
            ErrorSeverity::Medium => warn!("[MEDIUM] {}: {}", context, error),
            ErrorSeverity::Low => info!("[LOW] {}: {}", context, error),
        }
    }
}

/// Trait pour convertir les erreurs Serenity en VoidBotError
impl From<serenity::Error> for VoidBotError {
    fn from(error: serenity::Error) -> Self {
        match error {
            serenity::Error::Http(http_error) => {
                VoidBotError::discord_api_error(&http_error.to_string(), None)
            },
            serenity::Error::Json(json_error) => {
                VoidBotError::Serialization {
                    message: json_error.to_string(),
                }
            },
            _ => VoidBotError::Internal {
                message: error.to_string(),
                context: Some("Serenity error".to_string()),
            },
        }
    }
}

/// Trait pour convertir les erreurs reqwest en VoidBotError
impl From<reqwest::Error> for VoidBotError {
    fn from(error: reqwest::Error) -> Self {
        VoidBotError::Network {
            message: error.to_string(),
            url: error.url().map(|u| u.to_string()),
        }
    }
}

/// Trait pour convertir les erreurs serde_json en VoidBotError
impl From<serde_json::Error> for VoidBotError {
    fn from(error: serde_json::Error) -> Self {
        VoidBotError::Serialization {
            message: error.to_string(),
        }
    }
}

/// Macro pour simplifier la gestion d'erreurs avec contexte
#[macro_export]
macro_rules! handle_error {
    ($result:expr, $context:expr) => {
        match $result {
            Ok(value) => value,
            Err(e) => {
                let error = VoidBotError::from(e);
                ErrorHandler::log_error(&error, $context);
                return Err(error);
            }
        }
    };
}

/// Macro pour les opérations avec retry automatique
#[macro_export]
macro_rules! retry_operation {
    ($operation:expr, $max_retries:expr, $context:expr) => {
        ErrorHandler::handle_with_retry(
            || $operation,
            $max_retries,
            $context,
        ).await
    };
}
