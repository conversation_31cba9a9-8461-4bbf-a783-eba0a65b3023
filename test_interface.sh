#!/bin/bash

# Script de test de l'interface VoidBot en mode navigateur
# Permet de tester l'interface React sans Tauri sous WSL2

set -e

# Couleurs pour les logs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DESKTOP_DIR="$PROJECT_ROOT/voidbot-desktop"

echo "🧪 Test Interface VoidBot - Mode Navigateur"
echo "============================================="

# Vérifier les dépendances
log_info "Vérification de l'environnement..."

if ! command -v node &> /dev/null; then
    log_error "Node.js non trouvé"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_error "npm non trouvé"
    exit 1
fi

log_success "Node.js $(node --version) et npm $(npm --version) détectés"

# Aller dans le répertoire desktop
cd "$DESKTOP_DIR"

# Vérifier node_modules
if [ ! -d "node_modules" ]; then
    log_info "Installation des dépendances npm..."
    npm install
fi

log_success "Dépendances npm prêtes"

# Nettoyer les processus existants
log_info "Nettoyage des processus précédents..."
pkill -f "vite" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true

# Attendre un peu
sleep 2

# Configuration du mode test
export VITE_TEST_MODE=true
export VITE_API_URL=http://localhost:3001

log_info "Démarrage de l'interface React..."
log_warning "Mode test activé - simulation des fonctionnalités Discord"

# Démarrer le serveur dev
nohup npm run dev > /tmp/voidbot_dev.log 2>&1 &
DEV_PID=$!

# Attendre que le serveur démarre
log_info "Attente du démarrage du serveur..."
for i in {1..30}; do
    if curl -s http://localhost:5173 >/dev/null 2>&1; then
        break
    fi
    sleep 1
    echo -n "."
done
echo ""

# Vérifier que le serveur répond
if curl -s http://localhost:5173 >/dev/null 2>&1; then
    log_success "Interface VoidBot démarrée avec succès !"
    echo ""
    echo "🌐 URLs de test :"
    echo "   Interface principale : http://localhost:5173"
    echo "   Accessible depuis Windows : http://$(hostname -I | awk '{print $1}'):5173"
    echo ""
    echo "🎮 Test de l'interface :"
    echo "   1. Ouvrir http://localhost:5173 dans votre navigateur"
    echo "   2. Tester la navigation et les fonctionnalités"
    echo "   3. Interface complète disponible en mode simulation"
    echo ""
    echo "📱 Fonctionnalités testables :"
    echo "   ✅ Dashboard et navigation"
    echo "   ✅ Login screen et simulation auth"
    echo "   ✅ Toutes les pages (Commands, Trolls, Settings)"
    echo "   ✅ Thèmes et animations"
    echo "   ✅ Configuration et persistance"
    echo ""
    echo "🛑 Pour arrêter : Ctrl+C"
    echo ""
    log_info "Logs du serveur : tail -f /tmp/voidbot_dev.log"
    
    # Garder le script actif
    trap "log_info 'Arrêt du serveur...'; kill $DEV_PID 2>/dev/null; exit 0" INT
    
    # Attendre et afficher les logs
    while kill -0 $DEV_PID 2>/dev/null; do
        sleep 5
        if [ -f /tmp/voidbot_dev.log ]; then
            if tail -5 /tmp/voidbot_dev.log | grep -q "error\|Error\|ERROR"; then
                log_warning "Erreurs détectées dans les logs"
            fi
        fi
    done
    
else
    log_error "Impossible de démarrer l'interface"
    log_info "Vérification des logs..."
    if [ -f /tmp/voidbot_dev.log ]; then
        cat /tmp/voidbot_dev.log
    fi
    exit 1
fi