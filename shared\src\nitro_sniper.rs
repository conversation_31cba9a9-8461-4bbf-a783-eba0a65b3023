use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Configuration du Nitro Sniper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NitroSniperConfig {
    /// Activer/désactiver le sniper
    pub enabled: bool,
    /// Délai minimum avant de récupérer (en millisecondes)
    pub min_delay_ms: u32,
    /// Délai maximum avant de récupérer (en millisecondes)
    pub max_delay_ms: u32,
    /// Serveurs à surveiller (vide = tous)
    pub monitored_guilds: Vec<u64>,
    /// Serveurs à ignorer
    pub blacklisted_guilds: Vec<u64>,
    /// Canaux à surveiller spécifiquement
    pub monitored_channels: Vec<u64>,
    /// Canaux à ignorer
    pub blacklisted_channels: Vec<u64>,
    /// Seulement les codes postés par des bots vérifiés
    pub verified_bots_only: bool,
    /// Liste d'ID de bots à surveiller spécifiquement
    pub trusted_bot_ids: Vec<u64>,
    /// Utiliser des comptes alternatifs si disponibles
    pub use_alt_accounts: bool,
    /// Notification quand un code est détecté
    pub notify_on_detect: bool,
    /// Notification quand un code est récupéré
    pub notify_on_claim: bool,
    /// Mode test (log seulement, ne récupère pas)
    pub test_mode: bool,
    /// Ignorer les codes expirés (plus de X minutes)
    pub ignore_old_codes_minutes: u32,
}

impl Default for NitroSniperConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            min_delay_ms: 50,   // Ultra rapide par défaut
            max_delay_ms: 200,  // Maximum 200ms
            monitored_guilds: Vec::new(),
            blacklisted_guilds: Vec::new(),
            monitored_channels: Vec::new(),
            blacklisted_channels: Vec::new(),
            verified_bots_only: false,
            trusted_bot_ids: Vec::new(),
            use_alt_accounts: false,
            notify_on_detect: true,
            notify_on_claim: true,
            test_mode: true, // Mode test par défaut pour sécurité
            ignore_old_codes_minutes: 10,
        }
    }
}

/// Types de codes Nitro Discord
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NitroCodeType {
    /// Code Nitro classique (1 mois)
    NitroClassic,
    /// Code Nitro Boost (3 mois)
    NitroBoost,
    /// Code Server Boost
    ServerBoost,
    /// Code inconnu/générique
    Unknown,
}

/// Statut d'un code Nitro
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NitroCodeStatus {
    /// Code détecté mais pas encore testé
    Detected,
    /// Code récupéré avec succès
    Claimed,
    /// Code invalide ou expiré
    Invalid,
    /// Code déjà utilisé par quelqu'un d'autre
    AlreadyClaimed,
    /// Erreur lors de la récupération
    Error,
    /// Ignoré (config ou filtres)
    Ignored,
}

/// Informations sur un code Nitro détecté
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NitroCodeInfo {
    /// ID unique du code
    pub id: String,
    /// Code Nitro brut
    pub code: String,
    /// Type de code détecté
    pub code_type: NitroCodeType,
    /// URL complète du code
    pub full_url: String,
    /// ID du message Discord
    pub message_id: u64,
    /// ID du canal Discord
    pub channel_id: u64,
    /// ID du serveur Discord
    pub guild_id: Option<u64>,
    /// Nom du serveur
    pub guild_name: Option<String>,
    /// Nom du canal
    pub channel_name: String,
    /// Auteur du message
    pub author_id: u64,
    /// Nom de l'auteur
    pub author_username: String,
    /// Si l'auteur est un bot
    pub author_is_bot: bool,
    /// Contenu complet du message
    pub message_content: String,
    /// Statut du code
    pub status: NitroCodeStatus,
    /// Timestamp de détection
    pub detected_at: DateTime<Utc>,
    /// Timestamp de récupération
    pub claimed_at: Option<DateTime<Utc>>,
    /// Délai utilisé pour la récupération (ms)
    pub claim_delay_ms: Option<u32>,
    /// Temps de réaction total (ms)
    pub reaction_time_ms: Option<u32>,
    /// Message d'erreur si échec
    pub error_message: Option<String>,
    /// Compte utilisé pour récupérer
    pub claimed_by_account: Option<String>,
}

/// Résultat d'une tentative de récupération
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaimResult {
    /// Succès de la récupération
    pub success: bool,
    /// Message de résultat
    pub message: String,
    /// Code qui a été testé
    pub code: String,
    /// Type de code
    pub code_type: NitroCodeType,
    /// Timestamp de la tentative
    pub attempted_at: DateTime<Utc>,
    /// Délai utilisé (en millisecondes)
    pub delay_used_ms: u32,
    /// Temps de réaction total (ms)
    pub total_time_ms: u32,
    /// Compte utilisé
    pub account_used: String,
}

/// Gestionnaire principal du Nitro Sniper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NitroSniperManager {
    /// Configuration actuelle
    pub config: NitroSniperConfig,
    /// Codes détectés
    pub detected_codes: Vec<NitroCodeInfo>,
    /// Statistiques
    pub stats: NitroSniperStats,
}

/// Statistiques du Nitro Sniper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NitroSniperStats {
    /// Nombre total de codes détectés
    pub total_detected: u32,
    /// Nombre de codes récupérés avec succès
    pub total_claimed: u32,
    /// Nombre de codes invalides
    pub total_invalid: u32,
    /// Nombre de codes déjà récupérés
    pub total_already_claimed: u32,
    /// Nombre de codes ignorés
    pub total_ignored: u32,
    /// Temps de réaction moyen (ms)
    pub average_reaction_time_ms: f32,
    /// Temps de réaction le plus rapide (ms)
    pub fastest_reaction_time_ms: Option<u32>,
    /// Dernière activité
    pub last_activity: Option<DateTime<Utc>>,
    /// Taux de succès (%)
    pub success_rate: f32,
    /// Valeur estimée récupérée (en euros)
    pub estimated_value_claimed: f32,
}

impl Default for NitroSniperStats {
    fn default() -> Self {
        Self {
            total_detected: 0,
            total_claimed: 0,
            total_invalid: 0,
            total_already_claimed: 0,
            total_ignored: 0,
            average_reaction_time_ms: 0.0,
            fastest_reaction_time_ms: None,
            last_activity: None,
            success_rate: 0.0,
            estimated_value_claimed: 0.0,
        }
    }
}

impl NitroSniperManager {
    pub fn new() -> Self {
        Self {
            config: NitroSniperConfig::default(),
            detected_codes: Vec::new(),
            stats: NitroSniperStats::default(),
        }
    }

    /// Ajouter un nouveau code détecté
    pub fn add_detected_code(&mut self, code: NitroCodeInfo) {
        self.detected_codes.push(code);
        self.stats.total_detected += 1;
        self.stats.last_activity = Some(Utc::now());
        
        // Garder seulement les 500 derniers codes
        if self.detected_codes.len() > 500 {
            self.detected_codes.remove(0);
        }
    }

    /// Marquer un code comme récupéré
    pub fn mark_as_claimed(&mut self, code_id: &str, result: &ClaimResult) -> bool {
        // Première phase: mettre à jour le code et récupérer les infos nécessaires
        let (found_code, code_type) = {
            if let Some(code) = self.detected_codes.iter_mut()
                .find(|c| c.id == code_id) {
                
                code.status = if result.success { 
                    NitroCodeStatus::Claimed 
                } else { 
                    NitroCodeStatus::Error 
                };
                code.claimed_at = Some(result.attempted_at);
                code.claim_delay_ms = Some(result.delay_used_ms);
                code.reaction_time_ms = Some(result.total_time_ms);
                code.claimed_by_account = Some(result.account_used.clone());
                
                if !result.success {
                    code.error_message = Some(result.message.clone());
                }
                
                (true, code.code_type.clone())
            } else {
                (false, NitroCodeType::Unknown) // Valeur par défaut si non trouvé
            }
        };

        // Deuxième phase: mettre à jour les statistiques après avoir libéré l'emprunt mutable
        if found_code {
            if result.success {
                self.stats.total_claimed += 1;
                self.update_reaction_time_stats(result.total_time_ms);
                self.update_estimated_value(&code_type);
            } else {
                // Analyser le type d'erreur et mettre à jour le statut
                let new_status = if result.message.to_lowercase().contains("already") || 
                   result.message.to_lowercase().contains("redeemed") {
                    self.stats.total_already_claimed += 1;
                    NitroCodeStatus::AlreadyClaimed
                } else if result.message.to_lowercase().contains("invalid") ||
                         result.message.to_lowercase().contains("expired") {
                    self.stats.total_invalid += 1;
                    NitroCodeStatus::Invalid
                } else {
                    NitroCodeStatus::Error
                };
                
                // Mettre à jour le statut du code dans un autre emprunt mutable
                if let Some(code) = self.detected_codes.iter_mut().find(|c| c.id == code_id) {
                    code.status = new_status;
                }
            }
            
            self.update_success_rate();
            true
        } else {
            false
        }
    }

    /// Marquer un code comme ignoré
    pub fn mark_as_ignored(&mut self, code_id: &str, reason: String) -> bool {
        if let Some(code) = self.detected_codes.iter_mut()
            .find(|c| c.id == code_id) {
            code.status = NitroCodeStatus::Ignored;
            code.error_message = Some(reason);
            self.stats.total_ignored += 1;
            true
        } else {
            false
        }
    }

    /// Mettre à jour les statistiques de temps de réaction
    fn update_reaction_time_stats(&mut self, reaction_time_ms: u32) {
        // Mettre à jour le temps le plus rapide
        if self.stats.fastest_reaction_time_ms.is_none() || 
           reaction_time_ms < self.stats.fastest_reaction_time_ms.unwrap() {
            self.stats.fastest_reaction_time_ms = Some(reaction_time_ms);
        }

        // Calculer la moyenne
        let total_claimed = self.stats.total_claimed as f32;
        let current_avg = self.stats.average_reaction_time_ms;
        self.stats.average_reaction_time_ms = 
            ((current_avg * (total_claimed - 1.0)) + reaction_time_ms as f32) / total_claimed;
    }

    /// Mettre à jour la valeur estimée récupérée
    fn update_estimated_value(&mut self, code_type: &NitroCodeType) {
        let value = match code_type {
            NitroCodeType::NitroClassic => 9.99,
            NitroCodeType::NitroBoost => 14.99,
            NitroCodeType::ServerBoost => 4.99,
            NitroCodeType::Unknown => 9.99, // Estimation conservatrice
        };
        self.stats.estimated_value_claimed += value;
    }

    /// Mettre à jour le taux de succès
    fn update_success_rate(&mut self) {
        let total_attempts = self.stats.total_claimed + 
                           self.stats.total_invalid + 
                           self.stats.total_already_claimed;
        
        if total_attempts > 0 {
            self.stats.success_rate = 
                (self.stats.total_claimed as f32 / total_attempts as f32) * 100.0;
        }
    }

    /// Obtenir les codes récupérés avec succès
    pub fn get_claimed_codes(&self) -> Vec<&NitroCodeInfo> {
        self.detected_codes.iter()
            .filter(|c| c.status == NitroCodeStatus::Claimed)
            .collect()
    }

    /// Obtenir les codes récents (dernières 24h)
    pub fn get_recent_codes(&self, hours: i64) -> Vec<&NitroCodeInfo> {
        let cutoff = Utc::now() - chrono::Duration::hours(hours);
        self.detected_codes.iter()
            .filter(|c| c.detected_at > cutoff)
            .collect()
    }

    /// Nettoyer les anciens codes
    pub fn cleanup_old_codes(&mut self, max_age_days: i64) {
        let cutoff = Utc::now() - chrono::Duration::days(max_age_days);
        self.detected_codes.retain(|c| c.detected_at > cutoff);
    }

    /// Obtenir les statistiques de performance
    pub fn get_performance_stats(&self) -> HashMap<String, f32> {
        let mut stats = HashMap::new();
        
        stats.insert("success_rate".to_string(), self.stats.success_rate);
        stats.insert("average_reaction_time".to_string(), self.stats.average_reaction_time_ms);
        stats.insert("fastest_reaction_time".to_string(), 
                     self.stats.fastest_reaction_time_ms.unwrap_or(0) as f32);
        stats.insert("total_value_claimed".to_string(), self.stats.estimated_value_claimed);
        stats.insert("codes_per_day".to_string(), 
                     self.get_recent_codes(24).len() as f32);
        
        stats
    }
}

/// Événements du Nitro Sniper pour le frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NitroSniperEvent {
    /// Nouveau code détecté
    CodeDetected {
        code_info: NitroCodeInfo,
    },
    /// Code récupéré avec succès
    CodeClaimed {
        code_id: String,
        code_type: NitroCodeType,
        reaction_time_ms: u32,
        value: f32,
    },
    /// Échec de récupération
    ClaimFailed {
        code_id: String,
        reason: String,
        reaction_time_ms: u32,
    },
    /// Code ignoré
    CodeIgnored {
        code_id: String,
        reason: String,
    },
    /// Configuration mise à jour
    ConfigUpdated,
    /// Nouveau record de vitesse
    NewSpeedRecord {
        reaction_time_ms: u32,
        previous_record: Option<u32>,
    },
}

/// Validations pour le Nitro Sniper
impl crate::validation::Validate for NitroSniperConfig {
    fn validate(&self) -> Result<(), crate::validation::ValidationError> {
        use crate::validation::{IntValidator, DiscordIdValidator};

        // Validation des délais
        IntValidator::new().min(10).max(5000)
            .validate("min_delay_ms", self.min_delay_ms as i64)?;
        
        IntValidator::new().min(10).max(5000)
            .validate("max_delay_ms", self.max_delay_ms as i64)?;

        if self.min_delay_ms >= self.max_delay_ms {
            return Err(crate::validation::ValidationError::InvalidFormat {
                field: "delay_range".to_string(),
                reason: "Le délai minimum doit être inférieur au maximum".to_string(),
            });
        }

        // Validation de l'âge des codes ignorés
        IntValidator::new().min(1).max(1440) // Max 24h
            .validate("ignore_old_codes_minutes", self.ignore_old_codes_minutes as i64)?;

        // Validation des guildes surveillées
        for (i, guild_id) in self.monitored_guilds.iter().enumerate() {
            DiscordIdValidator::validate(&format!("monitored_guild_{}", i), &guild_id.to_string())?;
        }

        // Validation des guildes blacklistées
        for (i, guild_id) in self.blacklisted_guilds.iter().enumerate() {
            DiscordIdValidator::validate(&format!("blacklisted_guild_{}", i), &guild_id.to_string())?;
        }

        // Validation des canaux surveillés
        for (i, channel_id) in self.monitored_channels.iter().enumerate() {
            DiscordIdValidator::validate(&format!("monitored_channel_{}", i), &channel_id.to_string())?;
        }

        // Validation des canaux blacklistés
        for (i, channel_id) in self.blacklisted_channels.iter().enumerate() {
            DiscordIdValidator::validate(&format!("blacklisted_channel_{}", i), &channel_id.to_string())?;
        }

        // Validation des bots de confiance
        for (i, bot_id) in self.trusted_bot_ids.iter().enumerate() {
            DiscordIdValidator::validate(&format!("trusted_bot_{}", i), &bot_id.to_string())?;
        }

        Ok(())
    }
}