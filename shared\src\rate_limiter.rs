use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{<PERSON>te<PERSON>, Semaphore};
use tokio::time::{sleep, Duration, Instant};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use anyhow::Result;

/// Configuration du rate limiter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Activer/désactiver le rate limiting
    pub enabled: bool,
    /// Nombre max de requêtes par seconde global
    pub global_rate_per_second: u32,
    /// <PERSON><PERSON><PERSON> entre requêtes en ms
    pub min_delay_between_requests: u64,
    /// Activer le mode debug avec logs détaillés
    pub debug_mode: bool,
    /// Seuil d'alerte (% de la limite)
    pub warning_threshold: f32,
    /// Backoff multiplier pour 429
    pub backoff_multiplier: f32,
    /// Max backoff time en secondes
    pub max_backoff_seconds: u64,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            global_rate_per_second: 50, // Discord limite globale
            min_delay_between_requests: 100, // 100ms entre requêtes
            debug_mode: false,
            warning_threshold: 0.8, // Alerte à 80%
            backoff_multiplier: 2.0,
            max_backoff_seconds: 300, // 5 minutes max
        }
    }
}

/// Bucket pour un endpoint spécifique
#[derive(Debug, Clone)]
pub struct RateLimitBucket {
    /// Nom du bucket (endpoint)
    pub name: String,
    /// Limite de requêtes
    pub limit: u32,
    /// Requêtes restantes
    pub remaining: u32,
    /// Reset timestamp
    pub reset_at: Option<DateTime<Utc>>,
    /// Retry after (en secondes)
    pub retry_after: Option<u64>,
}

/// Statistiques du rate limiter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitStats {
    /// Total de requêtes effectuées
    pub total_requests: u64,
    /// Requêtes bloquées par rate limit
    pub blocked_requests: u64,
    /// Nombre de 429 reçus
    pub rate_limit_hits: u64,
    /// Requêtes en attente
    pub queued_requests: u32,
    /// Temps moyen d'attente (ms)
    pub avg_wait_time_ms: u64,
    /// Buckets actuels
    pub active_buckets: Vec<BucketInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BucketInfo {
    pub name: String,
    pub limit: u32,
    pub remaining: u32,
    pub reset_in_seconds: Option<i64>,
}

/// Événements du rate limiter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RateLimitEvent {
    /// Approche de la limite
    ApproachingLimit { bucket: String, percentage: f32 },
    /// Limite atteinte
    LimitReached { bucket: String, retry_after: u64 },
    /// 429 reçu de Discord
    RateLimitHit { bucket: String, retry_after: u64 },
    /// Bucket reset
    BucketReset { bucket: String },
    /// Requête retardée
    RequestDelayed { bucket: String, delay_ms: u64 },
}

/// Gestionnaire principal de rate limiting
pub struct RateLimiter {
    /// Configuration
    config: Arc<Mutex<RateLimitConfig>>,
    /// Buckets par endpoint
    buckets: Arc<Mutex<HashMap<String, RateLimitBucket>>>,
    /// Semaphore global
    global_semaphore: Arc<Semaphore>,
    /// Dernière requête globale
    last_request: Arc<Mutex<Instant>>,
    /// Statistiques
    stats: Arc<Mutex<RateLimitStats>>,
    /// File d'attente par priorité
    priority_queue: Arc<Mutex<Vec<QueuedRequest>>>,
}

#[derive(Debug)]
struct QueuedRequest {
    endpoint: String,
    priority: RequestPriority,
    created_at: Instant,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum RequestPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

impl RateLimiter {
    /// Créer un nouveau rate limiter
    pub fn new(config: RateLimitConfig) -> Self {
        let global_limit = config.global_rate_per_second as usize;
        
        Self {
            config: Arc::new(Mutex::new(config)),
            buckets: Arc::new(Mutex::new(HashMap::new())),
            global_semaphore: Arc::new(Semaphore::new(global_limit)),
            last_request: Arc::new(Mutex::new(Instant::now())),
            stats: Arc::new(Mutex::new(RateLimitStats {
                total_requests: 0,
                blocked_requests: 0,
                rate_limit_hits: 0,
                queued_requests: 0,
                avg_wait_time_ms: 0,
                active_buckets: Vec::new(),
            })),
            priority_queue: Arc::new(Mutex::new(Vec::new())),
        }
    }

    /// Vérifier et attendre si nécessaire avant une requête
    pub async fn check_rate_limit(
        &self,
        endpoint: &str,
        _priority: RequestPriority,
    ) -> Result<()> {
        let config = self.config.lock().await;
        
        if !config.enabled {
            return Ok(());
        }

        let start_time = Instant::now();

        // Vérifier le bucket spécifique
        let should_wait = {
            let mut buckets = self.buckets.lock().await;
            if let Some(bucket) = buckets.get_mut(endpoint) {
                if bucket.remaining == 0 {
                    if let Some(reset_at) = bucket.reset_at {
                        let now = Utc::now();
                        if now < reset_at {
                            let wait_time = (reset_at - now).num_milliseconds() as u64;
                            Some(wait_time)
                        } else {
                            // Reset le bucket
                            bucket.remaining = bucket.limit;
                            bucket.reset_at = None;
                            None
                        }
                    } else {
                        Some(1000) // Attendre 1 seconde par défaut
                    }
                } else {
                    bucket.remaining -= 1;
                    
                    // Vérifier le seuil d'alerte
                    let usage = 1.0 - (bucket.remaining as f32 / bucket.limit as f32);
                    if usage >= config.warning_threshold {
                        self.emit_event(RateLimitEvent::ApproachingLimit {
                            bucket: endpoint.to_string(),
                            percentage: usage * 100.0,
                        }).await;
                    }
                    
                    None
                }
            } else {
                None
            }
        };

        // Attendre si nécessaire
        if let Some(wait_ms) = should_wait {
            if config.debug_mode {
                tracing::debug!("Rate limit: attente {}ms pour {}", wait_ms, endpoint);
            }
            
            self.emit_event(RateLimitEvent::RequestDelayed {
                bucket: endpoint.to_string(),
                delay_ms: wait_ms,
            }).await;
            
            sleep(Duration::from_millis(wait_ms)).await;
        }

        // Vérifier le rate limit global
        let permit = self.global_semaphore.acquire().await?;
        
        // Respecter le délai minimum entre requêtes
        {
            let mut last_request = self.last_request.lock().await;
            let elapsed = last_request.elapsed().as_millis() as u64;
            
            if elapsed < config.min_delay_between_requests {
                let wait_time = config.min_delay_between_requests - elapsed;
                sleep(Duration::from_millis(wait_time)).await;
            }
            
            *last_request = Instant::now();
        }

        // Mettre à jour les stats
        {
            let mut stats = self.stats.lock().await;
            stats.total_requests += 1;
            
            let wait_time = start_time.elapsed().as_millis() as u64;
            stats.avg_wait_time_ms = (stats.avg_wait_time_ms + wait_time) / 2;
        }

        // Libérer le permit après un court délai (sans spawn pour éviter les lifetime issues)
        sleep(Duration::from_millis(100)).await;
        drop(permit);

        Ok(())
    }

    /// Mettre à jour les headers de rate limit depuis une réponse
    pub async fn update_from_headers(&self, endpoint: &str, headers: &reqwest::header::HeaderMap) {
        let mut buckets = self.buckets.lock().await;
        
        let bucket = buckets.entry(endpoint.to_string()).or_insert_with(|| {
            RateLimitBucket {
                name: endpoint.to_string(),
                limit: 1000, // Défaut
                remaining: 1000,
                reset_at: None,
                retry_after: None,
            }
        });

        // Parser les headers Discord
        if let Some(limit) = headers.get("x-ratelimit-limit") {
            if let Ok(limit_str) = limit.to_str() {
                if let Ok(limit_val) = limit_str.parse::<u32>() {
                    bucket.limit = limit_val;
                }
            }
        }

        if let Some(remaining) = headers.get("x-ratelimit-remaining") {
            if let Ok(remaining_str) = remaining.to_str() {
                if let Ok(remaining_val) = remaining_str.parse::<u32>() {
                    bucket.remaining = remaining_val;
                }
            }
        }

        if let Some(reset) = headers.get("x-ratelimit-reset") {
            if let Ok(reset_str) = reset.to_str() {
                if let Ok(reset_timestamp) = reset_str.parse::<i64>() {
                    bucket.reset_at = Some(DateTime::from_timestamp(reset_timestamp, 0).unwrap());
                }
            }
        }

        if let Some(retry_after) = headers.get("retry-after") {
            if let Ok(retry_str) = retry_after.to_str() {
                if let Ok(retry_val) = retry_str.parse::<u64>() {
                    bucket.retry_after = Some(retry_val);
                }
            }
        }
    }

    /// Gérer une erreur 429
    pub async fn handle_rate_limit_error(
        &self,
        endpoint: &str,
        retry_after_seconds: u64,
    ) -> Result<()> {
        let config = self.config.lock().await;
        
        // Mettre à jour le bucket
        {
            let mut buckets = self.buckets.lock().await;
            if let Some(bucket) = buckets.get_mut(endpoint) {
                bucket.remaining = 0;
                bucket.retry_after = Some(retry_after_seconds);
                bucket.reset_at = Some(Utc::now() + chrono::Duration::seconds(retry_after_seconds as i64));
            }
        }

        // Mettre à jour les stats
        {
            let mut stats = self.stats.lock().await;
            stats.rate_limit_hits += 1;
            stats.blocked_requests += 1;
        }

        self.emit_event(RateLimitEvent::RateLimitHit {
            bucket: endpoint.to_string(),
            retry_after: retry_after_seconds,
        }).await;

        // Calculer le backoff
        let mut backoff = retry_after_seconds;
        if config.backoff_multiplier > 1.0 {
            backoff = (backoff as f32 * config.backoff_multiplier) as u64;
            backoff = backoff.min(config.max_backoff_seconds);
        }

        if config.debug_mode {
            tracing::warn!(
                "Rate limit 429 sur {}: attente {}s (backoff: {}s)",
                endpoint, retry_after_seconds, backoff
            );
        }

        sleep(Duration::from_secs(backoff)).await;
        Ok(())
    }

    /// Obtenir les statistiques actuelles
    pub async fn get_stats(&self) -> RateLimitStats {
        let stats = self.stats.lock().await.clone();
        let buckets = self.buckets.lock().await;
        
        let mut stats_with_buckets = stats;
        stats_with_buckets.active_buckets = buckets.values().map(|b| {
            let reset_in = b.reset_at.map(|reset| {
                (reset - Utc::now()).num_seconds()
            });
            
            BucketInfo {
                name: b.name.clone(),
                limit: b.limit,
                remaining: b.remaining,
                reset_in_seconds: reset_in,
            }
        }).collect();

        stats_with_buckets
    }

    /// Réinitialiser un bucket spécifique
    pub async fn reset_bucket(&self, endpoint: &str) {
        let mut buckets = self.buckets.lock().await;
        if let Some(bucket) = buckets.get_mut(endpoint) {
            bucket.remaining = bucket.limit;
            bucket.reset_at = None;
            bucket.retry_after = None;
            
            self.emit_event(RateLimitEvent::BucketReset {
                bucket: endpoint.to_string(),
            }).await;
        }
    }

    /// Réinitialiser tous les buckets
    pub async fn reset_all_buckets(&self) {
        let mut buckets = self.buckets.lock().await;
        for bucket in buckets.values_mut() {
            bucket.remaining = bucket.limit;
            bucket.reset_at = None;
            bucket.retry_after = None;
        }
        
        let mut stats = self.stats.lock().await;
        stats.blocked_requests = 0;
        stats.rate_limit_hits = 0;
    }

    /// Obtenir la configuration actuelle
    pub async fn get_config(&self) -> Result<RateLimitConfig> {
        let config = self.config.lock().await;
        Ok(config.clone())
    }

    /// Mettre à jour la configuration
    pub async fn update_config(&self, new_config: RateLimitConfig) {
        let mut config = self.config.lock().await;
        *config = new_config;
    }

    /// Émettre un événement (pour notifications)
    async fn emit_event(&self, event: RateLimitEvent) {
        // TODO: Intégrer avec le système de notifications
        match &event {
            RateLimitEvent::ApproachingLimit { bucket, percentage } => {
                tracing::warn!("Rate limit: {} approche la limite ({}%)", bucket, percentage);
            },
            RateLimitEvent::LimitReached { bucket, retry_after } => {
                tracing::error!("Rate limit: {} limite atteinte, retry après {}s", bucket, retry_after);
            },
            RateLimitEvent::RateLimitHit { bucket, retry_after } => {
                tracing::error!("Rate limit 429: {} doit attendre {}s", bucket, retry_after);
            },
            _ => {}
        }
    }
}

/// Extension pour reqwest::Client avec rate limiting
#[async_trait::async_trait]
pub trait RateLimitedClient {
    async fn execute_with_rate_limit(
        &self,
        request: reqwest::Request,
        rate_limiter: &RateLimiter,
        priority: RequestPriority,
    ) -> Result<reqwest::Response>;
}

#[async_trait::async_trait]
impl RateLimitedClient for reqwest::Client {
    async fn execute_with_rate_limit(
        &self,
        request: reqwest::Request,
        rate_limiter: &RateLimiter,
        priority: RequestPriority,
    ) -> Result<reqwest::Response> {
        let endpoint = request.url().path().to_string();
        
        // Vérifier le rate limit avant la requête
        rate_limiter.check_rate_limit(&endpoint, priority).await?;
        
        // Exécuter la requête
        let response = self.execute(request).await?;
        
        // Mettre à jour depuis les headers
        rate_limiter.update_from_headers(&endpoint, response.headers()).await;
        
        // Gérer 429
        if response.status() == reqwest::StatusCode::TOO_MANY_REQUESTS {
            let retry_after = response
                .headers()
                .get("retry-after")
                .and_then(|v| v.to_str().ok())
                .and_then(|s| s.parse::<u64>().ok())
                .unwrap_or(60); // 60s par défaut
            
            rate_limiter.handle_rate_limit_error(&endpoint, retry_after).await?;
            return Err(anyhow::anyhow!("Rate limited: retry after {}s", retry_after));
        }
        
        Ok(response)
    }
}