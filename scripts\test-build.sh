#!/bin/bash

# Script de test rapide pour vérifier la configuration de build
# Teste la configuration sans faire le build complet

set -e

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${YELLOW}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DESKTOP_DIR="$PROJECT_ROOT/voidbot-desktop"
TAURI_DIR="$DESKTOP_DIR/src-tauri"

echo "🧪 Test de configuration de build VoidBot"
echo "=============================================="

# Test 1: Structure des fichiers
log_info "Test 1: Vérification de la structure des fichiers"

required_files=(
    "$DESKTOP_DIR/package.json"
    "$TAURI_DIR/tauri.conf.json"
    "$TAURI_DIR/tauri.prod.conf.json"
    "$TAURI_DIR/Cargo.toml"
    "$TAURI_DIR/src/lib.rs"
    "$TAURI_DIR/installer.nsi"
    "$TAURI_DIR/main.wxs"
    "$TAURI_DIR/entitlements.plist"
    "$TAURI_DIR/assets/voidbot.desktop"
    "$PROJECT_ROOT/LICENSE"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        log_success "$(basename "$file") existe"
    else
        log_error "$(basename "$file") manquant: $file"
    fi
done

# Test 2: Configuration package.json
log_info "Test 2: Scripts package.json"

cd "$DESKTOP_DIR"
if npm run build:installers --dry-run >/dev/null 2>&1; then
    log_success "Script build:installers configuré"
else
    log_error "Script build:installers non trouvé"
fi

# Test 3: Configuration Tauri
log_info "Test 3: Configuration Tauri"

if cd "$TAURI_DIR" && cargo check >/dev/null 2>&1; then
    log_success "Configuration Rust valide"
else
    log_error "Erreurs dans la configuration Rust"
fi

# Test 4: Icônes
log_info "Test 4: Icônes requises"

icon_files=(
    "$TAURI_DIR/icons/icon.ico"
    "$TAURI_DIR/icons/icon.icns"
    "$TAURI_DIR/icons/128x128.png"
    "$TAURI_DIR/icons/32x32.png"
)

for icon in "${icon_files[@]}"; do
    if [ -f "$icon" ]; then
        log_success "$(basename "$icon") existe"
    else
        log_error "$(basename "$icon") manquant"
    fi
done

# Test 5: Assets d'installeur
log_info "Test 5: Assets d'installeur"

# Créer des assets de placeholder si manquants
if [ ! -f "$TAURI_DIR/assets/header-image.bmp" ]; then
    log_info "Création d'asset placeholder header-image.bmp"
    # Créer un fichier BMP basique (1x1 pixel noir)
    printf '\x42\x4d\x3a\x00\x00\x00\x00\x00\x00\x00\x36\x00\x00\x00\x28\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x01\x00\x18\x00\x00\x00\x00\x00\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00' > "$TAURI_DIR/assets/header-image.bmp"
fi

if [ ! -f "$TAURI_DIR/assets/sidebar-image.bmp" ]; then
    log_info "Création d'asset placeholder sidebar-image.bmp"
    printf '\x42\x4d\x3a\x00\x00\x00\x00\x00\x00\x00\x36\x00\x00\x00\x28\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x01\x00\x18\x00\x00\x00\x00\x00\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00' > "$TAURI_DIR/assets/sidebar-image.bmp"
fi

# Test 6: Test de configuration JSON
log_info "Test 6: Validation JSON"

if jq empty "$TAURI_DIR/tauri.conf.json" >/dev/null 2>&1; then
    log_success "tauri.conf.json valide"
else
    log_error "tauri.conf.json invalide"
fi

if jq empty "$TAURI_DIR/tauri.prod.conf.json" >/dev/null 2>&1; then
    log_success "tauri.prod.conf.json valide"
else
    log_error "tauri.prod.conf.json invalide"
fi

# Test 7: Dépendances
log_info "Test 7: Dépendances"

cd "$DESKTOP_DIR"
if [ -d "node_modules" ]; then
    log_success "node_modules installé"
else
    log_error "node_modules manquant - exécuter 'npm install'"
fi

if command -v cargo >/dev/null 2>&1; then
    log_success "Cargo disponible"
else
    log_error "Cargo non trouvé"
fi

if npm list @tauri-apps/cli >/dev/null 2>&1; then
    log_success "Tauri CLI disponible"
else
    log_error "Tauri CLI manquant"
fi

echo ""
echo "🏁 Test terminé !"
echo ""
echo "💡 Pour lancer un build complet:"
echo "   cd voidbot-desktop && npm run build:installers"
echo ""
echo "🔧 Pour tester un build simple:"
echo "   cd voidbot-desktop && npm run tauri build"