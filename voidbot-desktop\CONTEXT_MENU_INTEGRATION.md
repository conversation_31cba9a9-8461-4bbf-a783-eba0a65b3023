# Menu Contextuel Intégré VoidBot

## Problème résolu

L'overlay transparent fullscreen causait des **crashes réguliers** de Tauri avec des écrans blancs qui apparaissaient/disparaissaient. Cette solution remplace l'overlay par un **menu contextuel intégré** directement dans l'application React.

## Nouvelle Architecture

### 1. **Composants React**
- `ContextMenu.tsx` - Menu contextuel principal
- `NotificationToast.tsx` - Système de notifications
- `useContextMenu.ts` - Hook pour gérer l'état du menu
- `ContextMenuExample.tsx` - Exemple d'utilisation

### 2. **Fonctionnalités Backend** (Rust)
- `translate_text()` - Traduction de texte
- `extract_emojis()` - Extraction d'emojis Discord
- Suppression des fonctions overlay problématiques

### 3. **Fonctionnalités Disponibles**
- ✅ **Copier les emojis** - Extrait et copie les emojis Discord
- ✅ **Traduire le texte** - Traduction en plusieurs langues
- ✅ **Copier le contenu** - Copie le texte du message
- ✅ **Copier l'ID du message** - Pour les développeurs
- ✅ **Copier le lien du message** - Lien direct Discord
- ✅ **Copier l'ID utilisateur** - ID de l'auteur
- ✅ **Date de création du compte** - Informations utilisateur

## Installation

### 1. Copier les fichiers
```bash
# Copier les composants
cp src/components/ContextMenu.tsx your_project/src/components/
cp src/components/NotificationToast.tsx your_project/src/components/
cp src/hooks/useContextMenu.ts your_project/src/hooks/
```

### 2. Installer les dépendances
```bash
npm install @tauri-apps/api
```

### 3. Intégrer dans votre composant
```tsx
import { ContextMenu } from './components/ContextMenu';
import { useContextMenu } from './hooks/useContextMenu';
import { useNotifications, NotificationToast } from './components/NotificationToast';

const YourComponent = () => {
  const { contextMenu, showContextMenu, hideContextMenu } = useContextMenu();
  const { notifications, removeNotification } = useNotifications();

  const handleRightClick = (e: React.MouseEvent) => {
    const messageData = {
      id: "message_id",
      content: "contenu du message",
      authorId: "user_id",
      timestamp: "timestamp",
      channelId: "channel_id",
      guildId: "guild_id"
    };
    
    showContextMenu(e, messageData);
  };

  return (
    <div>
      <div onContextMenu={handleRightClick}>
        Votre contenu ici
      </div>
      
      {contextMenu.isVisible && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          onClose={hideContextMenu}
          messageData={contextMenu.data}
        />
      )}
      
      {notifications.map((notification) => (
        <NotificationToast
          key={notification.id}
          message={notification.message}
          type={notification.type}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
};
```

## Avantages

### **Stabilité** 🔧
- ✅ Plus de crashes Tauri
- ✅ Plus d'écrans blancs
- ✅ Pas de conflit avec le gestionnaire de fenêtres

### **Performance** ⚡
- ✅ Pas de fenêtre overlay full-screen
- ✅ Rendu natif React
- ✅ Animations fluides

### **Fonctionnalités** 🚀
- ✅ Toutes les fonctionnalités de l'overlay
- ✅ Meilleure intégration UI/UX
- ✅ Notifications toast élégantes

### **Maintenance** 🛠️
- ✅ Code plus simple
- ✅ Moins de dépendances système
- ✅ Débogage facilité

## Personnalisation

### Ajouter une nouvelle action
```tsx
// Dans ContextMenu.tsx
const menuItems: MenuItem[] = [
  // ... autres items
  {
    id: 'custom-action',
    label: 'Mon action personnalisée',
    icon: '🎯',
    action: async () => {
      try {
        const result = await invoke('my_custom_command', { 
          param: messageData?.content 
        });
        await copyToClipboard(result as string, 'Action réussie');
      } catch (error) {
        showNotification('Erreur dans l\'action', 'error');
      }
    }
  }
];
```

### Personnaliser le style
```tsx
// Dans ContextMenu.tsx, modifier les classes Tailwind
className="fixed z-50 min-w-[280px] bg-your-color border border-your-border rounded-lg"
```

## Raccourcis Clavier

- **Ctrl+C** - Copier le contenu
- **Ctrl+T** - Traduire le texte
- **Ctrl+I** - Copier l'ID utilisateur
- **Escape** - Fermer le menu

## Commandes Backend Disponibles

```rust
// Traduction
translate_text(text: String, target_lang: String) -> Result<String, String>

// Extraction d'emojis
extract_emojis(text: String) -> Result<Vec<String>, String>

// Autres commandes Discord existantes
copy_user_id(user_id: String) -> Result<String, String>
get_user_creation_date(user_id: String) -> Result<String, String>
// ... etc
```

## Migration depuis l'overlay

1. **Supprimer** les fichiers overlay :
   - `overlay.html`
   - `context-menu.html`
   - Fonctions `create_overlay_window()` et `destroy_overlay_window()`

2. **Remplacer** par les composants React

3. **Tester** la stabilité - plus de crashes !

## Support

Ce système est **beaucoup plus stable** que l'overlay et offre une meilleure expérience utilisateur. Il respecte les principes de design de Tauri en évitant les fenêtres multiples problématiques.

Pour des questions ou des améliorations, référez-vous à la documentation Tauri officielle ou aux exemples fournis.
