/**
 * Centralized Error Handling System for VoidBot
 * Provides consistent error handling, user-friendly messages, and logging
 */

export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  DATABASE = 'database',
  DISCORD_API = 'discord_api',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface VoidError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code?: string;
  details?: any;
  timestamp: Date;
  context?: string;
  retryable?: boolean;
}

export class ErrorHandler {
  private static errorLog: VoidError[] = [];
  private static maxLogSize = 1000;

  /**
   * Créer une erreur VoidBot standardisée
   */
  static createError(
    type: ErrorType,
    severity: ErrorSeverity,
    message: string,
    userMessage: string,
    code?: string,
    details?: any,
    context?: string,
    retryable: boolean = false
  ): VoidError {
    const error: VoidError = {
      type,
      severity,
      message,
      userMessage,
      code,
      details,
      timestamp: new Date(),
      context,
      retryable
    };

    this.logError(error);
    return error;
  }

  /**
   * Traiter les erreurs Tauri avec messages utilisateur appropriés
   */
  static handleTauriError(error: any, context: string = 'Operation'): VoidError {
    console.error(`Tauri Error in ${context}:`, error);

    let errorType = ErrorType.UNKNOWN;
    let severity = ErrorSeverity.MEDIUM;
    let userMessage = 'Une erreur inattendue s\'est produite';
    let retryable = false;

    const errorString = String(error);

    // Classification des erreurs
    if (errorString.includes('Network') || errorString.includes('timeout') || errorString.includes('connection')) {
      errorType = ErrorType.NETWORK;
      userMessage = 'Erreur de réseau. Vérifiez votre connexion internet';
      retryable = true;
    } else if (errorString.includes('Unauthorized') || errorString.includes('token') || errorString.includes('401')) {
      errorType = ErrorType.AUTHENTICATION;
      userMessage = 'Erreur d\'authentification. Votre token Discord est peut-être invalide';
      severity = ErrorSeverity.HIGH;
    } else if (errorString.includes('Forbidden') || errorString.includes('403')) {
      errorType = ErrorType.PERMISSION;
      userMessage = 'Permissions insuffisantes pour cette action';
    } else if (errorString.includes('database') || errorString.includes('sql')) {
      errorType = ErrorType.DATABASE;
      userMessage = 'Erreur de base de données. Essayez de redémarrer l\'application';
      retryable = true;
    } else if (errorString.includes('Rate limit') || errorString.includes('429')) {
      errorType = ErrorType.DISCORD_API;
      userMessage = 'Limite de taux Discord atteinte. Réessayez dans quelques secondes';
      retryable = true;
    } else if (errorString.includes('validation') || errorString.includes('invalid')) {
      errorType = ErrorType.VALIDATION;
      userMessage = 'Données invalides. Vérifiez vos paramètres';
    }

    return this.createError(
      errorType,
      severity,
      errorString,
      userMessage,
      undefined,
      error,
      context,
      retryable
    );
  }

  /**
   * Wrapper pour les appels Tauri avec gestion d'erreur automatique
   */
  static async invokeTauriSafe<T>(
    command: string, 
    args?: any, 
    context?: string,
    retryAttempts: number = 0
  ): Promise<{ data?: T; error?: VoidError }> {
    const attemptInvoke = async (): Promise<{ data?: T; error?: VoidError }> => {
      try {
        const { invoke } = await import('@tauri-apps/api/core');
        const result = await invoke<T>(command, args);
        return { data: result };
      } catch (error) {
        const voidError = this.handleTauriError(error, context || command);
        return { error: voidError };
      }
    };

    let lastError: VoidError | undefined;
    
    // Tentative principale + retry si applicable
    for (let attempt = 0; attempt <= retryAttempts; attempt++) {
      const result = await attemptInvoke();
      
      if (result.data !== undefined) {
        return result;
      }
      
      lastError = result.error;
      
      // Si l'erreur n'est pas retryable ou si c'est la dernière tentative
      if (!result.error?.retryable || attempt === retryAttempts) {
        break;
      }
      
      // Délai exponentiel entre les tentatives
      const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    return { error: lastError };
  }

  /**
   * Validation des entrées utilisateur
   */
  static validateInput(value: any, rules: ValidationRule[]): VoidError | null {
    for (const rule of rules) {
      const result = rule.validate(value);
      if (!result.isValid) {
        return this.createError(
          ErrorType.VALIDATION,
          ErrorSeverity.LOW,
          `Validation failed: ${result.message}`,
          result.message,
          undefined,
          { value, rule: rule.name },
          'Input validation'
        );
      }
    }
    return null;
  }

  /**
   * Logger une erreur
   */
  private static logError(error: VoidError): void {
    this.errorLog.unshift(error);
    
    // Limiter la taille du log
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Log console selon la sévérité
    const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨', logMessage, error);
        break;
      case ErrorSeverity.HIGH:
        console.error('❌', logMessage, error);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️', logMessage, error);
        break;
      case ErrorSeverity.LOW:
        console.info('ℹ️', logMessage, error);
        break;
    }
  }

  /**
   * Obtenir les erreurs récentes
   */
  static getRecentErrors(limit: number = 50): VoidError[] {
    return this.errorLog.slice(0, limit);
  }

  /**
   * Effacer le log d'erreurs
   */
  static clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Obtenir les statistiques d'erreurs
   */
  static getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent24h: number;
  } {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const byType = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = 0;
      return acc;
    }, {} as Record<ErrorType, number>);
    
    const bySeverity = Object.values(ErrorSeverity).reduce((acc, severity) => {
      acc[severity] = 0;
      return acc;
    }, {} as Record<ErrorSeverity, number>);

    let recent24h = 0;

    this.errorLog.forEach(error => {
      byType[error.type]++;
      bySeverity[error.severity]++;
      if (error.timestamp > yesterday) {
        recent24h++;
      }
    });

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recent24h
    };
  }
}

/**
 * Règles de validation des entrées
 */
export interface ValidationRule {
  name: string;
  validate: (value: any) => { isValid: boolean; message: string };
}

export const ValidationRules = {
  required: (fieldName: string): ValidationRule => ({
    name: 'required',
    validate: (value) => ({
      isValid: value !== null && value !== undefined && String(value).trim() !== '',
      message: `${fieldName} est requis`
    })
  }),

  minLength: (fieldName: string, min: number): ValidationRule => ({
    name: 'minLength',
    validate: (value) => ({
      isValid: String(value).length >= min,
      message: `${fieldName} doit contenir au moins ${min} caractères`
    })
  }),

  maxLength: (fieldName: string, max: number): ValidationRule => ({
    name: 'maxLength',
    validate: (value) => ({
      isValid: String(value).length <= max,
      message: `${fieldName} ne peut pas dépasser ${max} caractères`
    })
  }),

  discordToken: (fieldName: string): ValidationRule => ({
    name: 'discordToken',
    validate: (value) => {
      const tokenRegex = /^[A-Za-z0-9._-]+$/;
      const isValid = typeof value === 'string' && 
                     value.length >= 50 && 
                     value.length <= 100 && 
                     tokenRegex.test(value);
      return {
        isValid,
        message: isValid ? '' : `${fieldName} doit être un token Discord valide`
      };
    }
  }),

  discordId: (fieldName: string): ValidationRule => ({
    name: 'discordId',
    validate: (value) => {
      const idRegex = /^\d{17,19}$/;
      return {
        isValid: idRegex.test(String(value)),
        message: `${fieldName} doit être un ID Discord valide (17-19 chiffres)`
      };
    }
  }),

  positiveNumber: (fieldName: string): ValidationRule => ({
    name: 'positiveNumber',
    validate: (value) => {
      const num = Number(value);
      return {
        isValid: !isNaN(num) && num > 0,
        message: `${fieldName} doit être un nombre positif`
      };
    }
  }),

  range: (fieldName: string, min: number, max: number): ValidationRule => ({
    name: 'range',
    validate: (value) => {
      const num = Number(value);
      return {
        isValid: !isNaN(num) && num >= min && num <= max,
        message: `${fieldName} doit être entre ${min} et ${max}`
      };
    }
  }),

  url: (fieldName: string): ValidationRule => ({
    name: 'url',
    validate: (value) => {
      try {
        new URL(String(value));
        return { isValid: true, message: '' };
      } catch {
        return { isValid: false, message: `${fieldName} doit être une URL valide` };
      }
    }
  })
};

/**
 * Hook React pour la gestion d'erreurs
 */
export function useErrorHandler() {
  const showError = (error: VoidError) => {
    // Intégration avec système de notification de l'app
    console.error('User Error:', error.userMessage);
    // TODO: Intégrer avec le système de toast/notification de l'UI
  };

  const handleAsync = async <T>(
    operation: () => Promise<T>,
    context?: string,
    retryAttempts: number = 0
  ): Promise<{ data?: T; error?: VoidError }> => {
    try {
      const data = await operation();
      return { data };
    } catch (err) {
      const error = ErrorHandler.handleTauriError(err, context);
      showError(error);
      
      if (error.retryable && retryAttempts > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return handleAsync(operation, context, retryAttempts - 1);
      }
      
      return { error };
    }
  };

  const validateAndExecute = async <T>(
    value: any,
    rules: ValidationRule[],
    operation: () => Promise<T>,
    context?: string
  ): Promise<{ data?: T; error?: VoidError }> => {
    const validationError = ErrorHandler.validateInput(value, rules);
    if (validationError) {
      showError(validationError);
      return { error: validationError };
    }

    return handleAsync(operation, context);
  };

  return {
    handleAsync,
    validateAndExecute,
    showError,
    invokeSafe: ErrorHandler.invokeTauriSafe
  };
}