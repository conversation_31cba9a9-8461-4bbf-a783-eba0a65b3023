use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use serenity::{
    model::prelude::*,
    prelude::*,
};
use tracing::{info, debug, warn};
use voidbot_shared::{
    ActivityViewer, ActivityEvent, ActivityEventType, ActivityEventData,
    UserStatus as VoidUserStatus, UserActivity, ActivityType as VoidActivityType,
    TrackedUser, VoiceState as VoidVoiceState, ActivityTimestamps, ActivityAssets, ActivityParty,
    create_status_change_event, create_activity_change_event
};

/// Tracker d'activité utilisateur pour le bot Discord
pub struct ActivityTracker {
    activity_viewer: Arc<Mutex<ActivityViewer>>,
    // Cache des états précédents pour détecter les changements
    previous_presences: Arc<Mutex<HashMap<UserId, Presence>>>,
    previous_voice_states: Arc<Mutex<HashMap<UserId, VoiceState>>>,
}

impl ActivityTracker {
    pub fn new() -> Self {
        Self {
            activity_viewer: Arc::new(Mutex::new(ActivityViewer::new())),
            previous_presences: Arc::new(Mutex::new(HashMap::new())),
            previous_voice_states: Arc::new(Mutex::new(HashMap::new())),
        }
    }
    
    /// Obtenir une référence à l'ActivityViewer
    pub fn get_activity_viewer(&self) -> Arc<Mutex<ActivityViewer>> {
        self.activity_viewer.clone()
    }
    
    /// Traiter un changement de présence utilisateur
    pub async fn handle_presence_update(&self, ctx: &Context, presence: &Presence) {
        let user_id = presence.user.id;
        
        // Vérifier si l'utilisateur est surveillé
        {
            let viewer = self.activity_viewer.lock().await;
            if !viewer.config.enabled || !viewer.tracked_users.contains_key(&user_id.to_string()) {
                return;
            }
        }
        
        // Obtenir l'ancien état de présence
        let previous_presence = {
            let mut prev = self.previous_presences.lock().await;
            prev.insert(user_id, presence.clone()).clone()
        };
        
        // Détecter les changements de statut
        if let Some(prev) = &previous_presence {
            let old_status = convert_discord_status(&prev.status);
            let new_status = convert_discord_status(&presence.status);
            
            if old_status != new_status {
                let event = create_status_change_event(
                    user_id.to_string(),
                    old_status,
                    new_status.clone(),
                    presence.guild_id.map(|id| id.to_string()),
                );
                
                self.record_event(event).await;
                debug!("Changement de statut détecté pour {}: {:?}", user_id, new_status);
            }
        }
        
        // Détecter les changements d'activité
        let old_activity = previous_presence
            .as_ref()
            .and_then(|p| p.activities.first())
            .and_then(|a| convert_discord_activity(a));
        
        let new_activity = presence.activities
            .first()
            .and_then(|a| convert_discord_activity(a));
        
        if old_activity != new_activity {
            let event = create_activity_change_event(
                user_id.to_string(),
                old_activity,
                new_activity,
                presence.guild_id.map(|id| id.to_string()),
            );
            
            self.record_event(event).await;
            debug!("Changement d'activité détecté pour {}", user_id);
        }
    }
    
    /// Traiter un changement d'état vocal
    pub async fn handle_voice_state_update(&self, ctx: &Context, voice_state: &VoiceState) {
        let user_id = voice_state.user_id;
        
        // Vérifier si l'utilisateur est surveillé
        {
            let viewer = self.activity_viewer.lock().await;
            if !viewer.config.enabled || !viewer.tracked_users.contains_key(&user_id.to_string()) {
                return;
            }
        }
        
        // Obtenir l'ancien état vocal
        let previous_voice = {
            let mut prev = self.previous_voice_states.lock().await;
            prev.insert(user_id, voice_state.clone()).clone()
        };
        
        let event_type = match (&previous_voice, &voice_state.channel_id) {
            (None, Some(_)) => ActivityEventType::VoiceJoin,
            (Some(prev), Some(_)) if prev.channel_id != voice_state.channel_id => ActivityEventType::VoiceMove,
            (Some(_), None) => ActivityEventType::VoiceLeave,
            _ => return, // Pas de changement significatif
        };
        
        let event = ActivityEvent {
            user_id: user_id.to_string(),
            event_type,
            timestamp: chrono::Utc::now(),
            guild_id: voice_state.guild_id.map(|id| id.to_string()),
            channel_id: voice_state.channel_id.map(|id| id.to_string()),
            data: ActivityEventData::VoiceChange {
                old_state: previous_voice.map(|vs| convert_voice_state(&vs)),
                new_state: voice_state.channel_id.map(|_| convert_voice_state(voice_state)),
            },
        };
        
        self.record_event(event).await;
        info!("Changement vocal détecté pour {} dans {:?}", user_id, voice_state.channel_id);
    }
    
    /// Traiter un événement de frappe
    pub async fn handle_typing_start(&self, ctx: &Context, event: &TypingStartEvent) {
        let user_id = event.user_id;
        
        // Vérifier si l'utilisateur est surveillé et si le tracking de frappe est activé
        {
            let viewer = self.activity_viewer.lock().await;
            if !viewer.config.enabled || !viewer.config.track_typing || !viewer.tracked_users.contains_key(&user_id.to_string()) {
                return;
            }
        }
        
        let activity_event = ActivityEvent {
            user_id: user_id.to_string(),
            event_type: ActivityEventType::TypingStart,
            timestamp: chrono::Utc::now(),
            guild_id: event.guild_id.map(|id| id.to_string()),
            channel_id: Some(event.channel_id.to_string()),
            data: ActivityEventData::TypingEvent {
                channel_id: event.channel_id.to_string(),
                guild_id: event.guild_id.map(|id| id.to_string()),
            },
        };
        
        self.record_event(activity_event).await;
        debug!("Frappe détectée pour {} dans {}", user_id, event.channel_id);
    }
    
    /// Traiter une mise à jour d'utilisateur
    pub async fn handle_user_update(&self, ctx: &Context, old_data: &Option<CurrentUser>, new_data: &CurrentUser) {
        // Ne tracker que si c'est un utilisateur surveillé
        {
            let viewer = self.activity_viewer.lock().await;
            if !viewer.config.enabled || !viewer.tracked_users.contains_key(&new_data.id.to_string()) {
                return;
            }
        }
        
        if let Some(old) = old_data {
            let event = ActivityEvent {
                user_id: new_data.id.to_string(),
                event_type: ActivityEventType::UserUpdate,
                timestamp: chrono::Utc::now(),
                guild_id: None,
                channel_id: None,
                data: ActivityEventData::UserUpdate {
                    old_username: Some(old.name.clone()),
                    new_username: Some(new_data.name.clone()),
                    old_discriminator: old.discriminator.map(|d| d.to_string()),
                    new_discriminator: new_data.discriminator.map(|d| d.to_string()),
                    old_avatar: old.avatar.map(|h| h.to_string()),
                    new_avatar: new_data.avatar.map(|h| h.to_string()),
                },
            };
            
            self.record_event(event).await;
            info!("Mise à jour utilisateur détectée pour {}", new_data.id);
        }
    }
    
    /// Enregistrer un événement d'activité
    async fn record_event(&self, event: ActivityEvent) {
        let mut viewer = self.activity_viewer.lock().await;
        viewer.record_activity_event(event);
    }
    
    /// Ajouter un utilisateur à surveiller
    pub async fn add_tracked_user(&self, user_id: UserId, user: &User, guilds_shared: Vec<String>) -> Result<(), String> {
        let mut viewer = self.activity_viewer.lock().await;
        viewer.add_tracked_user(
            user_id.to_string(),
            user.name.clone(),
            user.discriminator.map(|d| d.to_string()).unwrap_or_else(|| "0000".to_string()),
            user.avatar.map(|h| h.to_string()),
        )?;
        
        // Mettre à jour les guilds partagés
        if let Some(tracked) = viewer.tracked_users.get_mut(&user_id.to_string()) {
            tracked.guilds_shared = guilds_shared;
        }
        
        info!("Utilisateur {} ajouté à la surveillance", user.name);
        Ok(())
    }
    
    /// Supprimer un utilisateur surveillé
    pub async fn remove_tracked_user(&self, user_id: UserId) -> Result<(), String> {
        let mut viewer = self.activity_viewer.lock().await;
        viewer.remove_tracked_user(&user_id.to_string())?;
        
        // Nettoyer les caches
        {
            let mut prev_presences = self.previous_presences.lock().await;
            prev_presences.remove(&user_id);
        }
        {
            let mut prev_voice = self.previous_voice_states.lock().await;
            prev_voice.remove(&user_id);
        }
        
        info!("Utilisateur {} retiré de la surveillance", user_id);
        Ok(())
    }
    
    /// Obtenir la liste des utilisateurs surveillés
    pub async fn get_tracked_users(&self) -> Vec<TrackedUser> {
        let viewer = self.activity_viewer.lock().await;
        viewer.tracked_users.values().cloned().collect()
    }
    
    /// Nettoyer les utilisateurs inactifs
    pub async fn cleanup_inactive_users(&self) {
        let mut viewer = self.activity_viewer.lock().await;
        viewer.cleanup_inactive_users();
    }
}

/// Convertir le statut Discord vers notre enum
fn convert_discord_status(status: &OnlineStatus) -> VoidUserStatus {
    match status {
        OnlineStatus::Online => VoidUserStatus::Online,
        OnlineStatus::Idle => VoidUserStatus::Idle,
        OnlineStatus::DoNotDisturb => VoidUserStatus::DoNotDisturb,
        OnlineStatus::Invisible => VoidUserStatus::Invisible,
        OnlineStatus::Offline => VoidUserStatus::Offline,
        _ => VoidUserStatus::Offline,
    }
}

/// Convertir l'activité Discord vers notre struct
fn convert_discord_activity(activity: &Activity) -> Option<UserActivity> {
    let activity_type = match activity.kind {
        ActivityType::Playing => voidbot_shared::activity_viewer::ActivityType::Playing,
        ActivityType::Streaming => voidbot_shared::activity_viewer::ActivityType::Streaming,
        ActivityType::Listening => voidbot_shared::activity_viewer::ActivityType::Listening,
        ActivityType::Watching => voidbot_shared::activity_viewer::ActivityType::Watching,
        ActivityType::Custom => voidbot_shared::activity_viewer::ActivityType::Custom,
        ActivityType::Competing => voidbot_shared::activity_viewer::ActivityType::Competing,
        _ => return None,
    };
    
    Some(UserActivity {
        activity_type,
        name: activity.name.clone(),
        details: activity.details.clone(),
        state: activity.state.clone(),
        timestamps: activity.timestamps.as_ref().map(|ts| voidbot_shared::activity_viewer::ActivityTimestamps {
            start: ts.start.map(|t| chrono::DateTime::from_timestamp(t as i64, 0).unwrap_or(chrono::Utc::now())),
            end: ts.end.map(|t| chrono::DateTime::from_timestamp(t as i64, 0).unwrap_or(chrono::Utc::now())),
        }),
        assets: activity.assets.as_ref().map(|assets| voidbot_shared::activity_viewer::ActivityAssets {
            large_image: assets.large_image.clone(),
            large_text: assets.large_text.clone(),
            small_image: assets.small_image.clone(),
            small_text: assets.small_text.clone(),
        }),
        party: activity.party.as_ref().map(|party| voidbot_shared::activity_viewer::ActivityParty {
            id: party.id.clone(),
            size: party.size.map(|s| (s[0], s[1])),
        }),
        buttons: activity.buttons.iter().map(|b| b.label.clone()).collect(),
    })
}

/// Convertir l'état vocal Discord vers notre struct
fn convert_voice_state(voice: &VoiceState) -> VoidVoiceState {
    VoidVoiceState {
        guild_id: voice.guild_id.map(|id| id.to_string()),
        channel_id: voice.channel_id.map(|id| id.to_string()),
        self_mute: voice.self_mute,
        self_deaf: voice.self_deaf,
        server_mute: voice.mute,
        server_deaf: voice.deaf,
        streaming: voice.self_stream.unwrap_or(false),
        video: voice.self_video,
    }
}