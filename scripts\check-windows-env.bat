@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo.
echo 🔍 VoidBot - Vérification Environnement Windows
echo ===============================================
echo.

REM Variables
set "PASS=0"
set "FAIL=0"

echo ✅ Vérifications système:
echo.

REM Vérifier Windows
echo    OS: %OS%
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo    Version: Windows %VERSION%
echo.

REM Vérifier Node.js
echo 📦 Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo    ✅ Node.js !NODE_VERSION! installé
    set /a PASS+=1
) else (
    echo    ❌ Node.js NON installé
    echo       Télécharger: https://nodejs.org
    set /a FAIL+=1
)

REM Vérifier npm
echo 📦 npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo    ✅ npm !NPM_VERSION! installé
    set /a PASS+=1
) else (
    echo    ❌ npm NON installé
    set /a FAIL+=1
)

REM Vérifier Rust
echo 🦀 Rust/Cargo...
cargo --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('cargo --version') do set CARGO_VERSION=%%i
    echo    ✅ Cargo !CARGO_VERSION! installé
    set /a PASS+=1
) else (
    echo    ❌ Rust/Cargo NON installé
    echo       Télécharger: https://rustup.rs
    set /a FAIL+=1
)

REM Vérifier rustup
echo 🦀 Rustup...
rustup --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('rustup --version') do set RUSTUP_VERSION=%%i
    echo    ✅ Rustup !RUSTUP_VERSION! installé
    set /a PASS+=1
) else (
    echo    ❌ Rustup NON installé
    set /a FAIL+=1
)

REM Vérifier Python (optionnel)
echo 🐍 Python (optionnel)...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo    ✅ Python !PYTHON_VERSION! installé
    set /a PASS+=1
) else (
    echo    ⚠️  Python NON installé (optionnel pour assets)
)

REM Vérifier Git
echo 📚 Git...
git --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=3" %%i in ('git --version') do set GIT_VERSION=%%i
    echo    ✅ Git !GIT_VERSION! installé
    set /a PASS+=1
) else (
    echo    ❌ Git NON installé
    echo       Télécharger: https://git-scm.com
    set /a FAIL+=1
)

echo.
echo 📊 Résumé:
echo    ✅ Composants OK: %PASS%
echo    ❌ Manquants: %FAIL%
echo.

if %FAIL% gtr 0 (
    echo ❌ ENVIRONNEMENT INCOMPLET
    echo    Installer les composants manquants avant de continuer
    echo.
    echo 📋 Instructions d'installation:
    echo    1. Node.js: https://nodejs.org ^(LTS recommandé^)
    echo    2. Rust: https://rustup.rs ^(stable^)
    echo    3. Git: https://git-scm.com
    echo    4. Python: https://python.org ^(optionnel^)
    echo.
) else (
    echo ✅ ENVIRONNEMENT PRÊT POUR LE BUILD !
    echo    Vous pouvez maintenant exécuter:
    echo    - build-windows.bat
    echo    - build-windows.ps1
    echo.
)

echo 🔧 Prochaines étapes:
echo    1. Naviguer vers le dossier VoidBot
echo    2. Exécuter: scripts\build-windows.bat
echo    3. Attendre la génération des installeurs
echo    4. Récupérer les fichiers dans builds\windows\
echo.

echo Appuyer sur une touche pour fermer...
pause >nul