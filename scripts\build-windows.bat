@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

REM ================================================================
REM Script de build Windows pour VoidBot v1.0
REM Génère installeurs MSI et NSIS pour Windows x64 et x86
REM ================================================================

echo.
echo 🚀 VoidBot - Build Windows Automatisé
echo =====================================
echo.

REM Variables de configuration
set "PROJECT_ROOT=%~dp0.."
set "DESKTOP_DIR=%PROJECT_ROOT%\voidbot-desktop"
set "BUILD_DIR=%PROJECT_ROOT%\builds\windows"
set "LOG_FILE=%PROJECT_ROOT%\build-windows.log"

REM Couleurs (simulation)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Rediriger les logs
echo [%date% %time%] Début du build Windows > "%LOG_FILE%"

echo %BLUE%ℹ️ Vérification de l'environnement...%NC%

REM Vérifier Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Node.js non trouvé%NC%
    echo    Installer Node.js depuis: https://nodejs.org
    pause
    exit /b 1
)

REM Vérifier npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ npm non trouvé%NC%
    pause
    exit /b 1
)

REM Vérifier Rust
cargo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Rust/Cargo non trouvé%NC%
    echo    Installer Rust depuis: https://rustup.rs
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
for /f "tokens=*" %%i in ('cargo --version') do set CARGO_VERSION=%%i

echo %GREEN%✅ Environnement prêt:%NC%
echo    Node.js: %NODE_VERSION%
echo    npm: %NPM_VERSION%
echo    Cargo: %CARGO_VERSION%
echo.

REM Aller dans le dossier desktop
cd /d "%DESKTOP_DIR%"
if %errorlevel% neq 0 (
    echo %RED%❌ Dossier voidbot-desktop non trouvé%NC%
    pause
    exit /b 1
)

echo %BLUE%📦 Installation des dépendances...%NC%

REM Installer dépendances npm si nécessaire
if not exist "node_modules" (
    echo    Installation npm en cours...
    npm install
    if %errorlevel% neq 0 (
        echo %RED%❌ Erreur installation npm%NC%
        pause
        exit /b 1
    )
) else (
    echo    Dépendances npm déjà installées
)

echo %GREEN%✅ Dépendances prêtes%NC%
echo.

REM Installer cibles Windows Rust
echo %BLUE%🔧 Configuration des cibles Rust...%NC%
rustup target add x86_64-pc-windows-msvc >nul 2>&1
rustup target add i686-pc-windows-msvc >nul 2>&1
echo %GREEN%✅ Cibles Windows installées%NC%
echo.

REM Créer dossier de build
if not exist "%BUILD_DIR%" (
    mkdir "%BUILD_DIR%"
)

echo %BLUE%🏗️ Génération des assets...%NC%
cd src-tauri
if exist "build-assets.py" (
    python build-assets.py
    if %errorlevel% neq 0 (
        echo %YELLOW%⚠️ Erreur génération assets, continuation...%NC%
    ) else (
        echo %GREEN%✅ Assets générés%NC%
    )
) else (
    echo %YELLOW%⚠️ Script build-assets.py non trouvé%NC%
)
cd ..
echo.

echo %BLUE%🔨 Build Frontend React...%NC%
npm run build:prod
if %errorlevel% neq 0 (
    echo %RED%❌ Erreur build frontend%NC%
    pause
    exit /b 1
)
echo %GREEN%✅ Frontend buildé%NC%
echo.

echo %BLUE%🏗️ Build Windows x64 (MSI + NSIS)...%NC%
echo    Ceci peut prendre 5-15 minutes...
npx tauri build --target x86_64-pc-windows-msvc --bundles msi,nsis
if %errorlevel% neq 0 (
    echo %RED%❌ Erreur build Windows x64%NC%
    echo    Vérifier les logs dans le dossier src-tauri/target
    pause
    exit /b 1
)
echo %GREEN%✅ Build Windows x64 terminé%NC%
echo.

echo %BLUE%🏗️ Build Windows x86 (MSI + NSIS)...%NC%
echo    Ceci peut prendre 5-15 minutes...
npx tauri build --target i686-pc-windows-msvc --bundles msi,nsis
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️ Erreur build Windows x86, continuation...%NC%
) else (
    echo %GREEN%✅ Build Windows x86 terminé%NC%
)
echo.

REM Copier les artefacts
echo %BLUE%📋 Copie des installeurs...%NC%

set "TARGET_DIR=src-tauri\target"
set "RELEASE_DIR_X64=%TARGET_DIR%\x86_64-pc-windows-msvc\release\bundle"
set "RELEASE_DIR_X86=%TARGET_DIR%\i686-pc-windows-msvc\release\bundle"

REM Copier installeurs x64
if exist "%RELEASE_DIR_X64%\msi" (
    xcopy "%RELEASE_DIR_X64%\msi\*.msi" "%BUILD_DIR%\" /Y >nul
    echo    ✅ MSI x64 copié
)
if exist "%RELEASE_DIR_X64%\nsis" (
    xcopy "%RELEASE_DIR_X64%\nsis\*.exe" "%BUILD_DIR%\" /Y >nul
    echo    ✅ NSIS x64 copié
)

REM Copier installeurs x86
if exist "%RELEASE_DIR_X86%\msi" (
    xcopy "%RELEASE_DIR_X86%\msi\*.msi" "%BUILD_DIR%\" /Y >nul
    echo    ✅ MSI x86 copié
)
if exist "%RELEASE_DIR_X86%\nsis" (
    xcopy "%RELEASE_DIR_X86%\nsis\*.exe" "%BUILD_DIR%\" /Y >nul
    echo    ✅ NSIS x86 copié
)

echo.

REM Résumé final
echo %GREEN%🎉 BUILD WINDOWS TERMINÉ !%NC%
echo ========================
echo.
echo 📁 Installeurs disponibles dans:
echo    %BUILD_DIR%
echo.
echo 📦 Fichiers générés:
dir "%BUILD_DIR%" /B 2>nul
echo.
echo 🚀 Prêt pour distribution !
echo.

REM Log final
echo [%date% %time%] Build Windows terminé avec succès >> "%LOG_FILE%"

echo Appuyer sur une touche pour fermer...
pause >nul