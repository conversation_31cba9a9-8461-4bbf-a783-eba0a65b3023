# 🔐 Authentification VoidBot

Guide complet pour comprendre et configurer l'authentification Discord dans VoidBot.

## 🎯 Type d'authentification

VoidBot utilise votre **token utilisateur Discord** (selfbot), pas un token de bot classique.

### ❌ **Pas un bot Discord classique**
- Pas de Client ID/Secret
- Pas d'OAuth2 flow
- Pas de permissions d'application
- Pas de page développeur Discord

### ✅ **Selfbot avec token utilisateur**
- Utilise directement votre session Discord
- Agit comme votre compte personnel
- Mêmes permissions que votre compte

## 🔑 Récupération du token utilisateur

### Méthode 1 : Navigateur (Recommandée)

1. **Ouvrir Discord dans le navigateur**
   ```
   https://discord.com/app
   ```

2. **Ouvrir les outils développeur**
   - `F12` ou `Ctrl+Shift+I`
   - Ou clic droit → "Inspecter l'élément"

3. **Aller dans l'onglet Network**
   - Cliquer sur "Network" (Réseau)
   - Ra<PERSON><PERSON><PERSON><PERSON> la page (`F5`)

4. **Trouver une requête API**
   - Chercher une requête vers `discord.com/api`
   - Cliquer dessus pour voir les détails

5. **Copier le token**
   - Dans "Headers" → "Request Headers"
   - Ligne `Authorization: mfa.XXX...` ou `Authorization: MTg4...`
   - Copier tout après `Authorization: `

### Méthode 2 : Console développeur

1. **Ouvrir Discord dans le navigateur**
2. **Ouvrir la console** (`F12` → Console)
3. **Exécuter ce code** :
   ```javascript
   window.webpackChunkdiscord_app.push([
     [Math.random()],
     {},
     req => {
       for (const m of Object.keys(req.c)
         .map(x => req.c[x].exports)
         .filter(x => x)) {
         if (m.default && m.default.getToken !== undefined) {
           return copy(m.default.getToken());
         }
         if (m.getToken !== undefined) {
           return copy(m.getToken());
         }
       }
     }
   ]);
   console.log("Token copié dans le presse-papier!");
   ```

## 🔒 Format des tokens

### Token standard (sans 2FA)
```
YOUR_USER_TOKEN_HERE.XXXXX.XXXXXXXXXXXXXXXXXXXXXXX
```
- Commence par des lettres/chiffres
- Contient des points
- Environ 59 caractères

### Token avec 2FA (recommandé)
```
mfa.VkO_2G4NRg0XuBdqLsHsEyVTNjlWpAb_q6y3-jlE-2G4NRg0XuBdqLsHsEyVTNjlWpAb
```
- Commence par `mfa.`
- Plus long (80+ caractères)
- Plus sécurisé

## 🏗️ Implémentation dans VoidBot

### Backend Tauri (Rust)

```rust
// Validation du format
fn is_valid_user_token(token: &str) -> bool {
    if token.starts_with("mfa.") {
        token.len() > 80  // Token 2FA
    } else {
        token.len() > 50 && !token.contains(' ')  // Token standard
    }
}

// Test avec l'API Discord
async fn validate_discord_token(token: &str) -> Result<DiscordUser, String> {
    let response = client
        .get("https://discord.com/api/v10/users/@me")
        .header("Authorization", token)
        .header("User-Agent", "VoidBot/1.0")
        .send()
        .await?;
    
    if response.status().is_success() {
        Ok(response.json::<DiscordUser>().await?)
    } else {
        Err("Token invalide".to_string())
    }
}
```

### Frontend React

```typescript
// Dans le store Zustand
connectBot: async (token: string) => {
  try {
    setBotStatus('connecting');
    
    // Appel Tauri pour validation
    const result = await invoke('connect_discord_bot', { token });
    
    setBotStatus('online');
    setIsConnected(true);
    
    console.log(result); // "Selfbot connecté : Username#1234"
  } catch (error) {
    setBotStatus('error');
    throw new Error(`Échec connexion : ${error}`);
  }
}
```

## 🛡️ Sécurité et bonnes pratiques

### ✅ **À faire**
- **Stocker le token de façon sécurisée** (keychain OS)
- **Vérifier périodiquement** la validité
- **Gérer l'expiration** gracieusement
- **Rate limiting** sur les requêtes API
- **Logs sécurisés** (pas de token dans les logs)

### ❌ **À éviter**
- Stocker en plain text
- Partager le token
- Commit dans Git
- Logs non filtrés

### Code sécurisé

```rust
// ✅ Bon - Token masqué dans les logs
tracing::info!("Connexion selfbot pour utilisateur: {}", user.username);

// ❌ Mauvais - Token exposé
tracing::info!("Token utilisé: {}", token);
```

## 📱 Gestion des erreurs

### Codes d'erreur API Discord

| Code | Signification | Action |
|------|---------------|--------|
| `401` | Token invalide/expiré | Demander nouveau token |
| `403` | Non autorisé | Vérifier permissions compte |
| `429` | Rate limited | Attendre et réessayer |
| `5xx` | Erreur serveur Discord | Réessayer plus tard |

### Messages utilisateur (français)

```rust
match response.status().as_u16() {
    401 => "Token invalide ou expiré. Veuillez vérifier votre token.",
    403 => "Accès refusé. Vérifiez les permissions de votre compte.",
    429 => "Trop de tentatives. Patientez quelques minutes.",
    _ => "Erreur de connexion Discord. Réessayez plus tard."
}
```

## 🔄 Flux d'authentification complet

```mermaid
sequenceDiagram
    participant U as Utilisateur
    participant F as Frontend React
    participant T as Backend Tauri
    participant D as Discord API

    U->>F: Saisit token utilisateur
    F->>T: invoke('connect_discord_bot', {token})
    T->>T: Validation format token
    T->>D: GET /users/@me (avec token)
    D->>T: UserInfo ou Erreur
    T->>F: Résultat connexion
    F->>U: Affichage statut + info user
```

## 🧪 Tests de l'authentification

### Test manuel
1. Récupérer votre token Discord
2. Lancer VoidBot desktop (`npm run tauri dev`)
3. Coller le token dans le champ
4. Cliquer "Connecter Selfbot"
5. Vérifier l'affichage de votre username

### Test avec token invalide
- Token trop court → "Format invalide"
- Token expiré → "Token expiré" 
- Token malformé → "Erreur parsing"

## 📞 Dépannage

### Problème : "Token invalide"
- Vérifier que c'est un token **utilisateur**, pas bot
- Re-récupérer le token (il peut expirer)
- Vérifier qu'il n'y a pas d'espaces

### Problème : "Accès refusé"
- Compte Discord suspendu/banni ?
- Vérifier dans le navigateur que Discord fonctionne

### Problème : "Erreur réseau"
- Vérifier la connexion internet
- Firewall qui bloque VoidBot ?
- Discord API en maintenance ?

---

**⚠️ Avertissement légal** : Les selfbots peuvent violer les ToS Discord. Utilisez VoidBot de façon responsable et à vos propres risques.