use anyhow::Result;
use serenity::{
    model::application::CommandInteraction,
    prelude::*,
};
use crate::commands::{animate::*, backup::*, general::*, images::*, rpc::*, snipe::*, troll::*};

pub async fn handle_slash_command(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    match command.data.name.as_str() {
        // General commands
        "ping" => handle_ping(ctx, command).await?,
        "help" => handle_help(ctx, command).await?,
        "settings" => handle_settings(ctx, command).await?,

        // Animation commands
        "animate" => handle_animate(ctx, command).await?,
        "animate_stop" => handle_animate_stop(ctx, command).await?,

        // Rich Presence commands
        "rpc" => handle_rpc(ctx, command).await?,
        "rpc_presets" => handle_rpc_presets(ctx, command).await?,

        // Backup & Restore commands
        "backup" => handle_backup(ctx, command).await?,
        "restore" => handle_restore(ctx, command).await?,
        "clone" => handle_clone(ctx, command).await?,

        // Utility commands
        "snipe" => handle_snipe(ctx, command).await?,
        "clear" => handle_clear(ctx, command).await?,

        // Fun commands
        "8ball" => handle_8ball(ctx, command).await?,
        "coinflip" => handle_coinflip(ctx, command).await?,
        "dice" => handle_dice(ctx, command).await?,

        // Tools
        "translate" => handle_translate(ctx, command).await?,
        "calculate" => handle_calculate(ctx, command).await?,

        // Info commands
        "server_info" => handle_server_info(ctx, command).await?,
        "user_info" => handle_user_info(ctx, command).await?,

        // Sniper commands
        "nitro_sniper" => handle_nitro_sniper(ctx, command).await?,
        "giveaway_sniper" => handle_giveaway_sniper(ctx, command).await?,

        // Troll commands
        "troll" => {
            let data = ctx.data.read().await;
            let bot_data = data.get::<crate::BotData>().unwrap();
            let stealth = bot_data.stealth_manager.lock().await;
            let stealth_mode = stealth.get_mode();
            drop(stealth);
            
            handle_troll_command(ctx, command, &bot_data.troll_manager, &stealth_mode).await?;
        }

        // Image commands
        "meme" | "avatar_overlay" | "banner" | "reaction" | "user_stats" => {
            handle_image_commands(ctx, command).await?;
        }

        _ => {
            // Unknown command
            let data = ctx.data.read().await;
            let bot_data = data.get::<crate::BotData>().unwrap();
            let stealth = bot_data.stealth_manager.lock().await;
            
            stealth.send_response(ctx, command, "❌ Unknown command. Use `/help` to see available commands.", true).await?;
        }
    }

    Ok(())
}

// Placeholder implementations for missing commands
async fn handle_8ball(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let responses = [
        "🎱 It is certain",
        "🎱 Reply hazy, try again",
        "🎱 Don't count on it",
        "🎱 Yes definitely",
        "🎱 My sources say no",
        "🎱 As I see it, yes",
        "🎱 Concentrate and ask again",
        "🎱 Outlook not so good",
    ];

    let response = responses[fastrand::usize(..responses.len())];
    stealth.send_response(ctx, command, response, false).await?;
    Ok(())
}

async fn handle_coinflip(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let result = if fastrand::bool() { "🪙 Heads!" } else { "🪙 Tails!" };
    stealth.send_response(ctx, command, result, false).await?;
    Ok(())
}

async fn handle_dice(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    let sides = command
        .data
        .options
        .iter()
        .find(|opt| opt.name == "sides")
        .and_then(|opt| opt.value.as_i64())
        .unwrap_or(6) as u32;

    let result = fastrand::u32(1..=sides);
    let content = format!("🎲 You rolled a **{}** (1-{})", result, sides);
    
    stealth.send_response(ctx, command, content, false).await?;
    Ok(())
}

async fn handle_translate(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    stealth.send_response(ctx, command, "🌐 Translation feature is currently in development.", false).await?;
    Ok(())
}

async fn handle_calculate(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    stealth.send_response(ctx, command, "🧮 Calculator feature is currently in development.", false).await?;
    Ok(())
}

async fn handle_server_info(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    stealth.send_response(ctx, command, "ℹ️ Server info feature is currently in development.", false).await?;
    Ok(())
}

async fn handle_user_info(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    stealth.send_response(ctx, command, "ℹ️ User info feature is currently in development.", false).await?;
    Ok(())
}

async fn handle_nitro_sniper(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    stealth.send_response(ctx, command, "💎 Nitro sniper feature is currently in development.", true).await?;
    Ok(())
}

async fn handle_giveaway_sniper(ctx: &Context, command: &CommandInteraction) -> Result<()> {
    let data = ctx.data.read().await;
    let bot_data = data.get::<crate::BotData>().unwrap();
    let stealth = bot_data.stealth_manager.lock().await;

    stealth.send_response(ctx, command, "🎁 Giveaway sniper feature is currently in development.", true).await?;
    Ok(())
}