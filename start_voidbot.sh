#!/bin/bash

# 🚀 Script de lancement VoidBot Desktop

echo "🎮 Démarrage de VoidBot Desktop..."
echo ""

cd /mnt/c/Users/<USER>/Desktop/VoidBot/voidbot-desktop

# Vérifier si les dépendances Node sont installées
if [ ! -d "node_modules" ]; then
    echo "📦 Installation des dépendances Node.js..."
    npm install
fi

# Lancer VoidBot
echo "🔥 Lancement de VoidBot en mode développement..."
echo "   Frontend: http://localhost:5173/"
echo "   Backend: Tauri + Rust compiling..."
echo ""
echo "💡 Attendre la compilation Tauri (~2-3 minutes première fois)"
echo "   Puis l'app desktop s'ouvrira automatiquement !"
echo ""

npm run tauri dev