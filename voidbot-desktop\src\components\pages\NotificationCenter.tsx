import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  BellOff, 
  X, 
  Trash2, 
  <PERSON><PERSON><PERSON>, 
  Check, 
  CheckCheck,
  Filter,
  Search,
  Volume2,
  VolumeX
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

interface VoidNotification {
  id: string;
  event_type: string;
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  timestamp: string;
  guild_id?: string;
  channel_id?: string;
  user_id?: string;
  metadata: Record<string, string>;
  read: boolean;
}

interface NotificationConfig {
  enabled_events: string[];
  keywords: string[];
  webhook_urls: string[];
  desktop_notifications: boolean;
  sound_enabled: boolean;
  sound_volume: number;
  history_retention_days: number;
  monitored_channels: string[];
  monitored_users: string[];
}

const NotificationEventTypeLabels: Record<string, string> = {
  Ghostping: 'Ghostping',
  FriendRemoved: 'Ami retiré',
  FriendBlocked: 'Ami bloqué',
  ServerKicked: 'Expulsé du serveur',
  ServerBanned: 'Banni du serveur',
  RoleChanged: 'Rôle modifié',
  NicknameChanged: 'Pseudo modifié',
  KeywordDetected: 'Mot-clé détecté',
  TicketReplied: 'Ticket répondu',
  UserTyping: 'Utilisateur tape',
  GiveawayDetected: 'Giveaway détecté',
  NitroSniped: 'Nitro récupéré',
  ChannelMessage: 'Message canal',
  Mentioned: 'Mentionné',
};

const NotificationIcons: Record<string, string> = {
  Ghostping: '👻',
  FriendRemoved: '💔',
  FriendBlocked: '🚫',
  ServerKicked: '🦵',
  ServerBanned: '🔨',
  RoleChanged: '🎭',
  NicknameChanged: '📝',
  KeywordDetected: '🔍',
  TicketReplied: '🎫',
  UserTyping: '⌨️',
  GiveawayDetected: '🎁',
  NitroSniped: '💎',
  ChannelMessage: '💬',
  Mentioned: '📢',
};

const PriorityColors: Record<string, string> = {
  Low: 'border-green-500 bg-green-500/10',
  Medium: 'border-yellow-500 bg-yellow-500/10',
  High: 'border-red-500 bg-red-500/10',
  Critical: 'border-red-600 bg-red-600/20',
};

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<VoidNotification[]>([]);
  const [config, setConfig] = useState<NotificationConfig | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [filterType, setFilterType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadNotifications();
    loadConfig();
    
    // Écouter les nouvelles notifications en temps réel
    const unlisten = listen<VoidNotification>('notification-added', (event) => {
      setNotifications(prev => [event.payload, ...prev]);
      
      // Notification desktop si activée
      if (config?.desktop_notifications) {
        showDesktopNotification(event.payload);
      }
    });

    return () => {
      unlisten.then(fn => fn());
    };
  }, [config?.desktop_notifications]);

  const loadNotifications = async () => {
    try {
      const notifs = await invoke<VoidNotification[]>('get_notifications');
      setNotifications(notifs);
    } catch (error) {
      console.error('Erreur chargement notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadConfig = async () => {
    try {
      const cfg = await invoke<NotificationConfig>('get_notification_config');
      setConfig(cfg);
    } catch (error) {
      console.error('Erreur chargement config notifications:', error);
    }
  };

  const showDesktopNotification = (notification: VoidNotification) => {
    if (Notification.permission === 'granted') {
      new Notification(`VoidBot - ${notification.title}`, {
        body: notification.description,
        icon: '/tauri.svg',
        tag: notification.id,
      });
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await invoke('mark_notification_as_read', { notificationId });
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
    } catch (error) {
      console.error('Erreur marquage notification comme lue:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await invoke('mark_all_notifications_as_read');
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    } catch (error) {
      console.error('Erreur marquage toutes notifications comme lues:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      await invoke('delete_notification', { notificationId });
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Erreur suppression notification:', error);
    }
  };

  const clearAllNotifications = async () => {
    try {
      await invoke('clear_all_notifications');
      setNotifications([]);
    } catch (error) {
      console.error('Erreur suppression toutes notifications:', error);
    }
  };

  const updateConfig = async (newConfig: Partial<NotificationConfig>) => {
    try {
      const updatedConfig = { ...config, ...newConfig } as NotificationConfig;
      await invoke('update_notification_config', { config: updatedConfig });
      setConfig(updatedConfig);
    } catch (error) {
      console.error('Erreur mise à jour config:', error);
    }
  };

  const requestNotificationPermission = async () => {
    if (Notification.permission === 'default') {
      await Notification.requestPermission();
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesFilter = filterType === 'all' || 
      filterType === 'unread' && !notification.read ||
      filterType === notification.event_type;

    const matchesSearch = searchQuery === '' || 
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'À l\'instant';
    if (diffMins < 60) return `${diffMins}m`;
    if (diffHours < 24) return `${diffHours}h`;
    return `${diffDays}j`;
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400">Chargement des notifications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Bell className="w-6 h-6 text-purple-500" />
            <h1 className="text-2xl font-bold text-white">Centre de Notifications</h1>
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={markAllAsRead}
            className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm flex items-center space-x-1 transition-colors"
            disabled={unreadCount === 0}
          >
            <CheckCheck className="w-4 h-4" />
            <span>Tout marquer lu</span>
          </button>

          <button
            onClick={clearAllNotifications}
            className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm flex items-center space-x-1 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
            <span>Tout supprimer</span>
          </button>

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm flex items-center space-x-1 transition-colors"
          >
            <Settings className="w-4 h-4" />
            <span>Paramètres</span>
          </button>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white text-sm"
          >
            <option value="all">Toutes</option>
            <option value="unread">Non lues</option>
            <option value="Ghostping">Ghostpings</option>
            <option value="FriendRemoved">Amis retirés</option>
            <option value="ServerBanned">Bannissements</option>
            <option value="KeywordDetected">Mots-clés</option>
            <option value="Mentioned">Mentions</option>
          </select>
        </div>

        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher dans les notifications..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-800 border border-gray-700 rounded-lg pl-10 pr-4 py-2 text-white text-sm placeholder-gray-400"
          />
        </div>
      </div>

      {/* Liste des notifications */}
      <div className="flex-1 overflow-hidden">
        {filteredNotifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-400">
            <BellOff className="w-16 h-16 mb-4" />
            <p className="text-xl font-semibold">Aucune notification</p>
            <p className="text-sm">Les nouvelles notifications apparaîtront ici</p>
          </div>
        ) : (
          <div className="space-y-3 h-full overflow-y-auto pr-2">
            <AnimatePresence>
              {filteredNotifications.map((notification) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className={`border-l-4 p-4 rounded-lg bg-gray-800/50 backdrop-blur-sm ${
                    PriorityColors[notification.priority]
                  } ${!notification.read ? 'border-opacity-100' : 'border-opacity-50 opacity-75'}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="text-2xl">
                        {NotificationIcons[notification.event_type] || '📋'}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-semibold text-white">{notification.title}</h3>
                          <span className="text-xs bg-gray-700 px-2 py-1 rounded text-gray-300">
                            {NotificationEventTypeLabels[notification.event_type] || notification.event_type}
                          </span>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        
                        <p className="text-gray-300 text-sm mb-2">{notification.description}</p>
                        
                        {Object.keys(notification.metadata).length > 0 && (
                          <div className="text-xs text-gray-400 space-y-1">
                            {notification.metadata.guild_name && (
                              <div>Serveur: {notification.metadata.guild_name}</div>
                            )}
                            {notification.metadata.channel_name && (
                              <div>Canal: {notification.metadata.channel_name}</div>
                            )}
                            {notification.metadata.author_name && (
                              <div>Utilisateur: {notification.metadata.author_name}</div>
                            )}
                          </div>
                        )}
                        
                        <div className="text-xs text-gray-500 mt-2">
                          {formatTimeAgo(notification.timestamp)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-1 ml-4">
                      {!notification.read && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-green-400 transition-colors"
                          title="Marquer comme lu"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => deleteNotification(notification.id)}
                        className="p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-red-400 transition-colors"
                        title="Supprimer"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Panneau de paramètres */}
      <AnimatePresence>
        {showSettings && config && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          >
            <h3 className="text-lg font-semibold text-white mb-4">Paramètres des Notifications</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-white">Notifications desktop</label>
                  <button
                    onClick={async () => {
                      if (!config.desktop_notifications) {
                        await requestNotificationPermission();
                      }
                      updateConfig({ desktop_notifications: !config.desktop_notifications });
                    }}
                    className={`w-12 h-6 rounded-full transition-colors ${
                      config.desktop_notifications ? 'bg-purple-500' : 'bg-gray-600'
                    }`}
                  >
                    <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                      config.desktop_notifications ? 'translate-x-6' : 'translate-x-1'
                    }`} />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-white">Sons de notification</label>
                  <button
                    onClick={() => updateConfig({ sound_enabled: !config.sound_enabled })}
                    className={`w-12 h-6 rounded-full transition-colors ${
                      config.sound_enabled ? 'bg-purple-500' : 'bg-gray-600'
                    }`}
                  >
                    <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                      config.sound_enabled ? 'translate-x-6' : 'translate-x-1'
                    }`} />
                  </button>
                </div>

                {config.sound_enabled && (
                  <div className="space-y-2">
                    <label className="text-white text-sm">Volume des sons</label>
                    <div className="flex items-center space-x-2">
                      <VolumeX className="w-4 h-4 text-gray-400" />
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={config.sound_volume}
                        onChange={(e) => updateConfig({ sound_volume: parseFloat(e.target.value) })}
                        className="flex-1"
                      />
                      <Volume2 className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-white text-sm">Mots-clés à surveiller</label>
                  <textarea
                    value={config.keywords.join(', ')}
                    onChange={(e) => updateConfig({ 
                      keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k) 
                    })}
                    placeholder="mot1, mot2, phrase..."
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-white text-sm">Rétention historique (jours)</label>
                  <input
                    type="number"
                    min="1"
                    max="365"
                    value={config.history_retention_days}
                    onChange={(e) => updateConfig({ 
                      history_retention_days: parseInt(e.target.value) || 30 
                    })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}