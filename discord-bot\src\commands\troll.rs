use serenity::all::*;
use voidbot_shared::StealthMode;
use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{<PERSON><PERSON><PERSON>, RwLock};
use tracing::{info, warn, error};
use std::time::{Duration, Instant};

/// Gestionnaire des commandes troll de VoidBot
pub struct TrollManager {
    /// Statuts actifs des commandes troll par utilisateur
    active_trolls: Arc<RwLock<HashMap<UserId, TrollState>>>,
    /// Cache des messages pour manipulation
    message_cache: Arc<RwLock<HashMap<MessageId, CachedTrollMessage>>>,
}

/// État d'une session troll active
#[derive(Debug, <PERSON>lone)]
struct TrollState {
    /// Type de troll actif
    troll_type: TrollType,
    /// Serveur où le troll est actif
    guild_id: Option<GuildId>,
    /// Canal où le troll est actif
    channel_id: Option<ChannelId>,
    /// Timestamp de début
    started_at: Instant,
    /// Configuration du troll
    config: TrollConfig,
}

/// Types de trolls disponibles
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
enum TrollType {
    /// Empêche de quitter les groupes
    NoLeave,
    /// Envoie des ghostpings
    GhostPing,
    /// Spam contrôlé
    Spam,
    /// Simule qu'on tape
    FakeTyping,
    /// Messages invisibles
    InvisibleMessage,
    /// Auto-react sur tous les messages
    AutoReact,
    /// Répète les messages
    EchoMessages,
}

/// Configuration d'un troll
#[derive(Debug, Clone)]
struct TrollConfig {
    /// Cible du troll (utilisateur spécifique)
    target_user: Option<UserId>,
    /// Message/réaction à utiliser
    message_content: Option<String>,
    /// Délai entre actions (en millisecondes)
    delay_ms: u64,
    /// Nombre maximum d'actions (-1 pour illimité)
    max_actions: i32,
    /// Auto-stop après X minutes
    auto_stop_minutes: Option<u64>,
}

/// Message mis en cache pour les trolls
#[derive(Debug, Clone)]
struct CachedTrollMessage {
    content: String,
    author_id: UserId,
    channel_id: ChannelId,
    guild_id: Option<GuildId>,
    timestamp: Instant,
}

impl TrollManager {
    /// Créer un nouveau gestionnaire de trolls
    pub fn new() -> Self {
        Self {
            active_trolls: Arc::new(RwLock::new(HashMap::new())),
            message_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Démarrer un troll NoLeave
    pub async fn start_noleave_troll(
        &self,
        ctx: &Context,
        interaction: &CommandInteraction,
        target_user: Option<UserId>,
    ) -> Result<()> {
        let config = TrollConfig {
            target_user,
            message_content: None,
            delay_ms: 1000,
            max_actions: -1,
            auto_stop_minutes: Some(60), // Auto-stop après 1h
        };

        let troll_state = TrollState {
            troll_type: TrollType::NoLeave,
            guild_id: interaction.guild_id,
            channel_id: Some(interaction.channel_id),
            started_at: Instant::now(),
            config,
        };

        // Enregistrer le troll actif
        {
            let mut active = self.active_trolls.write().await;
            active.insert(interaction.user.id, troll_state);
        }

        info!("🎭 Troll NoLeave démarré par {}", interaction.user.name);
        Ok(())
    }

    /// Démarrer un troll GhostPing
    pub async fn start_ghostping_troll(
        &self,
        ctx: &Context,
        interaction: &CommandInteraction,
        target_user: UserId,
        message: Option<String>,
    ) -> Result<()> {
        let message_clone = message.clone();
        let config = TrollConfig {
            target_user: Some(target_user),
            message_content: message,
            delay_ms: 2000, // 2 secondes entre ghostpings
            max_actions: 10, // Maximum 10 ghostpings
            auto_stop_minutes: Some(30),
        };

        let troll_state = TrollState {
            troll_type: TrollType::GhostPing,
            guild_id: interaction.guild_id,
            channel_id: Some(interaction.channel_id),
            started_at: Instant::now(),
            config,
        };

        {
            let mut active = self.active_trolls.write().await;
            active.insert(interaction.user.id, troll_state);
        }

        // Démarrer le troll ghostping en arrière-plan
        self.execute_ghostping_troll(ctx.clone(), interaction.channel_id, target_user, message_clone).await?;

        info!("👻 Troll GhostPing démarré par {} sur {}", interaction.user.name, target_user);
        Ok(())
    }

    /// Démarrer un troll Spam contrôlé
    pub async fn start_spam_troll(
        &self,
        ctx: &Context,
        interaction: &CommandInteraction,
        message: String,
        count: u32,
        delay_seconds: u64,
    ) -> Result<()> {
        // Limiter le spam pour éviter l'abuse
        let safe_count = std::cmp::min(count, 20); // Max 20 messages
        let safe_delay = std::cmp::max(delay_seconds, 3); // Min 3 secondes entre messages

        let config = TrollConfig {
            target_user: None,
            message_content: Some(message.clone()),
            delay_ms: safe_delay * 1000,
            max_actions: safe_count as i32,
            auto_stop_minutes: Some(10),
        };

        let troll_state = TrollState {
            troll_type: TrollType::Spam,
            guild_id: interaction.guild_id,
            channel_id: Some(interaction.channel_id),
            started_at: Instant::now(),
            config,
        };

        {
            let mut active = self.active_trolls.write().await;
            active.insert(interaction.user.id, troll_state);
        }

        // Exécuter le spam en arrière-plan
        self.execute_spam_troll(ctx.clone(), interaction.channel_id, message, safe_count, safe_delay).await?;

        info!("💬 Troll Spam démarré par {} : {} messages", interaction.user.name, safe_count);
        Ok(())
    }

    /// Démarrer le troll FakeTyping
    pub async fn start_fake_typing_troll(
        &self,
        ctx: &Context,
        interaction: &CommandInteraction,
        duration_minutes: u64,
    ) -> Result<()> {
        let safe_duration = std::cmp::min(duration_minutes, 30); // Max 30 minutes

        let config = TrollConfig {
            target_user: None,
            message_content: None,
            delay_ms: 8000, // Typing indicator dure ~10 secondes
            max_actions: (safe_duration * 60 / 8) as i32, // Calculer les cycles
            auto_stop_minutes: Some(safe_duration),
        };

        let troll_state = TrollState {
            troll_type: TrollType::FakeTyping,
            guild_id: interaction.guild_id,
            channel_id: Some(interaction.channel_id),
            started_at: Instant::now(),
            config,
        };

        {
            let mut active = self.active_trolls.write().await;
            active.insert(interaction.user.id, troll_state);
        }

        // Exécuter fake typing en arrière-plan
        self.execute_fake_typing_troll(ctx.clone(), interaction.channel_id, safe_duration).await?;

        info!("⌨️ Troll FakeTyping démarré par {} : {}min", interaction.user.name, safe_duration);
        Ok(())
    }

    /// Arrêter tous les trolls d'un utilisateur
    pub async fn stop_all_trolls(&self, user_id: UserId) -> Result<Vec<TrollType>> {
        let mut active = self.active_trolls.write().await;
        if let Some(state) = active.remove(&user_id) {
            Ok(vec![state.troll_type])
        } else {
            Ok(vec![])
        }
    }

    /// Vérifier si un utilisateur a des trolls actifs
    pub async fn has_active_trolls(&self, user_id: UserId) -> bool {
        let active = self.active_trolls.read().await;
        active.contains_key(&user_id)
    }

    /// Obtenir les trolls actifs d'un utilisateur
    pub async fn get_active_trolls(&self, user_id: UserId) -> Option<TrollState> {
        let active = self.active_trolls.read().await;
        active.get(&user_id).cloned()
    }

    /// Nettoyer les trolls expirés
    pub async fn cleanup_expired_trolls(&self) {
        let mut active = self.active_trolls.write().await;
        let mut to_remove = Vec::new();

        for (user_id, state) in active.iter() {
            if let Some(auto_stop) = state.config.auto_stop_minutes {
                if state.started_at.elapsed() > Duration::from_secs(auto_stop * 60) {
                    to_remove.push(*user_id);
                }
            }
        }

        for user_id in to_remove {
            active.remove(&user_id);
            info!("🕐 Troll auto-expiré pour l'utilisateur {}", user_id);
        }
    }

    /// Exécuter le troll ghostping
    async fn execute_ghostping_troll(
        &self,
        ctx: Context,
        channel_id: ChannelId,
        target_user: UserId,
        message: Option<String>,
    ) -> Result<()> {
        let base_message = message.unwrap_or_else(|| "👻".to_string());
        
        for i in 1..=10 {
            // Envoyer message avec mention
            let content = format!("{} <@{}>", base_message, target_user);
            
            if let Ok(sent_message) = channel_id.say(&ctx.http, &content).await {
                // Attendre un peu puis supprimer
                tokio::time::sleep(Duration::from_millis(500)).await;
                
                if let Err(e) = sent_message.delete(&ctx.http).await {
                    warn!("Échec suppression ghostping message : {}", e);
                }
            }

            // Pause entre ghostpings
            tokio::time::sleep(Duration::from_secs(2)).await;
        }

        Ok(())
    }

    /// Exécuter le troll spam
    async fn execute_spam_troll(
        &self,
        ctx: Context,
        channel_id: ChannelId,
        message: String,
        count: u32,
        delay_seconds: u64,
    ) -> Result<()> {
        for i in 1..=count {
            let numbered_message = format!("{} [{}]", message, i);
            
            if let Err(e) = channel_id.say(&ctx.http, &numbered_message).await {
                warn!("Échec envoi spam message {} : {}", i, e);
                break;
            }

            if i < count {
                tokio::time::sleep(Duration::from_secs(delay_seconds)).await;
            }
        }

        Ok(())
    }

    /// Exécuter le troll fake typing
    async fn execute_fake_typing_troll(
        &self,
        ctx: Context,
        channel_id: ChannelId,
        duration_minutes: u64,
    ) -> Result<()> {
        let end_time = Instant::now() + Duration::from_secs(duration_minutes * 60);

        while Instant::now() < end_time {
            // Déclencher typing indicator
            let _typing = channel_id.start_typing(&ctx.http);

            // Attendre ~8 secondes (typing indicator dure ~10s)
            tokio::time::sleep(Duration::from_secs(8)).await;
        }

        Ok(())
    }

    /// Gérer l'événement de tentative de quitter un groupe (pour NoLeave)
    pub async fn handle_group_leave_attempt(
        &self,
        ctx: &Context,
        user_id: UserId,
        channel_id: ChannelId,
    ) -> Result<bool> {
        let active = self.active_trolls.read().await;
        
        // Vérifier si l'utilisateur a un troll NoLeave actif
        if let Some(state) = active.get(&user_id) {
            if state.troll_type == TrollType::NoLeave {
                // Empêcher de quitter en envoyant un message
                if let Err(e) = channel_id.say(&ctx.http, "🔒 **NoLeave activé !** Vous ne pouvez pas quitter ce groupe tant que le troll est actif.").await {
                    warn!("Échec envoi message NoLeave : {}", e);
                }
                
                info!("🔒 Tentative de quitter bloquée par NoLeave pour {}", user_id);
                return Ok(true); // Bloqué
            }
        }

        Ok(false) // Non bloqué
    }
}

/// Créer les commandes slash pour les trolls
pub fn create_troll_commands() -> Vec<CreateCommand> {
    vec![
        // Commande principale /troll
        CreateCommand::new("troll")
            .description("🎭 Commandes de troll avancées")
            .add_option(
                CreateCommandOption::new(
                    CommandOptionType::SubCommand,
                    "noleave",
                    "🔒 Empêche de quitter les groupes"
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::User,
                        "utilisateur",
                        "Utilisateur cible (optionnel)"
                    )
                    .required(false)
                )
            )
            .add_option(
                CreateCommandOption::new(
                    CommandOptionType::SubCommand,
                    "ghostping",
                    "👻 Envoie des ghostpings à un utilisateur"
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::User,
                        "utilisateur",
                        "Utilisateur à ghostping"
                    )
                    .required(true)
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::String,
                        "message",
                        "Message à envoyer avant mention (optionnel)"
                    )
                    .required(false)
                )
            )
            .add_option(
                CreateCommandOption::new(
                    CommandOptionType::SubCommand,
                    "spam",
                    "💬 Spam contrôlé de messages"
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::String,
                        "message",
                        "Message à spam"
                    )
                    .required(true)
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::Integer,
                        "nombre",
                        "Nombre de messages (max 20)"
                    )
                    .required(false)
                    .min_int_value(1)
                    .max_int_value(20)
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::Integer,
                        "delai",
                        "Délai entre messages en secondes (min 3)"
                    )
                    .required(false)
                    .min_int_value(3)
                    .max_int_value(60)
                )
            )
            .add_option(
                CreateCommandOption::new(
                    CommandOptionType::SubCommand,
                    "typing",
                    "⌨️ Simule qu'on tape en permanence"
                )
                .add_sub_option(
                    CreateCommandOption::new(
                        CommandOptionType::Integer,
                        "duree",
                        "Durée en minutes (max 30)"
                    )
                    .required(false)
                    .min_int_value(1)
                    .max_int_value(30)
                )
            )
            .add_option(
                CreateCommandOption::new(
                    CommandOptionType::SubCommand,
                    "stop",
                    "⏹️ Arrête tous les trolls actifs"
                )
            )
            .add_option(
                CreateCommandOption::new(
                    CommandOptionType::SubCommand,
                    "status",
                    "📊 Affiche les trolls actifs"
                )
            ),
    ]
}

/// Traiter les commandes troll
pub async fn handle_troll_command(
    ctx: &Context,
    interaction: &CommandInteraction,
    troll_manager: &TrollManager,
    stealth_mode: &StealthMode,
) -> Result<()> {
    let subcommand = interaction.data.options.first()
        .ok_or_else(|| anyhow::anyhow!("Sous-commande troll manquante"))?;

    // Les commandes troll sont toujours en mode fantôme pour la discrétion
    let ephemeral = true;
    
    // Get the sub-options from the subcommand
    let sub_options = if let CommandDataOptionValue::SubCommand(ref options) = &subcommand.value {
        options
    } else {
        return Err(anyhow::anyhow!("Structure de commande invalide"));
    };

    match subcommand.name.as_str() {
        "noleave" => {
            let target_user = sub_options.iter()
                .find(|opt| opt.name == "utilisateur")
                .and_then(|opt| {
                    if let CommandDataOptionValue::User(user_id) = &opt.value {
                        Some(*user_id)
                    } else {
                        None
                    }
                });

            troll_manager.start_noleave_troll(ctx, interaction, target_user).await?;

            let response = if let Some(user_id) = target_user {
                format!("🔒 **Troll NoLeave activé** sur <@{}>!\nIls ne pourront plus quitter les groupes.", user_id)
            } else {
                "🔒 **Troll NoLeave activé** sur tous les utilisateurs!\nPersonne ne peut plus quitter les groupes.".to_string()
            };

            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(response)
                    .ephemeral(ephemeral)
            )).await?;
        }

        "ghostping" => {
            let target_user = sub_options.iter()
                .find(|opt| opt.name == "utilisateur")
                .and_then(|opt| {
                    if let CommandDataOptionValue::User(user_id) = &opt.value {
                        Some(*user_id)
                    } else {
                        None
                    }
                })
                .ok_or_else(|| anyhow::anyhow!("Utilisateur cible requis pour ghostping"))?;

            let message = sub_options.iter()
                .find(|opt| opt.name == "message")
                .and_then(|opt| opt.value.as_str())
                .map(|s| s.to_string());

            troll_manager.start_ghostping_troll(ctx, interaction, target_user, message).await?;

            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(format!("👻 **Troll GhostPing démarré** sur <@{}>!\n10 ghostpings seront envoyés toutes les 2 secondes.", target_user))
                    .ephemeral(ephemeral)
            )).await?;
        }

        "spam" => {
            let message = sub_options.iter()
                .find(|opt| opt.name == "message")
                .and_then(|opt| opt.value.as_str())
                .ok_or_else(|| anyhow::anyhow!("Message requis pour spam"))?
                .to_string();

            let count = sub_options.iter()
                .find(|opt| opt.name == "nombre")
                .and_then(|opt| opt.value.as_i64())
                .unwrap_or(5) as u32;

            let delay = sub_options.iter()
                .find(|opt| opt.name == "delai")
                .and_then(|opt| opt.value.as_i64())
                .unwrap_or(5) as u64;

            troll_manager.start_spam_troll(ctx, interaction, message.clone(), count, delay).await?;

            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(format!("💬 **Troll Spam démarré**!\n{} messages '{}' seront envoyés toutes les {} secondes.", count, message, delay))
                    .ephemeral(ephemeral)
            )).await?;
        }

        "typing" => {
            let duration = sub_options.iter()
                .find(|opt| opt.name == "duree")
                .and_then(|opt| opt.value.as_i64())
                .unwrap_or(5) as u64;

            troll_manager.start_fake_typing_troll(ctx, interaction, duration).await?;

            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(format!("⌨️ **Troll FakeTyping démarré**!\nSimulation de frappe pendant {} minutes.", duration))
                    .ephemeral(ephemeral)
            )).await?;
        }

        "stop" => {
            let stopped_trolls = troll_manager.stop_all_trolls(interaction.user.id).await?;

            let response = if stopped_trolls.is_empty() {
                "⏹️ Aucun troll actif à arrêter.".to_string()
            } else {
                format!("⏹️ **Trolls arrêtés** : {} troll(s) stoppé(s).", stopped_trolls.len())
            };

            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(response)
                    .ephemeral(ephemeral)
            )).await?;
        }

        "status" => {
            let active_troll = troll_manager.get_active_trolls(interaction.user.id).await;

            let response = if let Some(state) = active_troll {
                let duration = state.started_at.elapsed();
                format!(
                    "📊 **Troll actif** : {:?}\n⏱️ Durée : {}min {}s\n🎯 Canal : <#{}>",
                    state.troll_type,
                    duration.as_secs() / 60,
                    duration.as_secs() % 60,
                    state.channel_id.map(|c| c.to_string()).unwrap_or("DM".to_string())
                )
            } else {
                "📊 **Aucun troll actif** pour le moment.".to_string()
            };

            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(response)
                    .ephemeral(ephemeral)
            )).await?;
        }

        _ => {
            interaction.create_response(&ctx.http, CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content("❌ Sous-commande troll inconnue!")
                    .ephemeral(true)
            )).await?;
        }
    }

    Ok(())
}