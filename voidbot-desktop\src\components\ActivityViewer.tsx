import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

interface ActivityViewerConfig {
  enabled: boolean;
  max_tracked_users: number;
  max_history_entries: number;
  track_status_changes: boolean;
  track_voice_activity: boolean;
  track_game_activity: boolean;
  track_typing: boolean;
  track_presence: boolean;
  auto_remove_inactive_days?: number;
  notification_on_activity: boolean;
  stealth_mode: boolean;
}

interface TrackedUser {
  user_id: string;
  username: string;
  discriminator: string;
  avatar?: string;
  added_at: string;
  last_seen: string;
  current_status: 'Online' | 'Idle' | 'DoNotDisturb' | 'Invisible' | 'Offline';
  current_activity?: UserActivity;
  voice_state?: VoiceState;
  guilds_shared: string[];
  notes?: string;
  alerts_enabled: boolean;
}

interface UserActivity {
  activity_type: 'Playing' | 'Streaming' | 'Listening' | 'Watching' | 'Custom' | 'Competing';
  name: string;
  details?: string;
  state?: string;
}

interface VoiceState {
  guild_id?: string;
  channel_id?: string;
  self_mute: boolean;
  self_deaf: boolean;
  server_mute: boolean;
  server_deaf: boolean;
  streaming: boolean;
  video: boolean;
}

interface ActivityStats {
  total_tracked_users: number;
  total_events: number;
  events_today: number;
  most_active_user?: string;
  most_common_activity?: string;
  average_online_time: number;
  last_updated: string;
}

interface ActivityEvent {
  user_id: string;
  event_type: string;
  timestamp: string;
  guild_id?: string;
  channel_id?: string;
  data: any;
}

const StatusIndicator = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Online': return { color: 'bg-green-500', glow: 'shadow-green-500/50', icon: '🟢', text: 'En ligne' };
      case 'Idle': return { color: 'bg-yellow-500', glow: 'shadow-yellow-500/50', icon: '🌙', text: 'Absent' };
      case 'DoNotDisturb': return { color: 'bg-red-500', glow: 'shadow-red-500/50', icon: '⛔', text: 'Ne pas déranger' };
      case 'Invisible': return { color: 'bg-gray-500', glow: 'shadow-gray-500/50', icon: '👻', text: 'Invisible' };
      case 'Offline': return { color: 'bg-gray-600', glow: 'shadow-gray-600/50', icon: '⚫', text: 'Hors ligne' };
      default: return { color: 'bg-gray-400', glow: 'shadow-gray-400/50', icon: '❓', text: 'Inconnu' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <motion.div 
      className="flex items-center space-x-2"
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <div className={`w-3 h-3 rounded-full ${config.color} ${config.glow} shadow-lg animate-pulse`} />
      <span className="text-sm text-gray-300">{config.text}</span>
    </motion.div>
  );
};

const UserCard = ({ user, onViewHistory, onExport, onRemove }: {
  user: TrackedUser;
  onViewHistory: (userId: string) => void;
  onExport: (userId: string) => void;
  onRemove: (userId: string) => void;
}) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50 hover:border-indigo-500/50 transition-all duration-300"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Avatar placeholder */}
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
              {user.username[0].toUpperCase()}
            </div>
            <div className="absolute -bottom-1 -right-1">
              <StatusIndicator status={user.current_status} />
            </div>
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="text-white font-semibold text-lg">
                {user.username}
                <span className="text-gray-400 text-sm ml-1">#{user.discriminator}</span>
              </h3>
              {user.alerts_enabled && (
                <span className="text-xs bg-green-600 text-white px-2 py-1 rounded-full">
                  🔔 Alertes ON
                </span>
              )}
            </div>
            
            <div className="text-sm text-gray-400 mt-1">
              ID: <code className="bg-gray-700 px-1 rounded text-xs">{user.user_id}</code>
            </div>
            
            <div className="text-xs text-gray-500 mt-1">
              Dernière vue: {new Date(user.last_seen).toLocaleString('fr-FR')}
            </div>

            {/* Current Activity */}
            <AnimatePresence>
              {user.current_activity && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-2 p-2 bg-gray-700/50 rounded-lg"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">🎮</span>
                    <span className="text-sm text-green-400 font-medium">
                      {user.current_activity.activity_type}
                    </span>
                    <span className="text-sm text-white">
                      {user.current_activity.name}
                    </span>
                  </div>
                  {user.current_activity.details && (
                    <div className="text-xs text-gray-300 mt-1 pl-6">
                      {user.current_activity.details}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Voice State */}
            <AnimatePresence>
              {user.voice_state?.channel_id && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-2 p-2 bg-purple-600/20 rounded-lg border border-purple-500/30"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-purple-400">🔊</span>
                    <span className="text-sm text-purple-400">En vocal</span>
                    <div className="flex space-x-1">
                      {user.voice_state.self_mute && <span title="Micro coupé">🔇</span>}
                      {user.voice_state.self_deaf && <span title="Audio coupé">🔇</span>}
                      {user.voice_state.streaming && <span title="En stream">📺</span>}
                      {user.voice_state.video && <span title="Caméra on">📹</span>}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowDetails(!showDetails)}
            className="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
            title="Voir détails"
          >
            📊
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onViewHistory(user.user_id)}
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            title="Voir historique"
          >
            📜
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onExport(user.user_id)}
            className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            title="Exporter données"
          >
            💾
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onRemove(user.user_id)}
            className="p-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            title="Supprimer"
          >
            🗑️
          </motion.button>
        </div>
      </div>

      {/* Expanded Details */}
      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 pt-4 border-t border-gray-600/50"
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-400">Ajouté le:</div>
                <div className="text-white">{new Date(user.added_at).toLocaleDateString('fr-FR')}</div>
              </div>
              <div>
                <div className="text-gray-400">Serveurs partagés:</div>
                <div className="text-white">{user.guilds_shared.length}</div>
              </div>
            </div>
            {user.notes && (
              <div className="mt-3">
                <div className="text-gray-400 text-sm">Notes:</div>
                <div className="text-white text-sm bg-gray-700/50 p-2 rounded mt-1">
                  {user.notes}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const StatsCard = ({ title, value, subtitle, icon, color = 'indigo' }: {
  title: string;
  value: string | number;
  subtitle: string;
  icon: string;
  color?: string;
}) => {
  const colorClasses = {
    indigo: 'from-indigo-500/20 to-purple-600/20 border-indigo-500/30',
    green: 'from-green-500/20 to-emerald-600/20 border-green-500/30',
    blue: 'from-blue-500/20 to-cyan-600/20 border-blue-500/30',
    purple: 'from-purple-500/20 to-pink-600/20 border-purple-500/30',
  };

  return (
    <motion.div
      whileHover={{ scale: 1.05, y: -2 }}
      className={`bg-gradient-to-br ${colorClasses[color as keyof typeof colorClasses]} backdrop-blur-sm rounded-xl p-4 border`}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold text-white">{value}</div>
          <div className="text-sm text-gray-300">{title}</div>
          <div className="text-xs text-gray-400 mt-1">{subtitle}</div>
        </div>
        <div className="text-3xl opacity-80">{icon}</div>
      </div>
    </motion.div>
  );
};

export default function ActivityViewer() {
  const [config, setConfig] = useState<ActivityViewerConfig>({
    enabled: false,
    max_tracked_users: 50,
    max_history_entries: 1000,
    track_status_changes: true,
    track_voice_activity: true,
    track_game_activity: true,
    track_typing: true,
    track_presence: true,
    auto_remove_inactive_days: 30,
    notification_on_activity: false,
    stealth_mode: true,
  });

  const [trackedUsers, setTrackedUsers] = useState<TrackedUser[]>([]);
  const [stats, setStats] = useState<ActivityStats>({
    total_tracked_users: 0,
    total_events: 0,
    events_today: 0,
    average_online_time: 0,
    last_updated: new Date().toISOString(),
  });

  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [userHistory, setUserHistory] = useState<ActivityEvent[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'config' | 'history'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Formulaire d'ajout d'utilisateur
  const [newUser, setNewUser] = useState({
    user_id: '',
    username: '',
    discriminator: '',
    avatar: '',
  });
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    loadConfig();
    loadTrackedUsers();
    loadStats();

    // Auto-refresh des données toutes les 5 secondes
    const interval = setInterval(() => {
      if (config.enabled) {
        loadTrackedUsers();
        loadStats();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [config.enabled]);

  const loadConfig = async () => {
    try {
      const activityConfig = await invoke<ActivityViewerConfig>('get_activity_viewer_config');
      setConfig(activityConfig);
    } catch (error) {
      console.error('Erreur lors du chargement de la config Activity Viewer:', error);
    }
  };

  const loadTrackedUsers = async () => {
    try {
      const users = await invoke<TrackedUser[]>('get_tracked_users');
      setTrackedUsers(users);
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs surveillés:', error);
    }
  };

  const loadStats = async () => {
    try {
      const activityStats = await invoke<ActivityStats>('get_activity_stats');
      setStats(activityStats);
    } catch (error) {
      console.error('Erreur lors du chargement des stats:', error);
    }
  };

  const toggleActivityViewer = async () => {
    try {
      const newEnabled = !config.enabled;
      await invoke('toggle_activity_viewer', { enabled: newEnabled });
      setConfig(prev => ({ ...prev, enabled: newEnabled }));
      toast.success(
        newEnabled ? '🔍 Activity Viewer activé' : '⏹️ Activity Viewer désactivé',
        {
          duration: 3000,
          style: {
            background: newEnabled ? '#10B981' : '#EF4444',
            color: 'white',
          },
        }
      );
    } catch (error) {
      toast.error('Erreur lors du toggle');
    }
  };

  const updateConfig = async (newConfig: ActivityViewerConfig) => {
    try {
      await invoke('update_activity_viewer_config', { config: newConfig });
      setConfig(newConfig);
      toast.success('✅ Configuration mise à jour', {
        style: { background: '#10B981', color: 'white' },
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('❌ Erreur lors de la mise à jour');
    }
  };

  const addUser = async () => {
    if (!newUser.user_id || !newUser.username) {
      toast.error('ID utilisateur et nom requis');
      return;
    }

    try {
      await invoke('add_tracked_user', {
        userId: newUser.user_id,
        username: newUser.username,
        discriminator: newUser.discriminator || '0000',
        avatar: newUser.avatar || null,
      });
      
      toast.success('👤 Utilisateur ajouté à la surveillance', {
        style: { background: '#10B981', color: 'white' },
      });
      setNewUser({ user_id: '', username: '', discriminator: '', avatar: '' });
      setShowAddForm(false);
      await loadTrackedUsers();
    } catch (error) {
      console.error('Erreur lors de l\'ajout:', error);
      toast.error('❌ Erreur lors de l\'ajout');
    }
  };

  const removeUser = async (userId: string) => {
    try {
      await invoke('remove_tracked_user', { userId });
      toast.success('🗑️ Utilisateur retiré de la surveillance');
      await loadTrackedUsers();
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error('❌ Erreur lors de la suppression');
    }
  };

  const loadUserHistory = async (userId: string) => {
    try {
      const history = await invoke<ActivityEvent[]>('get_user_activity_history', {
        userId,
        limit: 100,
      });
      setUserHistory(history);
      setSelectedUser(userId);
      setActiveTab('history');
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
      toast.error('❌ Erreur lors du chargement de l\'historique');
    }
  };

  const exportData = async (userId?: string) => {
    try {
      const data = await invoke<string>('export_activity_data', { userId });
      
      // Créer un fichier de téléchargement
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `activity_data_${userId || 'all'}_${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      toast.success('💾 Données exportées', {
        style: { background: '#3B82F6', color: 'white' },
      });
    } catch (error) {
      console.error('Erreur lors de l\'export:', error);
      toast.error('❌ Erreur lors de l\'export');
    }
  };

  // Filtrer les utilisateurs
  const filteredUsers = trackedUsers.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.user_id.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || user.current_status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const tabs = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: '📊' },
    { id: 'users', label: 'Utilisateurs', icon: '👥' },
    { id: 'config', label: 'Configuration', icon: '⚙️' },
    { id: 'history', label: 'Historique', icon: '📜' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
              🔍 Activity Viewer
            </h1>
            <p className="text-gray-400 mt-2">
              Surveillance temps réel d'activité utilisateurs Discord
              {!config.enabled && (
                <span className="text-red-400 ml-2 animate-pulse">● DÉSACTIVÉ</span>
              )}
              {config.enabled && (
                <span className="text-green-400 ml-2 animate-pulse">● ACTIF</span>
              )}
            </p>
          </div>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleActivityViewer}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
              config.enabled 
                ? 'bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-500/25' 
                : 'bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-500/25'
            }`}
          >
            {config.enabled ? '⏹️ Désactiver' : '▶️ Activer'}
          </motion.button>
        </motion.div>

        {/* Warning si désactivé */}
        <AnimatePresence>
          {!config.enabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 border border-yellow-700/50 rounded-xl p-4 backdrop-blur-sm"
            >
              <div className="flex items-center">
                <div className="text-yellow-400 text-2xl mr-3 animate-bounce">⚠️</div>
                <div>
                  <h3 className="text-yellow-400 font-medium text-lg">Activity Viewer désactivé</h3>
                  <p className="text-yellow-300 text-sm mt-1">
                    Activez le système pour commencer la surveillance d'activité utilisateurs en temps réel
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          <StatsCard
            title="Utilisateurs surveillés"
            value={stats.total_tracked_users}
            subtitle="Actifs en temps réel"
            icon="👥"
            color="indigo"
          />
          <StatsCard
            title="Événements total"
            value={stats.total_events.toLocaleString()}
            subtitle="Toutes les données"
            icon="📊"
            color="green"
          />
          <StatsCard
            title="Aujourd'hui"
            value={stats.events_today}
            subtitle="Événements du jour"
            icon="🌅"
            color="blue"
          />
          <StatsCard
            title="Temps en ligne moy."
            value={`${Math.round(stats.average_online_time * 10) / 10}h`}
            subtitle="Par utilisateur"
            icon="⏱️"
            color="purple"
          />
        </motion.div>

        {/* Tabs Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="border-b border-gray-700/50"
        >
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                whileHover={{ y: -2 }}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-400 bg-indigo-500/10 rounded-t-lg'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                }`}
              >
                <span className="mr-2 text-lg">{tab.icon}</span>
                {tab.label}
              </motion.button>
            ))}
          </nav>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Quick Actions */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">🚀 Actions rapides</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setActiveTab('users')}
                      className="p-4 bg-indigo-600/20 border border-indigo-500/30 rounded-lg text-left hover:bg-indigo-600/30 transition-colors"
                    >
                      <div className="text-2xl mb-2">👤</div>
                      <div className="text-white font-medium">Gérer utilisateurs</div>
                      <div className="text-gray-400 text-sm">Ajouter/supprimer surveillance</div>
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => exportData()}
                      className="p-4 bg-green-600/20 border border-green-500/30 rounded-lg text-left hover:bg-green-600/30 transition-colors"
                    >
                      <div className="text-2xl mb-2">💾</div>
                      <div className="text-white font-medium">Exporter tout</div>
                      <div className="text-gray-400 text-sm">Télécharger toutes les données</div>
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setActiveTab('config')}
                      className="p-4 bg-purple-600/20 border border-purple-500/30 rounded-lg text-left hover:bg-purple-600/30 transition-colors"
                    >
                      <div className="text-2xl mb-2">⚙️</div>
                      <div className="text-white font-medium">Configuration</div>
                      <div className="text-gray-400 text-sm">Paramètres surveillance</div>
                    </motion.button>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">📈 Activité récente</h3>
                  {trackedUsers.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">👻</div>
                      <div className="text-gray-400 text-lg">Aucun utilisateur surveillé</div>
                      <div className="text-gray-500 text-sm">Ajoutez des utilisateurs pour commencer</div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {trackedUsers.slice(0, 6).map((user) => (
                        <motion.div
                          key={user.user_id}
                          whileHover={{ scale: 1.02 }}
                          className="p-3 bg-gray-700/50 rounded-lg border border-gray-600/30"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              {user.username[0].toUpperCase()}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium truncate">{user.username}</div>
                              <StatusIndicator status={user.current_status} />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Users Tab */}
            {activeTab === 'users' && (
              <div className="space-y-6">
                {/* Search and Filter */}
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="🔍 Rechercher par nom ou ID..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      />
                    </div>
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                    >
                      <option value="all">Tous les statuts</option>
                      <option value="Online">En ligne</option>
                      <option value="Idle">Absent</option>
                      <option value="DoNotDisturb">Ne pas déranger</option>
                      <option value="Invisible">Invisible</option>
                      <option value="Offline">Hors ligne</option>
                    </select>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setShowAddForm(!showAddForm)}
                      className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                    >
                      ➕ Ajouter utilisateur
                    </motion.button>
                  </div>
                </div>

                {/* Add User Form */}
                <AnimatePresence>
                  {showAddForm && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-gradient-to-r from-indigo-900/50 to-purple-900/50 border border-indigo-500/30 rounded-xl p-6"
                    >
                      <h3 className="text-lg font-semibold text-white mb-4">👤 Ajouter un utilisateur à surveiller</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <input
                          type="text"
                          placeholder="ID utilisateur Discord"
                          value={newUser.user_id}
                          onChange={(e) => setNewUser(prev => ({ ...prev, user_id: e.target.value }))}
                          className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500"
                        />
                        <input
                          type="text"
                          placeholder="Nom d'utilisateur"
                          value={newUser.username}
                          onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
                          className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500"
                        />
                        <input
                          type="text"
                          placeholder="Discriminateur (optionnel)"
                          value={newUser.discriminator}
                          onChange={(e) => setNewUser(prev => ({ ...prev, discriminator: e.target.value }))}
                          className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500"
                        />
                      </div>
                      <div className="flex justify-end space-x-3 mt-4">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setShowAddForm(false)}
                          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                        >
                          Annuler
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={addUser}
                          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                        >
                          ✅ Ajouter
                        </motion.button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Users List */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-semibold text-white">
                      👥 Utilisateurs surveillés ({filteredUsers.length})
                    </h3>
                    {trackedUsers.length > 0 && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => exportData()}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        💾 Exporter tout
                      </motion.button>
                    )}
                  </div>
                  
                  {filteredUsers.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">🔍</div>
                      <div className="text-gray-400 text-xl">
                        {searchTerm || statusFilter !== 'all' 
                          ? 'Aucun utilisateur trouvé' 
                          : 'Aucun utilisateur surveillé'
                        }
                      </div>
                      <div className="text-gray-500 text-sm mt-2">
                        {searchTerm || statusFilter !== 'all' 
                          ? 'Essayez de modifier vos filtres' 
                          : 'Ajoutez des utilisateurs pour commencer la surveillance'
                        }
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <AnimatePresence>
                        {filteredUsers.map((user) => (
                          <UserCard
                            key={user.user_id}
                            user={user}
                            onViewHistory={loadUserHistory}
                            onExport={exportData}
                            onRemove={removeUser}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Config Tab */}
            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-6">⚙️ Configuration Activity Viewer</h3>
                  
                  <div className="space-y-8">
                    {/* Général */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">📊 Paramètres généraux</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Nombre max d'utilisateurs surveillés
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="200"
                            value={config.max_tracked_users}
                            onChange={(e) => setConfig(prev => ({ ...prev, max_tracked_users: parseInt(e.target.value) || 1 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Nombre max d'événements en historique
                          </label>
                          <input
                            type="number"
                            min="100"
                            max="10000"
                            value={config.max_history_entries}
                            onChange={(e) => setConfig(prev => ({ ...prev, max_history_entries: parseInt(e.target.value) || 100 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Supprimer les utilisateurs inactifs après (jours)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="365"
                            value={config.auto_remove_inactive_days || 30}
                            onChange={(e) => setConfig(prev => ({ ...prev, auto_remove_inactive_days: parseInt(e.target.value) || 30 }))}
                            className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Types d'événements */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">🎯 Types d'événements à surveiller</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                          { key: 'track_status_changes', label: 'Changements de statut', icon: '🔄', desc: 'En ligne, Absent, Ne pas déranger, etc.' },
                          { key: 'track_voice_activity', label: 'Activité vocale', icon: '🔊', desc: 'Rejoindre/Quitter salons vocaux' },
                          { key: 'track_game_activity', label: 'Activité de jeu', icon: '🎮', desc: 'Jeux et Rich Presence' },
                          { key: 'track_typing', label: 'Événements de frappe', icon: '⌨️', desc: 'Utilisateur en train d\'écrire' },
                          { key: 'track_presence', label: 'Mises à jour de présence', icon: '👤', desc: 'Changements d\'activité et statut' },
                          { key: 'notification_on_activity', label: 'Notifications sur activité', icon: '🔔', desc: 'Alertes en temps réel' },
                          { key: 'stealth_mode', label: 'Mode furtif', icon: '👻', desc: 'Masquer la surveillance' },
                        ].map((setting) => (
                          <motion.label 
                            key={setting.key} 
                            className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                            whileHover={{ scale: 1.02 }}
                          >
                            <input
                              type="checkbox"
                              checked={config[setting.key as keyof ActivityViewerConfig] as boolean}
                              onChange={(e) => setConfig(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                              className="w-4 h-4 text-indigo-600 bg-gray-700 border-gray-600 rounded focus:ring-indigo-500 mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-lg">{setting.icon}</span>
                                <span className="text-white font-medium">{setting.label}</span>
                              </div>
                              <div className="text-gray-400 text-sm mt-1">{setting.desc}</div>
                            </div>
                          </motion.label>
                        ))}
                      </div>
                    </div>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateConfig(config)}
                      className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg shadow-indigo-500/25"
                    >
                      ✅ Sauvegarder la configuration
                    </motion.button>
                  </div>
                </div>
              </div>
            )}

            {/* History Tab */}
            {activeTab === 'history' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50">
                  <h3 className="text-xl font-semibold text-white mb-4">
                    📜 Historique d'activité
                    {selectedUser && (
                      <span className="text-indigo-400 ml-2">
                        - {trackedUsers.find(u => u.user_id === selectedUser)?.username}
                      </span>
                    )}
                  </h3>
                  
                  {userHistory.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">📜</div>
                      <div className="text-gray-400 text-xl">
                        {selectedUser ? 'Aucun historique pour cet utilisateur' : 'Sélectionnez un utilisateur pour voir son historique'}
                      </div>
                      <div className="text-gray-500 text-sm mt-2">
                        Les événements apparaîtront ici une fois la surveillance activée
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      <AnimatePresence>
                        {userHistory.map((event, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="bg-gray-700/50 rounded-lg p-4 border border-gray-600/30"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                <span className="text-lg">📊</span>
                                <span className="text-white font-medium">{event.event_type}</span>
                              </div>
                              <span className="text-gray-400 text-sm">
                                {new Date(event.timestamp).toLocaleString('fr-FR')}
                              </span>
                            </div>
                            {event.guild_id && (
                              <div className="text-gray-400 text-sm mb-2">
                                🏠 Serveur: <code className="bg-gray-600 px-1 rounded text-xs">{event.guild_id}</code>
                              </div>
                            )}
                            <div className="text-gray-300 text-sm bg-gray-800/50 p-2 rounded font-mono">
                              {JSON.stringify(event.data, null, 2)}
                            </div>
                          </motion.div>
                        ))}
                      </AnimatePresence>
                    </div>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}