import { useState } from 'react';
import { motion } from 'framer-motion';
import { Globe, ShieldCheck, AlertTriangle, ExternalLink } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { useAppStore } from '../../stores/appStore';

export function WebLogin() {
  const { connectBot, botStatus } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginStep, setLoginStep] = useState<'idle' | 'webview' | 'extracting' | 'connected'>('idle');

  const handleWebLogin = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setLoginStep('webview');

      // Écouter les événements de capture de token
      const unlisten = await listen<{ token: string }>('discord-token-captured', async (event) => {
        setLoginStep('extracting');
        
        try {
          // Connecter avec le token capturé
          setLoginStep('connected');
          await connectBot(event.payload.token);
          
          setIsLoading(false);
          unlisten();
        } catch (connectError) {
          setError(connectError instanceof Error ? connectError.message : 'Erreur de connexion');
          setIsLoading(false);
          setLoginStep('idle');
          unlisten();
        }
      });

      // Ouvrir la webview Discord via Tauri
      await invoke('open_discord_login_webview');

      // Timeout après 5 minutes
      setTimeout(() => {
        if (isLoading) {
          setError('Délai d\'attente dépassé. Veuillez réessayer.');
          setIsLoading(false);
          setLoginStep('idle');
          unlisten();
        }
      }, 5 * 60 * 1000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la connexion');
      setIsLoading(false);
      setLoginStep('idle');
    }
  };

  const getStepText = () => {
    switch (loginStep) {
      case 'webview':
        return 'Connectez-vous à Discord...';
      case 'extracting':
        return 'Extraction du token...';
      case 'connected':
        return 'Connexion réussie !';
      default:
        return '';
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Connexion Web Discord</h1>
        <p className="page-subtitle">
          Connectez-vous avec la page officielle Discord, sans récupérer manuellement votre token
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Section connexion */}
        <motion.div 
          className="card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="flex items-center gap-3 mb-6">
            <Globe className="w-6 h-6 text-voidbot-primary" />
            <h3 className="text-xl font-semibold">Connexion sécurisée</h3>
          </div>

          <div className="space-y-4 mb-6">
            <div className="flex items-start gap-3">
              <ShieldCheck className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-semibold mb-1">Page officielle Discord</h4>
                <p className="text-sm text-voidbot-secondary">
                  Utilise la page de connexion officielle de Discord
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <ShieldCheck className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-semibold mb-1">Support 2FA</h4>
                <p className="text-sm text-voidbot-secondary">
                  Compatible avec l'authentification à deux facteurs
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <ShieldCheck className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-semibold mb-1">Extraction automatique</h4>
                <p className="text-sm text-voidbot-secondary">
                  Le token est extrait automatiquement après connexion
                </p>
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-sm text-red-400">{error}</p>
            </div>
          )}

          {loginStep !== 'idle' && (
            <div className="mb-4 p-3 bg-voidbot-primary/10 border border-voidbot-primary/20 rounded-lg">
              <p className="text-sm text-voidbot-primary">{getStepText()}</p>
            </div>
          )}

          <button
            className="btn btn-primary w-full"
            onClick={handleWebLogin}
            disabled={isLoading || botStatus === 'online'}
          >
            {isLoading ? (
              <span className="flex items-center gap-2">
                <span className="animate-spin">⏳</span>
                {getStepText()}
              </span>
            ) : botStatus === 'online' ? (
              'Déjà connecté'
            ) : (
              'Se connecter via Discord'
            )}
          </button>

          <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
              <p className="text-xs text-yellow-400">
                Une fenêtre Discord s'ouvrira. Connectez-vous normalement et VoidBot 
                récupérera automatiquement votre token de façon sécurisée.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Section informations */}
        <motion.div 
          className="card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center gap-3 mb-6">
            <ExternalLink className="w-6 h-6 text-voidbot-secondary" />
            <h3 className="text-xl font-semibold">Comment ça fonctionne</h3>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">1. Ouverture de Discord</h4>
              <p className="text-sm text-voidbot-secondary">
                Une fenêtre s'ouvre avec la page de connexion officielle Discord
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-2">2. Connexion normale</h4>
              <p className="text-sm text-voidbot-secondary">
                Connectez-vous avec vos identifiants habituels (email/mot de passe)
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-2">3. Validation 2FA</h4>
              <p className="text-sm text-voidbot-secondary">
                Si activé, entrez votre code 2FA comme d'habitude
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-2">4. Extraction automatique</h4>
              <p className="text-sm text-voidbot-secondary">
                VoidBot détecte la connexion et extrait automatiquement le token
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-2">5. Connexion établie</h4>
              <p className="text-sm text-voidbot-secondary">
                La fenêtre se ferme et VoidBot est connecté à votre compte
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-voidbot-primary/10 rounded-lg border border-voidbot-primary/20">
            <h4 className="font-semibold mb-2 text-voidbot-primary">💡 Avantages</h4>
            <ul className="text-sm text-voidbot-secondary space-y-1">
              <li>• Pas besoin de connaissances techniques</li>
              <li>• Support complet du 2FA</li>
              <li>• Plus sûr que la récupération manuelle</li>
              <li>• Processus familier pour tous</li>
            </ul>
          </div>
        </motion.div>
      </div>
    </div>
  );
}