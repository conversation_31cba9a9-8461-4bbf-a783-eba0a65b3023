import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-hot-toast';
import { listen } from '@tauri-apps/api/event';

interface RateLimitConfig {
  enabled: boolean;
  global_rate_per_second: number;
  min_delay_between_requests: number;
  debug_mode: boolean;
  warning_threshold: number;
  backoff_multiplier: number;
  max_backoff_seconds: number;
}

interface RateLimitStats {
  total_requests: number;
  blocked_requests: number;
  rate_limit_hits: number;
  queued_requests: number;
  avg_wait_time_ms: number;
  active_buckets: BucketInfo[];
}

interface BucketInfo {
  name: string;
  limit: number;
  remaining: number;
  reset_in_seconds: number | null;
}

export default function RateLimiter() {
  const [config, setConfig] = useState<RateLimitConfig>({
    enabled: true,
    global_rate_per_second: 50,
    min_delay_between_requests: 100,
    debug_mode: false,
    warning_threshold: 0.8,
    backoff_multiplier: 2.0,
    max_backoff_seconds: 300,
  });

  const [stats, setStats] = useState<RateLimitStats>({
    total_requests: 0,
    blocked_requests: 0,
    rate_limit_hits: 0,
    queued_requests: 0,
    avg_wait_time_ms: 0,
    active_buckets: [],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadConfig();
    loadStats();

    // Écouter les événements
    const unlistenConfig = listen('rate-limit-config-updated', (event) => {
      setConfig(event.payload as RateLimitConfig);
      toast.success('Configuration rate limiter mise à jour');
    });

    const unlistenToggle = listen('rate-limiter-toggled', (event) => {
      const enabled = event.payload as boolean;
      setConfig(prev => ({ ...prev, enabled }));
      toast.success(enabled ? 'Rate limiter activé' : 'Rate limiter désactivé');
    });

    const unlistenBucketReset = listen('rate-limit-bucket-reset', (event) => {
      const endpoint = event.payload as string;
      toast.success(`Bucket ${endpoint} réinitialisé`);
      loadStats();
    });

    const unlistenAllReset = listen('rate-limit-all-buckets-reset', () => {
      toast.success('Tous les buckets réinitialisés');
      loadStats();
    });

    // Auto-refresh des stats
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadStats();
      }
    }, 2000); // Toutes les 2 secondes

    return () => {
      unlistenConfig.then(fn => fn());
      unlistenToggle.then(fn => fn());
      unlistenBucketReset.then(fn => fn());
      unlistenAllReset.then(fn => fn());
      clearInterval(interval);
    };
  }, [autoRefresh]);

  const loadConfig = async () => {
    try {
      const rateConfig = await invoke<RateLimitConfig>('get_rate_limit_config');
      setConfig(rateConfig);
    } catch (error) {
      console.error('Erreur lors du chargement de la config:', error);
      toast.error('Impossible de charger la configuration du rate limiter');
    }
  };

  const loadStats = async () => {
    try {
      const rateStats = await invoke<RateLimitStats>('get_rate_limit_stats');
      setStats(rateStats);
    } catch (error) {
      console.error('Erreur lors du chargement des stats:', error);
    }
  };

  const updateConfig = async (updates: Partial<RateLimitConfig>) => {
    setIsLoading(true);
    try {
      await invoke('update_rate_limit_config', {
        enabled: updates.enabled,
        globalRatePerSecond: updates.global_rate_per_second,
        minDelayBetweenRequests: updates.min_delay_between_requests,
        warningThreshold: updates.warning_threshold,
        debugMode: updates.debug_mode,
      });
      
      setConfig(prev => ({ ...prev, ...updates }));
      toast.success('Configuration mise à jour');
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('Erreur lors de la mise à jour de la configuration');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleRateLimiter = async () => {
    try {
      const newEnabled = !config.enabled;
      await invoke('toggle_rate_limiter', { enabled: newEnabled });
    } catch (error) {
      console.error('Erreur lors du toggle:', error);
      toast.error('Erreur lors du changement de statut');
    }
  };

  const resetBucket = async (endpoint: string) => {
    try {
      await invoke('reset_rate_limit_bucket', { endpoint });
    } catch (error) {
      console.error('Erreur lors du reset bucket:', error);
      toast.error('Erreur lors de la réinitialisation du bucket');
    }
  };

  const resetAllBuckets = async () => {
    try {
      await invoke('reset_all_rate_limit_buckets');
    } catch (error) {
      console.error('Erreur lors du reset all:', error);
      toast.error('Erreur lors de la réinitialisation des buckets');
    }
  };

  const getHealthColor = (percentage: number) => {
    if (percentage >= 80) return 'text-red-400';
    if (percentage >= 60) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getHealthBgColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-red-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Rate Limiter</h1>
          <p className="text-gray-400 mt-1">Protection contre les limites API Discord</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Auto-refresh:</span>
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                autoRefresh ? 'bg-indigo-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  autoRefresh ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Activé:</span>
            <button
              onClick={toggleRateLimiter}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-indigo-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Alert si désactivé */}
      {!config.enabled && (
        <div className="bg-yellow-900/20 border border-yellow-600 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-500">⚠️</span>
            <p className="text-yellow-400">
              Le rate limiter est désactivé. Votre bot risque d'être banni par Discord.
            </p>
          </div>
        </div>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-400">Requêtes totales</h3>
            <span className="text-xl font-bold text-white">{stats.total_requests}</span>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-400">Requêtes bloquées</h3>
            <span className={`text-xl font-bold ${stats.blocked_requests > 0 ? 'text-red-400' : 'text-white'}`}>
              {stats.blocked_requests}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-400">429 reçus</h3>
            <span className={`text-xl font-bold ${stats.rate_limit_hits > 0 ? 'text-red-400' : 'text-white'}`}>
              {stats.rate_limit_hits}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-400">Temps d'attente moy.</h3>
            <span className="text-xl font-bold text-white">{stats.avg_wait_time_ms}ms</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration */}
        <div className="lg:col-span-1 space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Configuration</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Taux global (req/sec)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={config.global_rate_per_second}
                  onChange={(e) => updateConfig({ global_rate_per_second: parseInt(e.target.value) })}
                  disabled={isLoading}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Délai minimum (ms)
                </label>
                <input
                  type="number"
                  min="50"
                  max="5000"
                  value={config.min_delay_between_requests}
                  onChange={(e) => updateConfig({ min_delay_between_requests: parseInt(e.target.value) })}
                  disabled={isLoading}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Seuil d'alerte (%)
                </label>
                <input
                  type="number"
                  min="50"
                  max="95"
                  step="5"
                  value={Math.round(config.warning_threshold * 100)}
                  onChange={(e) => updateConfig({ warning_threshold: parseInt(e.target.value) / 100 })}
                  disabled={isLoading}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="debug-mode"
                  checked={config.debug_mode}
                  onChange={(e) => updateConfig({ debug_mode: e.target.checked })}
                  disabled={isLoading}
                  className="h-4 w-4 rounded border-gray-600 bg-gray-700 text-indigo-600"
                />
                <label htmlFor="debug-mode" className="text-sm text-gray-300">
                  Mode debug (logs détaillés)
                </label>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Actions</h3>
            <div className="space-y-3">
              <button
                onClick={resetAllBuckets}
                className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Réinitialiser tous les buckets
              </button>
              
              <button
                onClick={loadStats}
                className="w-full px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                Actualiser les stats
              </button>
            </div>
          </div>
        </div>

        {/* Active Buckets */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Buckets actifs</h3>
              <span className="text-sm text-gray-400">
                {stats.active_buckets.length} endpoint{stats.active_buckets.length > 1 ? 's' : ''}
              </span>
            </div>
            
            {stats.active_buckets.length === 0 ? (
              <p className="text-gray-400 text-center py-8">Aucun bucket actif</p>
            ) : (
              <div className="space-y-3">
                {stats.active_buckets.map((bucket) => {
                  const usagePercentage = ((bucket.limit - bucket.remaining) / bucket.limit) * 100;
                  
                  return (
                    <div key={bucket.name} className="bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="text-sm font-medium text-white">{bucket.name}</h4>
                          <p className="text-xs text-gray-400">
                            {bucket.remaining}/{bucket.limit} restant
                            {bucket.reset_in_seconds && (
                              <span> • Reset dans {bucket.reset_in_seconds}s</span>
                            )}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm font-medium ${getHealthColor(usagePercentage)}`}>
                            {Math.round(usagePercentage)}%
                          </span>
                          
                          <button
                            onClick={() => resetBucket(bucket.name)}
                            className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
                          >
                            Reset
                          </button>
                        </div>
                      </div>
                      
                      {/* Progress bar */}
                      <div className="w-full bg-gray-600 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all ${getHealthBgColor(usagePercentage)}`}
                          style={{ width: `${usagePercentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Alertes */}
          {stats.rate_limit_hits > 0 && (
            <div className="mt-6 bg-red-900/20 border border-red-600 rounded-lg p-4">
              <h4 className="text-red-400 font-semibold mb-2">⚠️ Alertes Rate Limit</h4>
              <ul className="space-y-1 text-sm text-red-300">
                <li>• {stats.rate_limit_hits} erreurs 429 reçues</li>
                <li>• {stats.blocked_requests} requêtes bloquées</li>
                <li>• Considérez de réduire le taux de requêtes</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}