---
interface NavItem {
  name: string;
  href: string;
  external?: boolean;
}

const navItems: NavItem[] = [
  { name: "Accueil", href: "/" },
  { name: "Fonctionnalités", href: "/features" },
  { name: "Télécharger", href: "/download" },
  { name: "Documentation", href: "/docs" },
  { name: "Support", href: "/support" },
];
---

<nav class="fixed top-0 w-full z-50 bg-gray-950/80 backdrop-blur-md border-b border-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-3 group">
          <div class="relative">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <span class="text-white font-bold text-lg">V</span>
            </div>
            <div class="absolute -inset-1 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 rounded-lg blur-md group-hover:blur-lg transition-all duration-200"></div>
          </div>
          <div class="flex flex-col">
            <span class="font-bold text-xl gradient-text">VoidBot</span>
            <span class="text-xs text-gray-400 font-mono">v1.0.0</span>
          </div>
        </a>
      </div>

      <!-- Navigation Links -->
      <div class="hidden md:block">
        <div class="flex items-center space-x-8">
          {navItems.map((item) => (
            <a 
              href={item.href}
              class="nav-link"
              target={item.external ? "_blank" : undefined}
              rel={item.external ? "noopener noreferrer" : undefined}
            >
              {item.name}
            </a>
          ))}
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="hidden md:flex items-center space-x-4">
        <a href="/download" class="btn btn-secondary">
          Télécharger
        </a>
        <a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="btn btn-primary">
          GitHub
        </a>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button 
          type="button" 
          class="text-gray-400 hover:text-white focus:outline-none focus:text-white"
          onclick="toggleMobileMenu()"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile menu -->
  <div id="mobile-menu" class="md:hidden hidden">
    <div class="px-2 pt-2 pb-3 space-y-1 bg-gray-900/95 backdrop-blur-md border-t border-gray-800">
      {navItems.map((item) => (
        <a 
          href={item.href}
          class="block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-200"
          target={item.external ? "_blank" : undefined}
          rel={item.external ? "noopener noreferrer" : undefined}
        >
          {item.name}
        </a>
      ))}
      <div class="flex flex-col space-y-2 px-3 pt-4 border-t border-gray-800 mt-4">
        <a href="/download" class="btn btn-secondary w-full text-center">
          Télécharger
        </a>
        <a href="#" onclick="alert('Repository GitHub sera bientôt disponible')" class="btn btn-primary w-full text-center">
          GitHub
        </a>
      </div>
    </div>
  </div>
</nav>

<script>
  function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    if (menu) {
      menu.classList.toggle('hidden');
    }
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', (event) => {
    const menu = document.getElementById('mobile-menu');
    const menuButton = document.querySelector('[onclick="toggleMobileMenu()"]');
    
    if (menu && !menu.contains(event.target as Node) && !menuButton?.contains(event.target as Node)) {
      menu.classList.add('hidden');
    }
  });

  // Close mobile menu on window resize to desktop size
  window.addEventListener('resize', () => {
    if (window.innerWidth >= 768) {
      const menu = document.getElementById('mobile-menu');
      if (menu) {
        menu.classList.add('hidden');
      }
    }
  });
</script>

<style>
  /* Ensure the main content starts below the fixed navbar */
  body {
    padding-top: 4rem; /* 64px = h-16 */
  }
</style>