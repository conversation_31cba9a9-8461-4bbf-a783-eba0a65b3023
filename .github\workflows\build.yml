name: 🔨 Build VoidBot

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'voidbot-desktop/**'
      - 'shared/**'
      - '.github/workflows/build.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'voidbot-desktop/**'
      - 'shared/**'
      - '.github/workflows/build.yml'

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Job de test et lint
  test:
    name: Test & Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: voidbot-desktop/package-lock.json

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: |
            voidbot-desktop/src-tauri -> target

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libgtk-3-dev \
            libwebkit2gtk-4.0-dev \
            libappindicator3-dev \
            librsvg2-dev

      - name: Install Node.js dependencies
        run: |
          cd voidbot-desktop
          npm ci

      - name: Lint TypeScript
        run: |
          cd voidbot-desktop
          npm run lint

      - name: Type check TypeScript
        run: |
          cd voidbot-desktop
          npm run type-check

      - name: Format check Rust
        run: |
          cd voidbot-desktop/src-tauri
          cargo fmt --check

      - name: Lint Rust
        run: |
          cd voidbot-desktop/src-tauri
          cargo clippy -- -D warnings

      - name: Test Rust
        run: |
          cd voidbot-desktop/src-tauri
          cargo test

  # Job de build multi-plateforme
  build:
    name: Build ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            bundles: msi,nsis
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            bundles: deb,appimage
          - os: macos-latest
            target: universal-apple-darwin
            bundles: dmg

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: voidbot-desktop/package-lock.json

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install Linux dependencies
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libgtk-3-dev \
            libwebkit2gtk-4.0-dev \
            libappindicator3-dev \
            librsvg2-dev \
            patchelf

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: |
            voidbot-desktop/src-tauri -> target

      - name: Install Node.js dependencies
        run: |
          cd voidbot-desktop
          npm ci

      - name: Generate assets (Windows)
        if: matrix.os == 'windows-latest'
        run: |
          cd voidbot-desktop/src-tauri
          python build-assets-simple.py
        continue-on-error: true

      - name: Generate assets (Unix)
        if: matrix.os != 'windows-latest'
        run: |
          cd voidbot-desktop/src-tauri
          python3 build-assets-simple.py
        continue-on-error: true

      - name: Build frontend
        run: |
          cd voidbot-desktop
          npm run build:prod

      - name: Build Tauri app
        run: |
          cd voidbot-desktop
          npm run tauri build -- --target ${{ matrix.target }} --bundles ${{ matrix.bundles }}

      - name: Create build artifacts
        run: |
          mkdir -p build-artifacts
          cd voidbot-desktop/src-tauri/target/${{ matrix.target }}/release/bundle
          
          # Copier tous les installeurs
          if [ -d "msi" ]; then
            cp msi/*.msi ../../../../../build-artifacts/ || true
          fi
          if [ -d "nsis" ]; then
            cp nsis/*.exe ../../../../../build-artifacts/ || true
          fi
          if [ -d "deb" ]; then
            cp deb/*.deb ../../../../../build-artifacts/ || true
          fi
          if [ -d "appimage" ]; then
            cp appimage/*.AppImage ../../../../../build-artifacts/ || true
          fi
          if [ -d "dmg" ]; then
            cp dmg/*.dmg ../../../../../build-artifacts/ || true
          fi
        shell: bash

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: voidbot-${{ matrix.os }}-${{ github.sha }}
          path: build-artifacts/
          retention-days: 7
          if-no-files-found: warn

  # Job de résumé
  build-summary:
    name: Build Summary
    runs-on: ubuntu-latest
    needs: [test, build]
    if: always()
    steps:
      - name: Build summary
        run: |
          echo "## 🔨 Build Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.test.result }}" = "success" ]; then
            echo "✅ **Tests & Lint**: Passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Tests & Lint**: Failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Platform Builds" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.build.result }}" = "success" ]; then
            echo "✅ **Multi-platform builds**: All successful" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Multi-platform builds**: Some failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Artifacts" >> $GITHUB_STEP_SUMMARY
          echo "Build artifacts are available for download for 7 days." >> $GITHUB_STEP_SUMMARY