import { useState } from 'react';
import { useAppStore } from '../stores/appStore';

export function SimpleDashboard() {
  const { botStatus, stealthMode, stats } = useAppStore();

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-voidbot-primary mb-2">Dashboard</h1>
        <p className="text-voidbot-secondary">Vue d'ensemble de VoidBot</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="card text-center">
          <div className="text-2xl font-bold text-voidbot-primary">{stats.commandsUsed}</div>
          <div className="text-sm text-voidbot-secondary mt-1">Commandes utilisées</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-voidbot-primary">{stats.serversBackedUp}</div>
          <div className="text-sm text-voidbot-secondary mt-1">Serveurs sauvegardés</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-voidbot-primary">{stats.animationsActive}</div>
          <div className="text-sm text-voidbot-secondary mt-1">Animations actives</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-voidbot-primary">{Math.floor(stats.uptime / 60)}m</div>
          <div className="text-sm text-voidbot-secondary mt-1">Temps de fonctionnement</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h2 className="text-xl font-semibold mb-4 text-voidbot-primary">État du Bot</h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-voidbot-secondary">Statut</span>
              <span className={`px-3 py-1 rounded-full text-sm ${
                botStatus === 'online' ? 'bg-green-500/20 text-green-400' :
                botStatus === 'connecting' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-red-500/20 text-red-400'
              }`}>
                {botStatus === 'online' ? 'En ligne' :
                 botStatus === 'connecting' ? 'Connexion...' :
                 'Hors ligne'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-voidbot-secondary">Mode</span>
              <span className={`px-3 py-1 rounded-full text-sm ${
                stealthMode === 'ghost' ? 'bg-purple-500/20 text-purple-400' :
                'bg-blue-500/20 text-blue-400'
              }`}>
                {stealthMode === 'ghost' ? 'Fantôme' : 'Normal'}
              </span>
            </div>
          </div>
        </div>

        <div className="card">
          <h2 className="text-xl font-semibold mb-4 text-voidbot-primary">Actions Rapides</h2>
          <div className="space-y-3">
            <button className="btn btn-primary w-full">
              🔗 Connexion Discord
            </button>
            <button className="btn btn-secondary w-full">
              👻 Changer de Mode
            </button>
            <button className="btn btn-ghost w-full">
              🔔 Voir Notifications
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export function SimpleStealthMode() {
  const { stealthMode, setStealthMode } = useAppStore();

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-voidbot-primary mb-2">Mode Furtif</h1>
        <p className="text-voidbot-secondary">Contrôlez la visibilité de vos réponses Discord</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div 
          className={`mode-card ${stealthMode === 'normal' ? 'active' : 'inactive'}`}
          onClick={() => setStealthMode('normal')}
        >
          <div className="text-center">
            <div className="text-4xl mb-3">👤</div>
            <h3 className="text-xl font-semibold mb-2 text-voidbot-primary">Mode Normal</h3>
            <p className="text-voidbot-secondary mb-4">
              Les réponses du bot sont visibles par tous les membres du serveur.
            </p>
            <div className="flex justify-center">
              {stealthMode === 'normal' && (
                <span className="bg-voidbot-primary text-white px-3 py-1 rounded-full text-sm">
                  Actif
                </span>
              )}
            </div>
          </div>
        </div>

        <div 
          className={`mode-card ${stealthMode === 'ghost' ? 'active' : 'inactive'}`}
          onClick={() => setStealthMode('ghost')}
        >
          <div className="text-center">
            <div className="text-4xl mb-3">👻</div>
            <h3 className="text-xl font-semibold mb-2 text-voidbot-primary">Mode Fantôme</h3>
            <p className="text-voidbot-secondary mb-4">
              Les réponses du bot ne sont visibles que par vous (ephemeral).
            </p>
            <div className="flex justify-center">
              {stealthMode === 'ghost' && (
                <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">
                  Actif
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 card">
        <h2 className="text-xl font-semibold mb-4 text-voidbot-primary">💡 Conseils d'utilisation</h2>
        <div className="space-y-3 text-voidbot-secondary">
          <div className="flex items-start gap-3">
            <span className="text-green-400">•</span>
            <span>Le mode Normal est parfait pour les serveurs publics et les interactions sociales</span>
          </div>
          <div className="flex items-start gap-3">
            <span className="text-purple-400">•</span>
            <span>Le mode Fantôme est idéal pour les tests et l'utilisation discrète</span>
          </div>
          <div className="flex items-start gap-3">
            <span className="text-blue-400">•</span>
            <span>Certaines commandes sensibles utilisent automatiquement le mode fantôme</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export function SimpleNotifications() {
  const [notifications] = useState([
    { id: '1', title: 'Ghostping détecté', message: 'Message supprimé avec mention dans #général', time: '2m', type: 'warning' },
    { id: '2', title: 'Bot connecté', message: 'Connexion réussie au serveur Discord', time: '5m', type: 'success' },
    { id: '3', title: 'Commande exécutée', message: '/animate démarré avec succès', time: '10m', type: 'info' },
  ]);

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-voidbot-primary mb-2">Centre de Notifications</h1>
        <p className="text-voidbot-secondary">Surveillez tous les événements importants</p>
      </div>

      <div className="space-y-4">
        {notifications.map((notif) => (
          <div key={notif.id} className="card card-hover">
            <div className="flex items-start gap-4">
              <div className={`w-3 h-3 rounded-full mt-2 ${
                notif.type === 'success' ? 'bg-green-400' :
                notif.type === 'warning' ? 'bg-yellow-400' :
                'bg-blue-400'
              }`} />
              <div className="flex-1">
                <div className="flex justify-between items-start mb-1">
                  <h3 className="font-semibold text-voidbot-primary">{notif.title}</h3>
                  <span className="text-sm text-voidbot-secondary">{notif.time}</span>
                </div>
                <p className="text-voidbot-secondary">{notif.message}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 card">
        <h2 className="text-xl font-semibold mb-4 text-voidbot-primary">⚙️ Configuration</h2>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-voidbot-secondary">Notifications desktop</span>
            <button className="toggle enabled">
              <span className="toggle-thumb"></span>
            </button>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-voidbot-secondary">Sons de notification</span>
            <button className="toggle enabled">
              <span className="toggle-thumb"></span>
            </button>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-voidbot-secondary">Détection de ghostping</span>
            <button className="toggle enabled">
              <span className="toggle-thumb"></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export function SimplePlaceholder({ title, subtitle }: { title: string; subtitle: string }) {
  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-voidbot-primary mb-2">{title}</h1>
        <p className="text-voidbot-secondary">{subtitle}</p>
      </div>
      <div className="card text-center py-12">
        <div className="text-6xl mb-4">🚧</div>
        <h2 className="text-xl font-semibold mb-2 text-voidbot-primary">En cours de développement</h2>
        <p className="text-voidbot-secondary">Cette fonctionnalité sera bientôt disponible.</p>
      </div>
    </div>
  );
}