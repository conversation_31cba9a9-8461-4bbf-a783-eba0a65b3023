use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc, Local};
use async_trait::async_trait;

/// Gestionnaire de variables dynamiques pour Rich Presence et autres
pub struct DynamicVariableManager {
    pub config: DynamicVariableConfig,
    // Note: providers ne peut pas être Debug ou Clone car il contient des trait objects
    pub providers: Vec<Box<dyn VariableProvider + Send + Sync>>,
    pub cache: HashMap<String, CachedVariable>,
    pub custom_variables: HashMap<String, String>,
}

impl std::fmt::Debug for DynamicVariableManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DynamicVariableManager")
            .field("config", &self.config)
            .field("providers", &format!("[{} providers]", self.providers.len()))
            .field("cache", &self.cache)
            .field("custom_variables", &self.custom_variables)
            .finish()
    }
}

/// Configuration des variables dynamiques
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DynamicVariableConfig {
    pub enabled: bool,
    pub cache_duration_seconds: u64,
    pub max_custom_variables: usize,
    pub auto_refresh_interval_seconds: u64,
    pub enabled_providers: Vec<String>,
}

impl Default for DynamicVariableConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            cache_duration_seconds: 10, // Cache 10 secondes pour variables dynamiques
            max_custom_variables: 50,
            auto_refresh_interval_seconds: 5,
            enabled_providers: vec![
                "time".to_string(),
                "discord".to_string(),
                "system".to_string(),
                "spotify".to_string(),
            ],
        }
    }
}

/// Variable mise en cache
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedVariable {
    pub name: String,
    pub value: String,
    pub cached_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub provider: String,
}

/// Trait pour les fournisseurs de variables
#[async_trait]
pub trait VariableProvider {
    fn name(&self) -> &str;
    fn get_variable_names(&self) -> Vec<String>;
    async fn get_variable(&self, name: &str) -> Result<String, VariableError>;
    async fn refresh_all(&self) -> Result<HashMap<String, String>, VariableError>;
}

/// Erreurs de variables dynamiques
#[derive(Debug, thiserror::Error)]
pub enum VariableError {
    #[error("Variable non trouvée: {0}")]
    NotFound(String),
    
    #[error("Fournisseur non disponible: {0}")]
    ProviderUnavailable(String),
    
    #[error("Erreur de récupération: {0}")]
    FetchError(String),
    
    #[error("Variable expirée: {0}")]
    Expired(String),
    
    #[error("Limite de variables atteinte")]
    LimitReached,
}

impl DynamicVariableManager {
    pub fn new() -> Self {
        let mut manager = Self {
            config: DynamicVariableConfig::default(),
            providers: Vec::new(),
            cache: HashMap::new(),
            custom_variables: HashMap::new(),
        };
        
        // Ajouter les fournisseurs par défaut
        manager.providers.push(Box::new(TimeVariableProvider::new()));
        manager.providers.push(Box::new(DiscordVariableProvider::new()));
        manager.providers.push(Box::new(SystemVariableProvider::new()));
        manager.providers.push(Box::new(SpotifyVariableProvider::new()));
        
        manager
    }
    
    /// Résoudre une variable (avec syntaxe {variable_name})
    pub async fn resolve_variable(&mut self, name: &str) -> Result<String, VariableError> {
        if !self.config.enabled {
            return Ok(format!("{{{}}}", name)); // Retourner tel quel si désactivé
        }
        
        // Vérifier d'abord les variables personnalisées
        if let Some(value) = self.custom_variables.get(name) {
            return Ok(value.clone());
        }
        
        // Vérifier le cache
        if let Some(cached) = self.cache.get(name) {
            if cached.expires_at > Utc::now() {
                return Ok(cached.value.clone());
            } else {
                // Variable expirée, la supprimer du cache
                self.cache.remove(name);
            }
        }
        
        // Chercher dans les fournisseurs
        for provider in &self.providers {
            if !self.config.enabled_providers.contains(&provider.name().to_string()) {
                continue;
            }
            
            if provider.get_variable_names().contains(&name.to_string()) {
                match provider.get_variable(name).await {
                    Ok(value) => {
                        // Mettre en cache
                        let cached = CachedVariable {
                            name: name.to_string(),
                            value: value.clone(),
                            cached_at: Utc::now(),
                            expires_at: Utc::now() + chrono::Duration::seconds(self.config.cache_duration_seconds as i64),
                            provider: provider.name().to_string(),
                        };
                        self.cache.insert(name.to_string(), cached);
                        return Ok(value);
                    },
                    Err(_e) => {
                        // Continuer avec le prochain fournisseur
                        continue;
                    }
                }
            }
        }
        
        Err(VariableError::NotFound(name.to_string()))
    }
    
    /// Résoudre un texte avec des variables (format {variable})
    pub async fn resolve_text(&mut self, text: &str) -> String {
        let mut result = text.to_string();
        
        // Regex pour trouver {variable_name}
        let re = regex::Regex::new(r"\{([^}]+)\}").unwrap();
        
        // Collecter toutes les variables à résoudre
        let mut variables_to_resolve = Vec::new();
        for caps in re.captures_iter(text) {
            if let Some(var_name) = caps.get(1) {
                variables_to_resolve.push(var_name.as_str().to_string());
            }
        }
        
        // Résoudre chaque variable
        for var_name in variables_to_resolve {
            match self.resolve_variable(&var_name).await {
                Ok(value) => {
                    result = result.replace(&format!("{{{}}}", var_name), &value);
                },
                Err(_) => {
                    // Garder la variable tel quel si erreur
                    // Ou la remplacer par une valeur par défaut
                    result = result.replace(&format!("{{{}}}", var_name), "?");
                }
            }
        }
        
        result
    }
    
    /// Ajouter une variable personnalisée
    pub fn add_custom_variable(&mut self, name: String, value: String) -> Result<(), VariableError> {
        if self.custom_variables.len() >= self.config.max_custom_variables {
            return Err(VariableError::LimitReached);
        }
        
        self.custom_variables.insert(name, value);
        Ok(())
    }
    
    /// Supprimer une variable personnalisée
    pub fn remove_custom_variable(&mut self, name: &str) -> Option<String> {
        self.custom_variables.remove(name)
    }
    
    /// Obtenir toutes les variables disponibles
    pub fn get_available_variables(&self) -> Vec<String> {
        let mut variables = Vec::new();
        
        // Variables personnalisées
        variables.extend(self.custom_variables.keys().cloned());
        
        // Variables des fournisseurs
        for provider in &self.providers {
            if self.config.enabled_providers.contains(&provider.name().to_string()) {
                variables.extend(provider.get_variable_names());
            }
        }
        
        variables.sort();
        variables.dedup();
        variables
    }
    
    /// Nettoyer le cache expiré
    pub fn cleanup_cache(&mut self) {
        let now = Utc::now();
        self.cache.retain(|_, cached| cached.expires_at > now);
    }
    
    /// Actualiser toutes les variables
    pub async fn refresh_all(&mut self) -> Result<(), VariableError> {
        self.cache.clear();
        
        for provider in &self.providers {
            if !self.config.enabled_providers.contains(&provider.name().to_string()) {
                continue;
            }
            
            if let Ok(variables) = provider.refresh_all().await {
                for (name, value) in variables {
                    let cached = CachedVariable {
                        name: name.clone(),
                        value,
                        cached_at: Utc::now(),
                        expires_at: Utc::now() + chrono::Duration::seconds(self.config.cache_duration_seconds as i64),
                        provider: provider.name().to_string(),
                    };
                    self.cache.insert(name, cached);
                }
            }
        }
        
        Ok(())
    }
}

/// Fournisseur de variables temporelles
#[derive(Debug, Clone)]
pub struct TimeVariableProvider;

impl TimeVariableProvider {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl VariableProvider for TimeVariableProvider {
    fn name(&self) -> &str {
        "time"
    }
    
    fn get_variable_names(&self) -> Vec<String> {
        vec![
            "local_time".to_string(),
            "local_date".to_string(),
            "utc_time".to_string(),
            "utc_date".to_string(),
            "unix_timestamp".to_string(),
            "hour".to_string(),
            "minute".to_string(),
            "second".to_string(),
            "day".to_string(),
            "month".to_string(),
            "year".to_string(),
            "weekday".to_string(),
        ]
    }
    
    async fn get_variable(&self, name: &str) -> Result<String, VariableError> {
        let now_local = Local::now();
        let now_utc = Utc::now();
        
        match name {
            "local_time" => Ok(now_local.format("%H:%M:%S").to_string()),
            "local_date" => Ok(now_local.format("%Y-%m-%d").to_string()),
            "utc_time" => Ok(now_utc.format("%H:%M:%S").to_string()),
            "utc_date" => Ok(now_utc.format("%Y-%m-%d").to_string()),
            "unix_timestamp" => Ok(now_utc.timestamp().to_string()),
            "hour" => Ok(now_local.format("%H").to_string()),
            "minute" => Ok(now_local.format("%M").to_string()),
            "second" => Ok(now_local.format("%S").to_string()),
            "day" => Ok(now_local.format("%d").to_string()),
            "month" => Ok(now_local.format("%m").to_string()),
            "year" => Ok(now_local.format("%Y").to_string()),
            "weekday" => Ok(now_local.format("%A").to_string()),
            _ => Err(VariableError::NotFound(name.to_string())),
        }
    }
    
    async fn refresh_all(&self) -> Result<HashMap<String, String>, VariableError> {
        let mut variables = HashMap::new();
        
        for var_name in self.get_variable_names() {
            if let Ok(value) = self.get_variable(&var_name).await {
                variables.insert(var_name, value);
            }
        }
        
        Ok(variables)
    }
}

/// Fournisseur de variables Discord
#[derive(Debug, Clone)]
pub struct DiscordVariableProvider {
    pub server_count: u64,
    pub friend_count: u64,
    pub username: String,
    pub user_id: String,
}

impl DiscordVariableProvider {
    pub fn new() -> Self {
        Self {
            server_count: 0,
            friend_count: 0,
            username: "Unknown".to_string(),
            user_id: "0".to_string(),
        }
    }
    
    pub fn update_stats(&mut self, server_count: u64, friend_count: u64, username: String, user_id: String) {
        self.server_count = server_count;
        self.friend_count = friend_count;
        self.username = username;
        self.user_id = user_id;
    }
}

#[async_trait]
impl VariableProvider for DiscordVariableProvider {
    fn name(&self) -> &str {
        "discord"
    }
    
    fn get_variable_names(&self) -> Vec<String> {
        vec![
            "server_count".to_string(),
            "friend_count".to_string(),
            "username".to_string(),
            "user_id".to_string(),
            "discriminator".to_string(),
        ]
    }
    
    async fn get_variable(&self, name: &str) -> Result<String, VariableError> {
        match name {
            "server_count" => Ok(self.server_count.to_string()),
            "friend_count" => Ok(self.friend_count.to_string()),
            "username" => Ok(self.username.clone()),
            "user_id" => Ok(self.user_id.clone()),
            "discriminator" => Ok("0000".to_string()), // Discord a supprimé les discriminators
            _ => Err(VariableError::NotFound(name.to_string())),
        }
    }
    
    async fn refresh_all(&self) -> Result<HashMap<String, String>, VariableError> {
        let mut variables = HashMap::new();
        
        for var_name in self.get_variable_names() {
            if let Ok(value) = self.get_variable(&var_name).await {
                variables.insert(var_name, value);
            }
        }
        
        Ok(variables)
    }
}

/// Fournisseur de variables système
#[derive(Debug, Clone)]
pub struct SystemVariableProvider;

impl SystemVariableProvider {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl VariableProvider for SystemVariableProvider {
    fn name(&self) -> &str {
        "system"
    }
    
    fn get_variable_names(&self) -> Vec<String> {
        vec![
            "os".to_string(),
            "hostname".to_string(),
            "uptime".to_string(),
            "active_app".to_string(),
        ]
    }
    
    async fn get_variable(&self, name: &str) -> Result<String, VariableError> {
        match name {
            "os" => Ok(std::env::consts::OS.to_string()),
            "hostname" => {
                // Essayer d'obtenir le hostname
                if let Ok(hostname) = hostname::get() {
                    Ok(hostname.to_string_lossy().to_string())
                } else {
                    Ok("Unknown".to_string())
                }
            },
            "uptime" => {
                // Note: Uptime système nécessiterait une lib comme sysinfo
                Ok("N/A".to_string())
            },
            "active_app" => {
                // Note: Détecter l'app active nécessiterait des APIs spécifiques à l'OS
                Ok("VoidBot".to_string())
            },
            _ => Err(VariableError::NotFound(name.to_string())),
        }
    }
    
    async fn refresh_all(&self) -> Result<HashMap<String, String>, VariableError> {
        let mut variables = HashMap::new();
        
        for var_name in self.get_variable_names() {
            if let Ok(value) = self.get_variable(&var_name).await {
                variables.insert(var_name, value);
            }
        }
        
        Ok(variables)
    }
}

/// Fournisseur de variables Spotify
#[derive(Debug, Clone)]
pub struct SpotifyVariableProvider {
    pub current_track: Option<String>,
    pub current_artist: Option<String>,
    pub current_album: Option<String>,
    pub is_playing: bool,
    pub lyrics: Option<String>,
}

impl SpotifyVariableProvider {
    pub fn new() -> Self {
        Self {
            current_track: None,
            current_artist: None,
            current_album: None,
            is_playing: false,
            lyrics: None,
        }
    }
    
    pub fn update_spotify_data(
        &mut self, 
        track: Option<String>, 
        artist: Option<String>, 
        album: Option<String>, 
        is_playing: bool,
        lyrics: Option<String>
    ) {
        self.current_track = track;
        self.current_artist = artist;
        self.current_album = album;
        self.is_playing = is_playing;
        self.lyrics = lyrics;
    }
}

#[async_trait]
impl VariableProvider for SpotifyVariableProvider {
    fn name(&self) -> &str {
        "spotify"
    }
    
    fn get_variable_names(&self) -> Vec<String> {
        vec![
            "spotify_track".to_string(),
            "spotify_artist".to_string(),
            "spotify_album".to_string(),
            "spotify_lyrics".to_string(),
            "spotify_status".to_string(),
        ]
    }
    
    async fn get_variable(&self, name: &str) -> Result<String, VariableError> {
        match name {
            "spotify_track" => Ok(self.current_track.clone().unwrap_or("Not playing".to_string())),
            "spotify_artist" => Ok(self.current_artist.clone().unwrap_or("Unknown".to_string())),
            "spotify_album" => Ok(self.current_album.clone().unwrap_or("Unknown".to_string())),
            "spotify_lyrics" => Ok(self.lyrics.clone().unwrap_or("No lyrics".to_string())),
            "spotify_status" => Ok(if self.is_playing { "Playing".to_string() } else { "Paused".to_string() }),
            _ => Err(VariableError::NotFound(name.to_string())),
        }
    }
    
    async fn refresh_all(&self) -> Result<HashMap<String, String>, VariableError> {
        let mut variables = HashMap::new();
        
        for var_name in self.get_variable_names() {
            if let Ok(value) = self.get_variable(&var_name).await {
                variables.insert(var_name, value);
            }
        }
        
        Ok(variables)
    }
}